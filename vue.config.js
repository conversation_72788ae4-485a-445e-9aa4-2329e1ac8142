/* eslint-disable @typescript-eslint/no-var-requires */
const webpack = require('webpack');
// 去除console
const UglifyJsPlugin = require('terser-webpack-plugin');
const path = require('path');
const resolve = dir => path.join(__dirname, dir); // 路径

const IS_PROD = ['production', 'prod'].includes(process.env.NODE_ENV);

module.exports = {
  // publicPath: isDev ? '' : querystring.unescape('<%=request.getContextPath()%>'),
  publicPath: '',
  // filenameHashing: false,
  productionSourceMap: false,
  css: {
    requireModuleExtension: true, // 是否开启CSSmodule并保留xxx.module.css后缀
    loaderOptions: {
      less: {
        javascriptEnabled: true,
      },
      sass: {
        additionalData: `
        @use 'sass:math';
        @import "@/styles/global.scss";`,
      },
      postcss: {
        plugins: [
          require('postcss-px-to-viewport')({
            unitToConvert: 'px', //需要转换的单位，默认为"px"
            viewportWidth: 1708, // 视窗的宽度，对应的是我们设计稿的宽度，一般是750
            unitPrecision: 5, //单位转换后保留的精度
            viewportUnit: 'vw', // 希望使用的视口单位
            fontViewportUnit: 'vw', //字体使用的视口单位
            selectorBlackList: ['ig-', 'cxd-'], //需要忽略的CSS选择器，不会转为视口单位，使用原有的px等单位。
            minPixelValue: 1, //设置最小的转换数值，如果为1的话，只有大于1的值会被转换
            mediaQuery: true, //媒体查询里的单位是否需要转换单位
            replace: true, //是否直接更换属性值，而不添加备用属性
            // exclude: /(\/|\\)(node_modules)(\/|\\)/,		//忽略某些文件夹下的文件或特定文件，例如 'node_modules' 下的文件
            exclude: [/node_modules/, /styles/],
          }),
        ],
      },
    },
  },
  chainWebpack: config => {
    // 移除 preload 插件
    config.plugins.delete('preload');
    // 移除 prefetch 插件
    config.plugins.delete('prefetch');
    config
      .plugin('ignore')
      .use(
        new webpack.ContextReplacementPlugin(/moment[/\\]locale$/, /zh-cn$/)
      );
    config.when(!IS_PROD, configuration => configuration.devtool('cheap-source-map'));

    // 配置相关loader，支持修改，添加和替换相关的loader
    config.resolve.alias.set('@', resolve('src'));
    // 解决bn.js重复打包的问题
    config.resolve.alias.set(
      'bn.js',
      path.resolve(process.cwd(), 'node_modules', 'bn.js')
    );
    // 打包分析
    if (IS_PROD) {
      config.optimization.delete('splitChunks');
    }
    config.plugin('html').tap(args => {
      args[0].title = 'vue3-antd-admin管理系统';
      return args;
    });

    // svg rule loader
    config.module.rule('svg').exclude.add(resolve('src/assets/icons')).end();

    config.module
      .rule('icons')
      .test(/\.svg$/)
      .include.add(resolve('src/assets/icons'))
      .end()
      .use('svg-sprite-loader')
      .loader('svg-sprite-loader')
      .options({
        symbolId: 'icon-[name]',
      });
  },
  configureWebpack: config => {
    if (IS_PROD) {
      config.plugins.push(
        new UglifyJsPlugin({
          terserOptions: {
            warnings: false,
            compress: {
              drop_debugger: true, // 注释console
              drop_console: true,
              pure_funcs: ['console.log'], // 移除console
            },
          },
          extractComments: false, // 是否将注释提取到一个单独的文件中
          sourceMap: false,
          parallel: true,
        })
      );
      config.optimization = {
        splitChunks: {
          cacheGroups: {
            antd: {
              name: 'chunk-ant-design-vue',
              test: /[\\/]node_modules[\\/]ant-design-vue[\\/]/,
              chunks: 'all',
              priority: 3,
              reuseExistingChunk: true,
              enforce: true,
            },
            vendors: {
              name: 'chunk-vendors',
              test: /[\\/]node_modules[\\/]/,
              chunks: 'initial',
              priority: 2,
              reuseExistingChunk: true,
              enforce: true,
            },
            common: {
              name: 'chunk-common',
              chunks: 'initial',
              minChunks: 2,
              maxInitialRequests: 5,
              minSize: 0,
              priority: 1,
              reuseExistingChunk: true,
              enforce: true,
            },
          },
        },
      };
    }
    config.plugins.push(
      new webpack.DefinePlugin({
        DEV_ENV: !IS_PROD,
      })
    );
  },

  devServer: {
    proxy: {
      '^/crm': {
        // target: 'http://**********:8082', // 刘畅内网ip
        // target: 'http://**********:8082', // 刘家清内网ip
        // target: 'http://**********:8082', // 刘家清内网ip
        target: 'https://trade.5ifund.com:8443',
        // target: 'https://testcrm.5ifund.com:8443',
        logLevel: 'debug',
        secure: false,
        // onProxyReq(proxyReq) {
        //   // 设置cookie
        //   proxyReq.setHeader(
        //     'cookie',
        //     'hxmPid=mob_mkt_conf_20200930_545; Hm_lvt_78c58f01938e4d85eaf619eae71b4ed1=1637893628; Hm_lpvt_78c58f01938e4d85eaf619eae71b4ed1=1637956002; v=AwtEdDaT7nOwTzInUe4u6R4RmqTwoB8imbTj1n0I58qhnCVaBXCvcqmEcxmO; shiro_session=f55311ad-5d86-4c5f-839d-fb6bdaae91b5'
        //   );
        // },
        pathRewrite: {
          '^/crm': '/crm',
        },
      },
    },
    https: false,
  },
};
