# B2C-CRM-VUE3.0

B2C-CRM 前端基于vue3.0架构升级

### 目录结构
├─public
├─src
│  └─assets 静态图片
│  └─components 公用组件
│  └─docs 文档（空）
│  └─layout 布局组件，不适用
│  └─router 路由
│  └─server 接口
│  └─store vuex
│  └─styles 静态图片
│  └─type
│  └─utils 公用方法库
│  └─views 业务页面
│  └─APP.vue 入口组件
│  └─index.css 业务页面
│  └─main.ts 入口组件
├─types 全局ts声明
├─.editorconfig
├─.eslintrc.js
├─.gitignore.js
├─.prettierrc.js
├─babel.config.js
├─Dockerfile
├─index.html
├─package.json
├─tsconfig.json
├─vue.config.js

#### 注意点
ant-design的table、ijijin-table都替换成common下的CustomTable
拖拽表格使用basic下的agGrid-creater

#### 本地开发
``` sh
	npm run start

  http://ca.myhexin.com/ca/index/index 申请证书，发给刘家清开通测试环境权限
  本地 host 配置：************ testcrm.5ifund.com（文档：http://**************:8003/pages/viewpage.action?pageId=*********）
  https://testcrm.5ifund.com:8443/fund 拿取cookies，嵌入到本地开发环境
  例如：
  'crm_user_token': 'c8ba34d3-fdfa-4c8c-9fd4-03047a2e1b25'
  'PHPSESSID': 'ah8airttu8q56ss513fe0itfnt81pg12'

  const cookiesString = 'crm_user_token=c8ba34d3-fdfa-4c8c-9fd4-03047a2e1b25;';

  cookiesString.split(';').forEach(cookie => {
    const [key, value] = cookie.split('=');
    document.cookie = `${key.trim()}=${value.trim()}; expires=Thu, 01 Jan 2026 00:00:00 UTC; path=/`;
  });
```
#### 远程加载低代码页面
项目中统一使用 id + systemId 的方式远程加载页面，默认crm的低代码页面都在同一个项目下，默认systemId都为15，每个页面的id则通过平台上页面列表的右侧导出按钮获取，项目地址为[crm-system](https://frontend.myhexin.com/kingfisher/collector/html/kamis-admin-sys/#/dashboard/detail?systemId=15&systemName=crm-system)。
```JavaScript
  import { amisRemoteEmbed } from '@king-fisher/crm-amis-next';

  amisRemoteEmbed(domId, {
    id: 1162,
    systemId: 15,
  }, ...others);
```

#### 发布
通过iframe页面url嵌入到新CRM平台
测试环境
https://testcrm.5ifund.com:8443/fund/
权限管理/资源列表 配置菜单权限

准生产、生产让产品配置
