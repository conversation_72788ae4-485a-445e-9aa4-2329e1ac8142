<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <link rel="icon" href="./static/favicon.ico" />
  <link href="https://s.thsi.cn/hxapp/m/pkgs/amis/sdk/v3-5-2/iconfont.css" rel="stylesheet" type="text/css">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>浙江核新同花顺</title>
  <script src="//s.thsi.cn/cd/ijj-thsi-cn/js/lib/skywalking.1.1.0.min.js"></script>
  <script src="./static/js/watermark.js"></script>
  <script>
    try {
      const host = window.location.hostname.split('.')[0];
      const collector =
        host === 'crm'
          ? 'https://apm.hexin.cn/skywalking-web'
          : 'https://khtest.10jqka.com.cn/skywalking-web';
      ClientMonitor.register({
        collector: collector, // 采集器服务，注意区分环境和域名
        rate: 0.6, // 性能采集比例，根据自己业务的实际访问量设置采样率，详见下方《其他注意事项》
        service: 'b2cpcweb:rzzx-crm-b2c-fund-front-container', // 业务标识
        // 页面路径，没有用前端路由要自定义，可参考location.pathname关键字
        pagePath: location.hash.replace('#', '') || '/root',
        serviceVersion: 'pc_crm_1.0', // 平台(iOS/Android/PC)+版本，格式：设备标识_业务标识_版本标识
        autoTracePerf: false,
        enableSPA: false, // 是否考虑SPA的处理，没有用前端路由一定要置为 false
        useFmp: true, // 是否记录首屏加载信息
        noTrace: true,
      });
    } catch (e) {
      console.log(e);
    }
  </script>
</head>
<body>
  <div id="app"></div>
</body>
</html>
