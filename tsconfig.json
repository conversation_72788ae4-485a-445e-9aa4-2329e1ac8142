{"compilerOptions": {"target": "esnext", "module": "esnext", "strict": false, "jsx": "preserve", "importHelpers": true, "moduleResolution": "node", "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "experimentalDecorators": true, "sourceMap": true, "pretty": true, "resolveJsonModule": true, "allowJs": true, "checkJs": true, "suppressImplicitAnyIndexErrors": true, "strictPropertyInitialization": false, "downlevelIteration": true, "noUnusedLocals": false, "noImplicitAny": false, "noImplicitThis": false, "removeComments": false, "strictFunctionTypes": false, "baseUrl": ".", "types": ["webpack-env"], "typeRoots": ["./node_modules/@types/", "./types"], "paths": {"@/*": ["src/*"]}, "lib": ["esnext", "dom", "dom.iterable", "scripthost"]}, "include": ["src/*.d.ts", "src/**/*.ts", "src/**/*.d.ts", "src/**/**/*.d.ts", "src/**/*.jsx", "src/**/*.tsx", "src/**/*.vue", "src/**/**/*.vue", "src/**/**/**/*.vue", "types/**/*.d.ts", "types/**/*.ts", "src/main.ts", "src/App.vue"], "exclude": ["node_modules", "public", "dist"]}