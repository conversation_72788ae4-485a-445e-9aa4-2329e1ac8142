import { ColumnType } from 'ant-design-vue/lib/table/interface';

declare global {
  interface ActionOptions {
    type: 'select' | 'button' | 'text' | 'popconfirm'; // 控制类型，默认为a,可选： select | button | text
    text: string;
    permission?: {
      // 权限
      action?: 'create' | 'delete' | 'update' | 'retrieve'; // CRUD权限：创建（Create）、更新（Update）、读取（Retrieve）和删除（Delete）操作
      effect?: 'disabled';
    };
    props?: any; // 组件属性，v-bind="props"
    func?: ({ text, record, index }, callback: (...rest) => any) => any; // 动作事件触发回调
  }

  interface TableColumn extends ColumnType {
    /** 插槽名称 */
    slotOption?: { [x: string]: any };
    slotName?: string;
    headerSlotName?: string;
    slotsType?: 'format' | 'link' | 'component';
    slotsFunc?: (...rest) => any;
    actions?: ActionOptions[];
    children?: TableColumn[];
  }
  type GridColumn = {
    /** 列id */
    colId?: string;
    /** key值 */
    field: string;
    /** 表头名称 */
    headerName: string;

    // 显示
    /** 表头类名 */
    headerClass?:
      | string
      | string[]
      | ((...params: any) => string | string[] | null);
    /** 表头组件 */
    headerComponent?: string | ((...params: any) => string);
    /** 表头组件传参 */
    headerComponentParams?: {
      [x: string]: any;
    };
    /** 获取表头标题 */
    headerValueGetter?: string | ((...params: any) => string);
    /** 单元格类名 */
    cellClass?:
      | string
      | string[]
      | ((...params: any) => string | string | null);
    /** 单元格类名规则 */
    cellClassRules?: {
      [cssClassName: string]: ((params: any) => boolean) | string;
    };

    /** 宽度 */
    width?: number;
    /** 最小宽度 */
    minWidth?: number;
    /** 最大宽度 */
    maxWidth?: number;
    /** 固钉 */
    pinned?: boolean | ('left' | 'right') | null;
    /** 是否隐藏 */
    hide?: boolean;
    /** 文字换行 */
    wrapText?: boolean;
    /** 自动行高 */
    autoHeight?: boolean;

    // 选择
    /** 行选择 */
    checkboxSelection?: boolean | ((...params: any) => boolean);
    /** 表头选择 - 全选 */
    headerCheckboxSelection?: boolean | ((...params: any) => boolean);

    // 编辑
    /** 是否可编辑 */
    editable?: boolean;
    /** 可调整列宽 */
    resizable?: boolean;
    /** 自定义编辑器 */
    cellEditor?: string;
    /** 编辑内容超出单元格限制可以选择弹出编辑器 */
    cellEditorPopup?: boolean;
    /** 自定义编辑器参数 */
    cellEditorParams?: {
      [x: string | number]: unknown
    }

    // 渲染
    /** 单元格getter */
    valueGetter?: string | ((...params: any) => any);
    /** 单元格formatter */
    valueFormatter?: string | ((...params: any) => any);
    /** 单元格渲染 */
    cellRenderer?: string | ((...params: any) => any);
    /** 单元格传参 */
    cellRendererParams?: {
      [x: string | number]: unknown;
    };

    // 事件
    /** 单元格值变化事件 */
    onCellValueChanged?: (...params: any) => any;
    /** 单元格单击事件 */
    onCellClicked?: (...params: any) => any;
    /** 单元格双击事件 */
    onCellDoubleClicked?: (...params: any) => any;
    /** 单元格右键事件 */
    onCellContextMenu?: (...params: any) => any;
  };
}
export default GridColumn;
