export interface Columns extends TableColumn {
  sort?: boolean; // 是否排序
  pickList?: {
    value: string|number;
    label: string;
    picked?: boolean;
  }[]; // 筛选菜单
  [key: string]: any;
};
export type onTableSubmit = (
  pageNum: number,
  pageSize: number,
  options?: TableSubmitOption
) => void;
export type TableSubmitOption = {
  sortKey?: string; // 当前排序key
  sortOrder?: string; // 当前排序顺序
  pickMap?: {
    [key: string]: string[];
  }; // 筛选表单
}