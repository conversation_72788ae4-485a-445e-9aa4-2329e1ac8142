/*
 * @Author: like4
 * @Date: 2023-04-27 14:10:43
 * @LastEditTime: 2023-05-08 14:12:20
 */
import { createApp } from 'vue';
import App from './App.vue';
import router from './router';
import store from './store';
import { systemMonitor } from './utils/common';
import './index.css';
import {
  Modal,
  Table,
  Menu,
  SubMenu,
  MenuItem,
  Input,
  Button,
  Form,
  Card,
  Checkbox,
  CheckboxGroup,
  Radio,
  Col,
  Row,
  Select,
  DatePicker,
  Cascader,
  TreeSelect,
  Tree,
  Tag,
  Tabs,
  Descriptions,
  Avatar,
  Anchor,
  Pagination,
  Popconfirm,
  Textarea,
  SelectOption,
  Calendar,
  Upload,
  Space,
  Divider,
  InputNumber,
  Image,
  Spin,
} from 'ant-design-vue';
import '../src/styles/override.scss';
import amisInit from '@king-fisher/crm-amis-next';
import { fetchUserInfo } from './server/api';

const modules = [
  Modal,
  Table,
  Menu,
  SubMenu,
  MenuItem,
  Input,
  Button,
  Form,
  Card,
  Checkbox,
  CheckboxGroup,
  Radio,
  Col,
  Row,
  Select,
  SelectOption,
  DatePicker,
  Cascader,
  Pagination,
  Popconfirm,
  Textarea,
  Calendar,
  router,
  store,
  TreeSelect,
  Tree,
  Tag,
  Tabs,
  Descriptions,
  Avatar,
  Anchor,
  Upload,
  Space,
  Divider,
  InputNumber,
  Image,
  Spin,
];
// eslint-disable-next-line
const app = createApp(App as any);

(function () {
  modules.forEach((_module: any, index) => {
    app.use(_module);
  });
})();
app.config.errorHandler = (error: any) => {
  // vue自带捕获错误信息
  systemMonitor.error(error, 'vue');
};

router
  .isReady()
  .then(() => fetchUserInfo())
  .then(userInfo => {
    store.dispatch('updateUserInfo', userInfo);
    // 水印展示姓名和工号
    const { userName, userGh } = userInfo;
    const DEFAULT_MARK = 'THS_CRM';
    const watermark_txt = `${userName || ''}${userName ? '（' : ''}${
      userGh || DEFAULT_MARK
    }${userName ? '）' : ''}`;
    watermark?.init?.({
      watermark_txt,
      watermark_alpha: 0.1,
      watermark_width: 140,
      watermark_height: 50,
      watermark_y_space: 70,
      watermark_x_space: 200,
      watermark_fontsize: '16px',
      watermark_color: 'black',
    });
  })
  .catch(e => console.log('水印初始化失败', e))
  .then(() => amisInit('prod', { noWatermark: true }))
  .then(() => app.mount('#app'))

  .catch(e => console.log('amis初始化失败', e));
export default app;
