@import '../../node_modules/ant-design-vue/dist/antd.css';

.ant-message-notice {
  text-align: right !important;
  .ant-message-notice-content {
    width: 200px;
    text-align: center;
    overflow: hidden;
  }
}
.ant-spin-spinning {
  min-height: 200px;
}

.ant-modal-close-x {
  width: 26px !important;
  height: 26px !important;
  line-height: 26px !important;
}

.ant-table-small .ant-table-thead > tr > th,
.ant-table .ant-table-thead > tr > th {
  background: #e1eaf1 !important;
}
.ant-table.ant-table-small .ant-table-title,
.ant-table.ant-table-small .ant-table-footer,
.ant-table.ant-table-small .ant-table-thead > tr > th,
.ant-table.ant-table-small .ant-table-tbody > tr > td,
.ant-table.ant-table-small tfoot > tr > th,
.ant-table.ant-table-small tfoot > tr > td {
  padding: 5px 8px;
}
.ant-table-content {
  .ant-table-cell {
    text-align: center;
  }
}
.ant-table-thead {
  th.ant-table-cell{
    padding: 5px;
  }
}
.ant-table-tbody{
  .ant-table-row {
    td {
      padding: 2px;
    }
  }
}

.ant-table-container {
  // border: 1px solid #babfc7 !important;
  border-right: 0 !important;
}
.ant-table.ant-table-small .ant-table-thead > tr > th,
.ant-table .ant-table-thead > tr > th {
  border-bottom: 1px solid #babfc7 !important;
  border-right: 1px solid #babfc7 !important;
}
.ant-form-inline.ant-form {
  padding-top: 0px;
  .ant-form-item {
    margin-bottom: 0 !important;
  }
}
.ant-picker,
.ant-picker-small {
  width: 180px;
}
.ant-picker.ant-picker-range {
  width: 240px;
}
.ant-form {
  padding: 15px;
  background: #f0f0f0;

  .ant-form-item {
    margin-bottom: 15px;
  }
}
.table-container {
  .ant-table-pagination.ant-pagination {
    position: fixed;
    bottom: 40px;
    right: 10px;
    left: 0px;
    z-index: 900;
    margin: 0;
  }
}

.app-container {
  height: calc(100% - 60px);
  display: flex;
  flex-direction: column;
  .table-container {
    flex: 1;
    overflow: auto;
    border: 1px solid #babfc7 !important;
    .ant-table-content {
      overflow: visible !important;
    }
  }
}
.table-wrapper {
  padding: 0;
  padding-bottom: 0;

  .filter-wrapper {
    margin-bottom: 10px;
    white-space: nowrap;
    background: rgb(240, 240, 240);
    padding: 16px;
    display: flex;
    align-items: center;
    justify-content: flex-start;

    > * {
      margin-right: 15px;
    }
  }
  .ag-row > div {
    display: flex;
    align-items: center;
  }
}
.ant-layout-content {
  & > .ant-spin-nested-loading {
    height: 100%;
    & > .ant-spin-container {
      height: 100%;
    }
  }
}

.m-select {
  .ant-select-tree {
    .ant-select-tree-list-holder-inner {
      & > div:last-child {
        visibility: hidden;
      }
    }
  }
}

div.ant-select-tree-treenode[aria-selected="false"]{
  .ant-select-tree-node-content-wrapper{
    cursor: not-allowed;
  }
}
