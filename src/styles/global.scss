$header-height: 64px;
$footer-height: 70px;

:root {
  --default-background: rgb(236, 255, 246);
}
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  width: 6px;
  background: rgba(#454646, 0.1);
  -webkit-border-radius: 2em;
  -moz-border-radius: 2em;
  border-radius: 2em;
}

::-webkit-scrollbar-thumb {
  background-color: rgba(#3d4140, 0.5);
  background-clip: padding-box;
  min-height: 28px;
  -webkit-border-radius: 2em;
  -moz-border-radius: 2em;
  border-radius: 2em;
}

::-webkit-scrollbar-thumb:hover {
  background-color: rgba(#363838, 1);
}
body {
  background-color: var(--default-background);
}
#app {
  > * {
    background-color: var(--default-background);
  }
  > :nth-child(1) {
    padding: 10px;
  }
}
// 通用字体颜色
$fontColors: ("red", red), ("blue" blue), ("yellow", yellow);
@each $var, $color in $fontColors{
  [s-cr="#{$var}"] {
    color: $color;
  }
}
.z-hide {
  display: none !important;
}
.m-description-item {
  display: flex;
  align-items: center;
  margin-right: 20px;
  > :nth-child(1) {
    margin-right: 8px;
  }
}
.m-image {
  background-position: 0 0;
  background-repeat: no-repeat;
  background-size: 100% 100%;
}
.m-page-title, .m-page-subtitle {
  font-weight: bold;
  display: flex;
  align-items: center;
}
.m-page-title {
  margin-bottom: 10px;
  font-size: 20px;
  &::before {
    content: '';
    width: 8px;
    height: 20px;
    margin-right: 8px;
    background-color: rgb(74, 189, 255);
  }
}
.m-page-subtitle {
  margin-bottom: 2px;
  font-size: 14px;
  &::before {
    content: '';
    width: 6px;
    height: 14px;
    margin-right: 10px;
    background-color: rgb(253, 202, 107);
  }
}
.m-row-1 {
  background-color: #fff;
}
.m-row-2 {
  background-color: #f1fcff !important;
  &:hover > td {
    background-color: #def3ff !important;
  }
}