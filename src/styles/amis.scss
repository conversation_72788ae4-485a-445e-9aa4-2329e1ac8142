.cxd-Page {
  background-color: #ecfff6;
  .cxd-Page-content {
    .cxd-Page-title {
      display: flex;
      align-items: center;
      font-size: 20px;
      font-weight: bold;
      &::before {
        content: '';
        display: inline-block;
        width: 8px;
        height: 20px;
        margin-right: 8px;
        background-color: rgb(74, 189, 255);
      }
    }
    .cxd-Crud .cxd-Table-headToolbar {
      background-color: #ecfff6;
    }
    .cxd-TreeSelect {
      min-height: 37px;
    }
  }
}
.cxd-Table-table {
  thead tr th {
    text-align: center;
    background-color: rgb(225, 234, 241);
    font-size: 14px;
    font-weight: bold;
  }
  tbody tr td {
    text-align: center;
    font-size: 14px;
    .cxd-OperationField .cxd-Button--link {
      font-size: 14px;
    }
  }
  .cxd-Table-tr--even {
    background-color: #f1fcff;
  }
  .cxd-Table-tr--even:hover {
    background-color: #E6F0FF;
  }
}
.cxd-Modal .cxd-Modal-title {
  display: flex;
  align-items: center;
  font-weight: bold;
  font-size: 16px;
  &::before {
    content: '';
    width: 6px;
    height: 16px;
    margin-right: 10px;
    background-color: rgb(253, 202, 107);
  }
}
.cxd-Tabs .cxd-Tabs-content {
  background-color: transparent;
}
