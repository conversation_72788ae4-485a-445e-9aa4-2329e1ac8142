<template>
  <config-provider :locale="zhCN">
    <router-view />
  </config-provider>
</template>

<script>
import { defineComponent } from 'vue';
import zhCN from 'ant-design-vue/es/locale/zh_CN';
import 'dayjs/locale/zh-cn';
import { ConfigProvider } from 'ant-design-vue';
import dayjs from 'dayjs';
dayjs.locale('zh-cn');

export default defineComponent({
  name: 'App',
  components: { ConfigProvider },
  setup() {
    const isLock = true;
    return {
      zhCN,
      isLock,
    };
  },
});
</script>
