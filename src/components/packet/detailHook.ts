import { reactive } from 'vue';
import { baseWebURL } from '@/utils/request';
import { DETAIL_DIMENSION } from '@/views/packet/const';
import { CalcType } from '@/views/packet/type';

export default function useDetail() {
  const state = reactive<{
    /** 明细弹窗显示 */
    detailVisible: boolean;
    /** 明细弹窗类型 */
    detailType: 'red' | 'receive' | 'use' | '';
    /** 明细页链接 */
    detailSrc: string;
    /** 明细页id */
    detailId: string;
  }>({
    detailVisible: false,
    detailType: '',
    detailSrc: '',
    detailId: '',
  });
  /**
   * 各明细页跳转
   * @param detailType 明细页类型
   * @param type 统计维度 1 项目 2 活动 3 红包 4 销售
   * @param id id
   * @param params 扩展参数
   */
  const jumpDetail = (
    detailType: 'red' | 'receive' | 'use' | '',
    type: CalcType,
    id: string | number,
    params?: {
      key: string;
      value: string | number;
    }[]
  ) => {
    state.detailVisible = true;
    state.detailType = detailType;
    state.detailId = id.toString();
    const idName = DETAIL_DIMENSION[type];
    let _params = ``;
    if (params && params.length > 0) {
      for (const param of params) {
        _params += `&${param.key}=${param.value}`;
      }
    }
    state.detailSrc = `${baseWebURL}#/packet/detail/${state.detailType}?type=${type}&${idName}=${id}${_params}`;
    console.log('明细页跳转', detailType, type, id, state.detailSrc);
  };
  return {
    detail: state,
    jumpDetail,
  };
}
