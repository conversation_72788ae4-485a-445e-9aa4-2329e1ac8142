<template>
  <a-form-item
    :label="label"
    :name="name"
    :rules="rules"
    :label-col="labelCol"
    :wrapper-col="wrapperCol"
  >
    <slot :name="slotName"></slot>
  </a-form-item>
</template>

<script lang="ts" setup>
import { defineProps, PropType } from 'vue';

// props
const props = defineProps({
  label: {
    type: String,
    default: '',
  },
  name: {
    type: String,
    default: '',
  },
  rules: {
    type: Array as PropType<any[]>,
    default: () => [],
  },
  labelCol: {
    type: Object as PropType<{ [x: string]: any }>,
    default: null,
  },
  wrapperCol: {
    type: Object as PropType<{ [x: string]: any }>,
    default: null,
  },
  /** 插槽名称 */
  slotName: {
    type: String,
    default: '',
  },
});
</script>
