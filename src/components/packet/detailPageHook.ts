import { reactive, watch } from 'vue';
import { CalcType } from '@/views/packet/type';
import { getUrlParam } from '@/utils/fn';
import { DETAIL_DIMENSION } from '@/views/packet/const';

export default function useDetailPage(
  props: {
    componentType: 'component' | 'page';
    calcType: CalcType;
    calcId: string;
    showDate: boolean;
    download: boolean;
    // 明细依赖key，key值变化即刷新数据
    detailKey: string;
  },
  fetch: () => void
): {
  calcType: CalcType;
  calcId: string;
  showDate: boolean;
  download: boolean;
  detailKey?: string;
} {
  const ifComponent = props.componentType === 'component';
  const _type = ifComponent
    ? props.calcType
    : (Number(getUrlParam('type', 'hash')) as CalcType);
  const calc = reactive<{
    /** 统计维度 */
    calcType: CalcType;
    /** 统计id */
    calcId: string;
    /** 是否隐藏时间筛选 */
    showDate: boolean;
    /** 是否下载 */
    download: boolean;
    detailKey?: string;
  }>({
    calcType: _type,
    calcId: ifComponent
      ? props.calcId
      : getUrlParam(DETAIL_DIMENSION[_type], 'hash'),
    showDate: ifComponent
      ? props.showDate
      : getUrlParam('showDate', 'hash') !== 'false',
    download: ifComponent
      ? props.download
      : getUrlParam('download', 'hash') === 'true',
    detailKey: '',
  });
  calc.detailKey = props.detailKey || calc.calcId;

  // 监听props的id变化
  watch(
    () => [calc.detailKey, props.detailKey, props.calcId],
    ([key, pKey, id], [prevKey]) => {
      calc.calcId = id;
      calc.detailKey = pKey || id;
      if (key && key !== prevKey) {
        fetch && fetch();
      }
    }
  );

  return calc;
}
