<template>
  <div
    :class="['img-view', view ? type : '', { view: view }]"
    :style="{ width: width }"
  >
    <img
      v-show="view"
      class="m-img-back"
      :style="{ width: width }"
      :src="src"
      :alt="alt"
    />
    <img
      ref="imgRef"
      class="m-img"
      :style="{ width: width }"
      :src="src"
      :alt="alt"
      @click="handleOnView"
    />
    <div v-show="view" class="img-view-mask" @click.stop="handleOnCancel">
      <span class="m-image" @click="handleOnCancel"></span>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, ref, toRefs } from 'vue';
export default defineComponent({
  name: 'ImgView',
  emits: ['onView', 'onCancel'],
  props: {
    // 图片链接
    src: {
      type: String,
      default: () => '',
    },
    // 宽度
    width: {
      type: String,
      default: () => '',
    },
    // 图片说明
    alt: {
      type: String,
      default: () => 'image',
    },
  },
  setup(props, { emit }) {
    const imgRef = ref<HTMLImageElement>();
    const state = reactive<{
      view: boolean;
      type: 'horizontal' | 'vertical' | '';
    }>({
      view: false,
      type: '',
    });
    // controller
    /**
     * 控制图片放大展示
     */
    const handleOnView = () => {
      if (!state.view) {
        if (!state.type) {
          if (imgRef.value.clientWidth > imgRef.value.clientHeight)
            state.type = 'horizontal';
          else state.type = 'vertical';
        }
        document.body.style.overflow = 'hidden';
        state.view = true;
        emit('onView', props.src, state.type);
      }
    };
    /**
     * 取消图片放大展示
     */
    const handleOnCancel = () => {
      if (state.view) {
        document.body.style.overflow = 'auto';
        state.view = false;
        emit('onCancel', props.src, state.type);
      }
    };

    return {
      ...toRefs(state),
      imgRef,
      handleOnView,
      handleOnCancel,
    };
  },
});
</script>

<style lang="less" scoped>
.img-view {
  position: relative;
  display: inline-block;
  cursor: pointer;
  .img-view-mask {
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    background: rgba(0, 0, 0, 0.4);
    position: fixed;
    z-index: 19;
    > span {
      width: 32px;
      height: 32px;
      top: 20px;
      right: 20px;
      position: absolute;
      display: inline-block;
      background-image: url(../../assets/images/icon-delete.png);
      cursor: pointer;
    }
  }
  &.view > .m-img {
    max-width: 90vw;
    max-height: 90vh;
    left: 50%;
    top: 50%;
    position: fixed;
    display: block;
    transform: translate(-50%, -50%);
    z-index: 20;
  }
  &.vertical > .m-img {
    width: auto !important;
    height: 80% !important;
  }
  &.horizontal > .m-img {
    width: 80% !important;
    height: auto !important;
  }
}
</style>
