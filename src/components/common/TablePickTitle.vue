<template>
  <div class="table-title-pick">
    <div class="pick-title">
      <span>{{ text }}</span>
      <span class="ig-pick-icon" @click.stop="handleClick">
        <i class="m-image"></i>
      </span>
    </div>
    <div v-show="target" class="pick-wrapper" @click.stop="() => {}">
      <div class="pick-menu">
        <a-checkbox-group
          v-model:value="i_list"
          :options="list"
          @change="handlePick"
        />
      </div>
      <div class="pick-divider"></div>
      <div class="pick-btns">
        <a-button :disabled="unreset" type="link" @click="handleClear">
          重置
        </a-button>
        <a-button type="primary" @click="handleConfirm">确认</a-button>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import {
  computed,
  defineComponent,
  PropType,
  reactive,
  toRefs,
  watch,
} from 'vue';
export type PickList = {
  value: string;
  label: string;
  picked: boolean;
}[];
export default defineComponent({
  name: 'TableSortTitle',
  props: {
    text: {
      type: String,
      default: () => '',
    },
    pickKey: {
      type: String,
      default: () => '',
    },
    target: {
      type: Boolean,
      default: () => false,
    },
    list: {
      type: Array as PropType<PickList>,
      default: () => [],
    },
    onClick: {
      type: Function,
      default: () => () => {},
    },
    onPick: {
      type: Function,
      default: () => () => {},
    },
    onCancel: {
      type: Function,
      default: () => () => {},
    },
  },
  setup(props) {
    const state = reactive({
      i_list: [],
    });

    /** computed */
    const unreset = computed(() => {
      return state.i_list.length === 0;
    });
    /** watch */
    watch(
      () => props.target,
      (target, prevTarget) => {
        if (target !== prevTarget) {
          if (target) document.addEventListener('click', handleMouseDown);
          else document.removeEventListener('click', handleMouseDown);
        }
      }
    );
    /** methods */
    const init = () => {};
    // 点击屏幕
    const handleMouseDown = () => {
      handleCancel();
    };
    // 点击筛选按钮
    const handleClick = () => {
      props.onClick(props.pickKey);
    };
    // 更改筛选
    const handlePick = (value) => {
      state.i_list = value;
    };
    // 确认筛选
    const handleConfirm = () => {
      props.onPick(props.pickKey, state.i_list);
    };
    // 取消筛选
    const handleCancel = () => {
      props.onCancel(props.pickKey);
      state.i_list = [];
    };
    // 重置
    const handleClear = () => {
      state.i_list = [];
    };

    init();
    return {
      ...toRefs(state),
      unreset,
      handleClick,
      handlePick,
      handleConfirm,
      handleClear,
    };
  },
});
</script>

<style lang="less" scoped>
.table-title-pick {
  position: relative;
  .pick-title {
    display: flex;
    align-items: center;
    > span:nth-child(1) {
      margin-right: 3px;
    }
    .ig-pick-icon {
      padding: 2px;
      > i {
        width: 10px;
        height: 12px;
        display: inline-block;
        background-image: url(../../assets/images/icon-pick.png);
      }
      cursor: pointer;
    }
  }
  .pick-wrapper {
    padding: 6px 8px;
    right: -6px;
    position: absolute;
    background-color: #fff;
    box-shadow: 0 2px 8px #00000026;
    border-radius: 6px;
    z-index: 2;
    .pick-menu {
      .ant-checkbox-group {
        display: flex;
        flex-flow: column nowrap;
        align-items: flex-start;
      }
    }
    .pick-divider {
      width: calc(100% + 12px);
      height: 1px;
      margin: 10px 0;
      left: -6px;
      position: relative;
      background-color: rgba(204, 204, 204, 0.4);
    }
    .pick-btns {
      display: flex;
      justify-content: space-between;
      > button:nth-child(1) {
        margin-right: 16px;
        padding: 0;
      }
      > button:nth-child(2) {
        padding: 4px 10px;
      }
    }
  }
}
</style>
