<template>
  <div ref="tableRef" class="custom-table app-container">
    <a-table
      :pagination="false"
      :columns="columns"
      :data-source="tableData"
      :scroll="scroll"
      :sticky="sticky"
      :bordered="bordered"
      :row-class-name="rowClassName"
      :row-selection="rowSelection"
      class="table-container"
    >
      <!-- 表头 -->
      <template #headerCell="{ column }">
        <!-- 自定义表头 -->
        <template v-if="column.headerSlotName">
          <div :class="`header-${column.key}`">
            <slot :name="column.headerSlotName" :column="column"></slot>
          </div>
        </template>
        <!-- 排序 -->
        <table-sort-title
          v-else-if="column.sort"
          :text="column.title"
          :target="sortKey === column.key"
          :sort-key="column.key"
          :sort-order="sortOrder"
          :on-sort="handleSort"
        />
        <!-- 筛选 -->
        <table-pick-title
          v-else-if="column.pickList && column.pickList.length > 0"
          :text="column.title"
          :pick-key="column.key"
          :target="pickKey === column.key"
          :list="column.pickList"
          :on-click="handlePickClick"
          :on-pick="handlePickConfirm"
          :on-cancel="handlePickCancel"
        />
        <template v-else>
          <div :class="`header-${column.key}`">
            {{ column.title }}
          </div>
        </template>
      </template>
      <!-- 单元格 -->
      <template #bodyCell="{ text, column, record, index }">
        <!-- 两位小数金额 -->
        <template v-if="column.slotName === 'cash'">
          <div :class="`cell-${column.key}`">
            {{ formatMoney(text) }}
          </div>
        </template>
        <template v-else-if="column.slotName">
          <div :class="`cell-${column.key}`">
            <slot
              :name="column.slotName"
              :text="text"
              :column="column"
              :record="record"
              :index="index"
            ></slot>
          </div>
        </template>
        <div v-else :class="`cell-${column.key}`">
          {{ text === 0 ? text : text || '--' }}
        </div>
      </template>
    </a-table>
    <div v-if="!hidePagination" class="table-pagination">
      <a-pagination
        v-model:current="pageNumState"
        v-model:pageSize="pageSizeState"
        v-model:total="total"
        :page-size-options="pageSizeList"
        :show-total="showTotal"
        show-size-changer
        show-less-items
      />
    </div>
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  onMounted,
  PropType,
  reactive,
  toRefs,
  watch,
  computed,
  ref,
} from 'vue';
import TableSortTitle, { SortOrderValue } from './TableSortTitle.vue';
import TablePickTitle from './TablePickTitle.vue';
import { Columns, onTableSubmit } from 'types/custom';
import { UnwrapNestedRefs } from '@vue/reactivity';
import { formatMoney } from '@/utils/fn';

type iState = {
  pageNumState: number; // 页码
  pageSizeState: number; // 页容量
  sortKey: string; // 排序列key
  sortOrder: SortOrderValue; // 排序方式
  pickKey: string; // 当前显示筛选菜单key
  pickMap: {
    [key: string]: string[];
  }; // 筛选表单
};

export default defineComponent({
  name: 'CustomTable',
  components: {
    TableSortTitle,
    TablePickTitle,
  },
  props: {
    // 数据总数
    tableTotal: {
      type: Number,
      default: () => 0,
    },
    // 表格数据
    tableData: {
      type: Array as PropType<
        {
          [key: string]: any;
        }[]
      >,
      default: () => [],
    },
    pageNum: {
      type: Number,
      default: () => 1,
    },
    pageSize: {
      type: Number,
      default: () => 10,
    },
    // 表格列配置
    columns: {
      type: Array as PropType<Columns[]>,
      default: () => [],
    },
    // 分页数组
    pageSizeList: {
      type: Array as PropType<string[]>,
      default: () => ['5', '10', '20', '50'],
    },
    showTotal: {
      type: Function,
      default: (total: number) => `共${total}条记录`,
    },
    sticky: {
      type: Boolean,
      default: () => false,
    },
    scroll: {
      type: Object,
      default: () => ({}),
    },
    // 默认页码
    defaultPageSize: {
      type: Number,
      default: () => 10,
    },
    // 查询回调
    onSubmit: {
      type: Function as PropType<onTableSubmit>,
      default: () => () => {},
    },
    hidePagination: {
      type: Boolean,
      default: false,
    },
    bordered: {
      type: Boolean,
      default: true,
    },
    rowClassName: {
      type: Function as PropType<(record?: any, index?: number) => any>,
      default: () => (record: any, index: number) =>
        `m-row-${index % 2 ? 1 : 2}`,
    },
    rowSelection: {
      type: Object,
      default: null,
    },
  },
  setup(props, context) {
    const tableRef = ref<HTMLElement>(null);
    const state = reactive<iState>({
      pageNumState: 1,
      pageSizeState: props.defaultPageSize || Number(props.pageSizeList[0]),
      sortKey: '',
      sortOrder: 'none',
      pickKey: '',
      pickMap: {},
    });
    // computed
    const total = computed(() => {
      return props.tableTotal || props.tableData.length;
    });
    // methods
    const init = () => {
      // 初始化筛选项表单
      props.columns.forEach((col: Columns) => {
        if (col.pickList && col.pickList.length > 0) {
          col.pickList = col.pickList.map(item => {
            item.picked = false;
            return item;
          });
          state.pickMap[col.key] = [];
        }
      });
    };
    /** 点击排序按钮 */
    const handleSort = (key: string, order: SortOrderValue) => {
      console.log('排序', key, order);
      const orderMap = {
        none: 0,
        up: 1,
        down: 2,
      };
      const ordsers = Object.keys(orderMap);
      if (state.sortKey !== key) state.sortOrder = 'up';
      else
        state.sortOrder = ordsers[
          (orderMap[order] + 1) % ordsers.length
        ] as SortOrderValue;
      state.sortKey = key;
      onSubmit();
    };
    /** 点击筛选按钮 */
    const handlePickClick = (key: string) => {
      console.log('点击筛选', state.pickKey, key);
      if (state.pickKey === key) state.pickKey = '';
      else state.pickKey = key;
    };
    /** 点击筛选确认按钮 */
    const handlePickConfirm = (key: string, value: string[]) => {
      state.pickMap[key] = value;
      state.pickKey = '';
      console.log('确认筛选', value, state.pickMap);
      onSubmit();
    };
    /** 隐藏筛选菜单 */
    const handlePickCancel = () => {
      console.log('隐藏筛选');
      state.pickKey = '';
    };
    /** 触发查询表格数据回调 */
    const onSubmit = () => {
      if (props.scroll.scrollToFirstRowOnChange) {
        tableRef.value.querySelector('.ant-table-body').scrollTop = 0;
      }
      props.onSubmit(state.pageNumState, state.pageSizeState, {
        sortKey: state.sortKey,
        sortOrder: state.sortOrder,
        pickMap: state.pickMap,
      });
    };

    // watch
    /** 监听props.pageNum */
    watch(
      () => props.pageNum,
      pageNum => {
        if (pageNum !== state.pageNumState) {
          state.pageNumState = pageNum;
        }
      }
    );
    /** 监听props.pageSize */
    watch(
      () => props.pageSize,
      pageSize => {
        if (pageSize !== state.pageSizeState) {
          state.pageSizeState = pageSize;
        }
      }
    );
    /** 监听state.pageNum */
    watch(
      () => state.pageNumState,
      (pageNum, prePageNum) => {
        if (pageNum !== prePageNum) {
          context.emit('update:pageNum', pageNum);
        }
        onSubmit();
      }
    );
    /** 监听state.pageSize */
    watch(
      () => state.pageSizeState,
      (pageSize, prePageSize) => {
        if (pageSize !== prePageSize) {
          context.emit('update:pageSize', pageSize);
          if (state.pageNumState !== 1) {
            state.pageNumState = 1;
          } else {
            onSubmit();
          }
        }
      }
    );

    onMounted(init);
    return {
      ...toRefs(state),
      tableRef,
      total,
      handleSort,
      handlePickClick,
      handlePickConfirm,
      handlePickCancel,
      formatMoney,
    };
  },
});

type CustomSubmitOptions = {
  sortKey?: string; // 当前排序key
  sortOrder?: string; // 当前排序顺序
  pickMap?: {
    [key: string]: string[];
  }; // 筛选表单
};
type TableHook<T> = {
  total: number;
  tableData: T[];
  pageNum: number;
  pageSize: number;
  selectedRowKeys: any[];
  submitOptions: CustomSubmitOptions;
};
export function useCustomTable<T>(defaultSize = 10): {
  table: any;
  onTableChange: (fetch: () => void) => void;
  onTableSubmit: (
    fetch: () => void
  ) => (
    pageNum: number,
    pageSize: number,
    options: CustomSubmitOptions
  ) => void;
  rowClassName: (
    class1?: string,
    class2?: string
  ) => (record: any, index: number) => string;
} {
  const table = reactive<TableHook<T>>({
    total: 0,
    tableData: [],
    pageNum: 1,
    pageSize: defaultSize,
    selectedRowKeys: [],
    submitOptions: {
      sortKey: '',
      sortOrder: 'none',
      pickMap: {},
    },
  });
  /**
   * 主动触发表格查询
   * @param fetch 查询回调
   */
  const onTableChange = (fetch: () => void) => {
    table.selectedRowKeys = [];
    if (table.pageSize !== defaultSize) {
      table.pageSize = defaultSize;
    } else if (table.pageNum !== 1) {
      table.pageNum = 1;
    } else {
      fetch();
    }
  };
  /**
   * 自动触发表格查询
   * @param fetch 查询回调
   */
  const onTableSubmit = (fetch: () => void) => {
    return function _onTableSubmit(
      pageNum: number,
      pageSize: number,
      options: CustomSubmitOptions
    ) {
      table.pageNum = pageNum;
      table.pageSize = pageSize;
      table.submitOptions = options;
      table.selectedRowKeys = [];
      fetch();
    };
  };
  /**
   * 表格斑马线
   * @param className1
   * @param className2
   * @returns function
   */
  const rowClassName = (className1 = 'm-row-1', className2 = 'm-row-2') => {
    return function _rowClassName(record: any, index: number) {
      return index % 2 === 0 ? className1 : className2;
    };
  };

  return {
    table,
    onTableChange,
    onTableSubmit,
    rowClassName,
  };
}
</script>

<style lang="less">
.custom-table {
  .table-pagination {
    margin-top: 20px;
    display: flex;
    .ant-pagination {
      margin-left: auto;
    }
  }
  .ant-table {
    .ant-table-container::after {
      display: none;
    }
  }
  .ant-pagination-options-size-changer {
    min-width: 100px;
  }
}
</style>
