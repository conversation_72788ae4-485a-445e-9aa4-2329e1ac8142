<template>
  <div :class="{ 'custom-modal': true, show: visible, hidden: !visible }">
    <div class="modal-mask" @click="handleCancel"></div>
    <div class="modal-wrapper">
      <div v-show="!!title" class="modal-title">{{ title }}</div>
      <div class="modal-content">
        <slot name="content"></slot>
      </div>
      <div class="modal-footer">
        <div v-if="!customFooter" class="options">
          <a-button v-if="cancelButton" type="primary" ghost @click="handleCancel">取消</a-button>
          <a-button v-if="okButton" type="primary" @click="handleConfirm">确认</a-button>
        </div>
        <div v-else class="options">
          <slot name="footer"></slot>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, watch } from 'vue';

export default defineComponent({
  name: 'CustomModal',
  emits: ['ok', 'cancel', 'update:visible'],
  props: {
    visible: {
      type: Boolean,
      default: () => false,
    },
    title: {
      type: String,
      default: () => '',
    },
    customFooter: {
      type: Boolean,
      default: () => false,
    },
    okButton: {
      type: Boolean,
      default: () => true,
    },
    cancelButton: {
      type: Boolean,
      default: () => true,
    },
  },
  // props: ['visible', 'title', 'customFooter'],
  setup(props, context) {
    const state = reactive({});
    // watch
    /** 打开弹窗时固定页面 */
    watch(
      () => props.visible,
      (visible) => {
        document.body.style.overflow = visible ? 'hidden' : 'auto';
      }
    )
    const handleConfirm = () => {
      context.emit('ok');
      context.emit('update:visible', false);
    };
    const handleCancel = () => {
      context.emit('cancel');
      context.emit('update:visible', false);
    };

    return { ...toRefs(state), handleConfirm, handleCancel };
  },
});
</script>

<style lang="less">
.custom-modal {
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  display: flex;
  position: fixed;
  justify-content: center;
  align-items: center;
  z-index: 999;
  &.hidden {
    display: none;
  }
  &.show > .modal-wrapper {
    animation: modal-show 100ms ease-out 1;
  }
  .modal-mask {
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    position: absolute;
    background-color: rgba(0, 0, 0, 0.45);
    z-index: 1;
  }
  .modal-wrapper {
    position: relative;
    z-index: 2;
    background-color: #fff;
    box-shadow: 0 0 6px rgba(0, 0, 0, 0.2);
    border-radius: 2px;
    .modal-title {
      padding: 12px 24px;
      font-size: 20px;
      border-bottom: 1px #cccccc55 solid;
    }
    .modal-content {
      padding: 12px;
    }
    .modal-footer {
      padding: 12px 24px;
      border-top: 1px #cccccc55 solid;
      .options {
        display: flex;
        > button:nth-child(1) {
          margin-left: auto;
        }
        > button:nth-child(2) {
          margin-left: 20px;
        }
      }
    }
  }
}
@keyframes modal-show {
  0% {
    transform: scale(0);
  }
  100% {
    transform: scale(1);
  }
}
</style>
