<template>
  <div class="table-title-sort">
    <span>{{ text }}</span>
    <div :class="`ig-sort-icon ${order}`" @click="handleClick">
      <span class="m-image"></span>
      <span class="m-image"></span>
    </div>
  </div>
</template>

<script lang="ts">
import { computed, defineComponent, PropType } from 'vue';
export type SortOrderValue = 'none' | 'up' | 'down';
export default defineComponent({
  name: 'TableSortTitle',
  props: {
    text: {
      type: String,
      default: () => '',
    },
    target: {
      type: Boolean,
      default: () => false,
    },
    sortKey: {
      type: String,
      default: () => '',
    },
    sortOrder: {
      type: String as PropType<'none' | 'up' | 'down'>,
      default: () => 'none',
    },
    onSort: {
      type: Function,
      default: () => () => {},
    },
  },
  setup(props) {
    const order = computed(() => {
      if (!props.target) return 'none';
      else return props.sortOrder;
    });

    const handleClick = () => {
      props.onSort(props.sortKey, props.sortOrder);
    };

    return { order, handleClick };
  },
});
</script>

<style lang="less" scoped>
.table-title-sort {
  display: flex;
  align-items: center;
  .ig-sort-icon {
    margin-left: 6px;
    display: flex;
    flex-flow: column;
    cursor: pointer;
    > span {
      width: 8px;
      height: 8px;
      background-image: url(../../assets/images/icon-sort-unselected.png);
    }
    > span:nth-child(2) {
      transform: rotate(180deg);
    }
    &.up > span:nth-child(1),
    &.down > span:nth-child(2) {
      background-image: url(../../assets/images/icon-sort-selected.png);
    }
  }
}
</style>
