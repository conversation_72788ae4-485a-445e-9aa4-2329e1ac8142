/*
  params {
    fetchData // 获取表格数据接口 
    url // 接口路径
  }
  return {
    tableState,// 表格state
    tableData,// 表格data
    reLoadTable,// 获取表格data的函数
  }
*/
import { reactive, ref } from 'vue';
type Param = {
  pageNo?: number;
  pageNum?: number;
  pageSize: number;
};
export default function useTable(fetchData: any, url: string) {
  const tableState = reactive({
    loading: false, // 加载动画
  });
  // 数据源
  const tableData = ref<any>([]);
  // 获取数据源
  const reLoadTable = async (data: any, param: Param) => {
    tableState.loading = true;
    fetchData(url, data, param)
      .then((res: any) => {
        if (res) {
          tableData.value = [res] || [];
        }
        tableState.loading = false;
      })
      .catch((err: unknown) => {
        tableState.loading = false;
        console.log('获取表格失败:', err);
      });
  };
  return {
    tableState,
    tableData,
    reLoadTable,
  };
}
