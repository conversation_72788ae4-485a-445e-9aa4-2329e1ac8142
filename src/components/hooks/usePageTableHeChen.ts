/*
  params {
    fetchData // 获取表格数据接口 
    url // 接口路径
  }
  return {
    tableState,// 表格state
    tableData,// 表格data
    reLoadTable,// 获取表格data的函数
  }
*/
import { reactive, ref } from 'vue';
export type Param = {
  aiId?: any;
  pageNo?: number;
  pageNum?: number;
  pageSize: number;
};
export type Data = {
  crmRecord: any;
  total: number;
  current: number;
  size: number;
};
export default function usePageTable(fetchData: any, url: string) {
  const tableState = reactive({
    total: null, // 总数据条数
    pageNum: 1, // 当前页面
    pageSize: 10, // 页面大小
    loading: false, // 加载动画
  });
  // 数据源
  const tableData = ref<any>([]);
  // 获取数据源
  const reLoadTable = async (data: any, param: Param) => {
    tableState.loading = true;
    return fetchData(url, data, param)
      .then((res: Data) => {
        if (res) {
          tableData.value = res.crmRecord || [];
          tableState.total = res.total;
          tableState.pageNum = res.current;
          tableState.pageSize = res.size;
        } else {
          tableData.value = [];
          tableState.total = 0;
          tableState.pageNum = 0;
          tableState.pageSize = 10;
        }
        tableState.loading = false;
      })
      .catch((err: unknown) => {
        tableState.loading = false;
        console.log('获取表格失败:', err);
      });
  };
  return {
    tableState,
    tableData,
    reLoadTable,
  };
}
