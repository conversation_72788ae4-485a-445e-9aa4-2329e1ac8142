import { reactive } from 'vue';

type TableOptions = {
  defaultSize?: number;
}
export type TableHook<T> = {
  table: {
    pageNum: number;
    pageSize: number;
    total: number;
    tableData: T[];
  };
  onTableChange: (fetch: () => void) => void;
  onTableSubmit: (
    fetch: () => void
  ) => ({ pageNum, pageSize }: { pageNum: number; pageSize: number }) => void;
  initTable: () => void;
}

export default function useTable<T>(options: TableOptions = {}): TableHook<T> {
  options = {
    defaultSize: 10,
    ...options,
  }
  const table = reactive<{
    pageNum: number;
    pageSize: number;
    total: number;
    tableData: any[];
  }>({
    pageNum: 1,
    pageSize: options.defaultSize,
    total: 0,
    tableData: [],
  });

  /**
   * 主动触发表格查询
   * @param fetch 查询回调
   */
  const onTableChange = (fetch: () => void) => {
    if (table.pageSize !== options.defaultSize) {
      table.pageSize = options.defaultSize;
    } else if (table.pageNum !== 1) {
      table.pageNum = 1;
    } else {
      fetch();
    }
  };
  /**
   * 自动触发表格查询
   * @param fetch 查询回调
   */
  const onTableSubmit = (fetch: () => void) => {
    return function _onTableSubmit({
      pageNum,
      pageSize,
    }: {
      pageNum: number;
      pageSize: number;
    }) {
      table.pageNum = pageNum;
      table.pageSize = pageSize;
      fetch();
    };
  };

  /** 清空表格 */
  const initTable = () => {
    table.total = 0;
    table.tableData = [];
  }

  return {
    table,
    onTableChange,
    onTableSubmit,
    initTable,
  };
}
