<template>
  <div>架构管理</div>
  <!-- <vue-org-tree
    :data="orgTree"
  /> -->
  <org-tree
    :data="tree"
    :collapsable="true"
    :horizontal="true"
    :onClick="handleTreeNodeClick"
  />
  <!-- 弹窗 -->
  <a-modal
    :type="type"
    :node="treeMap[nodeId] || {}"
    :visible="visible"
    :confirm-loading="confirmLoading"
    @ok="handleConfirm"
    @cancel="handleCancel"
  >
    <template v-slot:department>
      <div>department</div>
    </template>
    <template v-slot:group>
      <div>group</div>
    </template>
  </a-modal>
</template>

<script lang="ts">
import { computed, defineComponent, reactive, toRefs } from 'vue';
import { orgTreeData, UserInfo } from '../../views/person/type';
import OrgTree from '@/components/person/orgTree.vue';
import Modal from '@/components/person/Modal.vue';
import 'ant-design-vue/dist/antd.css';
// import { Modal } from 'ant-design-vue';

type iState = {
  userInfo: UserInfo; // 用户信息（权限之类）
  tree: orgTreeData; // 组织树
  treeMap: any; // 组织节点表，以id为key
  type: '' | 'scan' | 'edit'; // 操作类型
  nodeId: number; // 当前节点id
  visible: boolean; // 弹窗是否可见
  confirmLoading: boolean; // 确认后loading
};

const mockTree: orgTreeData = {
  label: '基金销售crm',
  level: 'root',
  // children: [],
  children: [
    {
      label: '销售1部',
      level: 'department',
      children: [
        {
          label: 'a组1',
          level: 'group',
          children: [],
        },
        {
          label: 'a组2',
          level: 'group',
          children: [],
        },
        {
          label: 'a组1',
          level: 'group',
          children: [],
        },
        {
          label: 'a组2',
          level: 'group',
          children: [],
        },
        {
          label: 'a组1',
          level: 'group',
          children: [],
        },
        {
          label: 'a组2',
          level: 'group',
          children: [],
        },
        {
          label: 'a组1',
          level: 'group',
          children: [],
        },
        {
          label: 'a组2',
          level: 'group',
          children: [],
        },
      ],
    },
    {
      label: '销售2部',
      level: 'department',
      children: [
        {
          label: 'b组1',
          level: 'group',
          children: [],
        },
        {
          label: 'b组2',
          level: 'group',
          children: [],
        },
      ],
    },
  ],
};

export default defineComponent({
  components: {
    OrgTree,
    // Modal,
  },
  props: {},
  setup() {
    const state = reactive<iState>({
      // TODO 用户权限控制
      userInfo: {
        level: '',
      },
      tree: {
        id: 0,
        label: '',
        level: 'root',
        children: [],
      },
      treeMap: {},
      nodeId: 0,
      type: '',
      visible: false,
      confirmLoading: false,
    });

    let idFlag = 0;
    let _children = [];

    // TODO 创建部门树
    const createTree = () => {
      state.tree = setNodeId(mockTree);
      console.log(state.tree, state.treeMap);
    };
    // 设置部门树id
    const setNodeId = (tree: orgTreeData) => {
      _children.push(tree);
      while (_children.length > 0) {
        const node = _children[0];
        _children = [..._children, ...node.children];
        _children.shift();
        node.id = idFlag;
        state.treeMap[idFlag] = node;
        idFlag++;
      }
      return tree;
    };
    // TODO 提交表单
    const submit = () => {
      console.log('提交表单');
    };

    // TODO 树节点点击控制器
    const handleTreeNodeClick = (data: orgTreeData, type: 'scan' | 'edit') => {
      if (data.level === 'root') return;
      state.nodeId = data.id;
      state.visible = true;
      state.type = type;
      console.log(data, type, state.nodeId);
    };
    // TODO 弹窗确认按钮
    const handleConfirm = (id: string, data: any) => {
      console.log('确认按钮', id, data);
      if (state.type === 'edit') {
        submit();
      }
      state.visible = false;
    };
    // TODO 弹窗取消按钮
    const handleCancel = (id: string, data: any) => {
      console.log('取消弹窗');
      state.visible = false;
    };

    createTree();
    console.log(state);

    return {
      ...toRefs(state),
      handleTreeNodeClick,
      handleConfirm,
      handleCancel,
    };
  },
});
</script>

<style lang="less">
.m-image {
  background-position: 0 0;
  background-repeat: no-repeat;
  background-size: 100% 100%;
}
</style>
