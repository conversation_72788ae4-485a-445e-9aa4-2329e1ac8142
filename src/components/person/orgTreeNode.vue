<template>
  <div
    :class="{
      'org-tree-node': true,
      collapsed: collapse,
      'is-leaf': data.children.length === 0,
    }"
  >
    <div class="org-tree-node-label">
      <div :class="{ 'org-tree-node-label-inner': true, click: !!onClick }">
        <div :class="{ 'label-content': true, 'click-disabled': data.level === 'root' }" @click="() => onClickNode('scan')">
          <span>{{ data.label }}</span>
          <img
            v-show="data.level !== 'root'"
            @click.stop="() => onClickNode('edit')"
            :src="iconEdit"
           
          />
        </div>
        <div
          v-show="collapsable && data.children.length > 0"
          :class="{ 'org-tree-node-btn': true, expanded: !collapse }"
          @click="onClickCollapse"
        ></div>
      </div>
    </div>
    <div
      class="org-tree-node-children"
      v-show="data.children.length > 0 && !collapse"
    >
      <org-tree-node
        v-for="item in data.children"
        :key="item.id"
        :data="item"
        :horizontal="horizontal"
        :collapsable="collapsable"
        :onExpand="onExpand"
        :onClick="onClick"
      />
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, PropType, reactive, toRefs, computed } from 'vue';
import { orgTreeData } from '@/views/person/type';
import iconEdit from '@/assets/images/icon-edit.png';

type iState = {
  collapse: boolean; // 节点是否收起
};

export default defineComponent({
  name: 'OrgTreeNode',
  props: {
    // 架构组件数据
    data: {
      type: Object as PropType<orgTreeData>,
      default: () => {
        return {};
      },
    },
    // 是否可收起
    horizontal: {
      type: Boolean,
      default: () => false,
    },
    // 是否水平朝向
    collapsable: {
      type: Boolean,
      default: () => false,
    },
    // 展开 控制器
    onExpand: {
      type: Function as PropType<(data: orgTreeData, ...arg: any[]) => any>,
      default: () => {},
    },
    // 节点点击 控制器
    onClick: {
      type: Function as PropType<(data: orgTreeData, ...arg: any[]) => any>,
      default: () => {},
    },
  },
  setup(props) {
    const state = reactive<iState>({
      collapse: false,
    });

    // 伸缩按钮
    const onClickCollapse = () => {
      if (props.data.level === 'root') return;
      state.collapse = !state.collapse;
      props.onExpand && props.onExpand(props.data);
    };

    // 节点按钮
    const onClickNode = (type: 'scan' | 'edit') => {
      if (props.data.level === 'root') return;
      props.onClick && props.onClick(props.data, type);
    };

    return { ...toRefs(state), iconEdit, onClickCollapse, onClickNode };
  },
});
</script>

<style lang="less">
.click-disabled {
  cursor: url(../../assets/images/icon-ban.png), text !important;
}
.label-content {
  display: flex;
  align-items: center;
  img {
    margin-left: 8px;
    width: 16px;
    height: 16px;
  }
}
</style>