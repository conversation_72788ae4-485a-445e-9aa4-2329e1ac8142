<!-- 表格组件 -->
<template class="app-container">
  <a-table
    class="table-containers"
    :scroll="{ x: 'max-content' }"
    :columns="columnInfo"
    :data-source="tableData"
    :loading="table.loading"
    :pagination="
      ifNeedPagination
        ? {
            total: table.total,
            pageSize: table.pageSize,
            showSizeChanger: true,
            showSizeChange: onPageSizeChange,
          }
        : false
    "
    :row-selection="
      ifNeedSelection
        ? {
            selectedRowKeys,
            onChange: onSelectChange,
          }
        : null
    "
    @change="onChange"
  >
    <template #bodyCell="{ text, column, record, index }">
      <template v-if="column.slotName">
        <slot
          :name="column.slotName"
          :text="text"
          :column="column"
          :record="record"
          :index="index"
        ></slot>
        <slot v-if="column.slotName === 'money'">
          {{ text ? currency(text, { symbol: '￥', precision: 2 }).format() : '--' }}
        </slot>
      </template>
      <template v-else>
        {{ text === 0 ? text : text || '--' }}
      </template>
    </template>
  </a-table>
</template>
<script lang="ts">
import { defineComponent, ref } from 'vue';
import currency from 'currency.js';
import { PaginationProps } from 'ant-design-vue';

export default defineComponent({
  emits: ['change', 'onSelectChange'],
  props: {
    columnInfo: {
      type: Array,
      required: true,
    },
    table: {
      type: Object,
      required: true,
    },
    tableData: {
      type: Array,
      required: true,
    },
    // 是否需要分页
    ifNeedPagination: {
      type: Boolean,
      default: true,
    },
    // 是否需要选择
    ifNeedSelection: {
      type: Boolean,
      default: false,
    },
  },
  setup(props, context) {
    // 选中项的 key 数组，需要和 onChange 进行配合
    const selectedRowKeys = ref<string[] | number[]>([]);
    // 当前分页变化
    const onChange = (
      pagination: PaginationProps,
      filters: any,
      sorter: any
    ) => {
      if (filters || sorter) {
        context.emit('change', {
          pageNum: pagination.current,
          pageSize: pagination.pageSize,
          filters: filters,
          sorter: sorter,
        });
      } else {
        context.emit('change', {
          pageNum: pagination.current,
          pageSize: pagination.pageSize,
        });
      }
    };
    // 分页大小变化
    const onPageSizeChange = (current: number, size: number) => {
      context.emit('change', {
        pageNum: current,
        pageSize: size,
      });
    };
    // 列表项选择
    const onSelectChange = (rowKeys: number[]) => {
      selectedRowKeys.value = rowKeys;
      context.emit('onSelectChange', {
        selectedRowKeys: rowKeys,
      });
    };
    return {
      selectedRowKeys,
      currency,
      onChange,
      onPageSizeChange,
      onSelectChange,
    };
  },
});
</script>
<style scoped lang="scss">
.table-containers {
  flex: 1;
  overflow: auto;
  border: 1px solid #babfc7 !important;
  .ant-table-content {
    overflow: visible !important;
  }
}
</style>
