<template>
  <div
    style="
      background: #f0f0f0;
      position: relative;
      overflow: hidden;
      padding: 20px;
      padding-bottom: 10px;
    "
  >
    <div style="">
      <div
        v-for="(item, index) in schema"
        :key="item.type + index"
        style="min-width: 90px"
        @keydown="onFormKeyDown"
      >
        <div style="overflow: hidden; margin-bottom: 15px">
          <div style="float: left; width: 20%; text-align: right">
            <div style="padding-right: 10px; font-size: 12px; color: #646464">
              {{ item.title }}
              <span v-if="item.required" style="color: red">*</span>
            </div>
          </div>
          <div style="float: left; width: 70%">
            <div
              v-if="item.type === 'input'"
              style="padding-right: 0; width: 100%"
            >
              <a-input
                v-model:value="formValue[item.enName]"
                size="small"
                allow-clear
                :placeholder="item.title"
                @change="handleChange"
              />
            </div>
            <div v-if="item.type === 'select'" style="padding-right: 0">
              <a-select
                ref="select"
                v-model:value="formValue[item.enName]"
                size="small"
                style="width: 50%"
                :placeholder="item.title"
                allow-clear
                @change="handleChange"
              >
                <a-select-option
                  v-for="option in item.options"
                  :key="option.value"
                  :value="option.value"
                >
                  {{ option.label }}
                </a-select-option>
              </a-select>
            </div>
            <div v-if="item.type === 'range'" style="padding-right: 0">
              <a-range-picker
                v-model:value="formValue[item.enName]"
                size="small"
                allow-clear
                :placeholder="[
                  item.title + '开始时间',
                  item.title + '结束时间',
                ]"
                @change="handleChange"
              />
            </div>
            <div v-if="item.type === 'date'" style="padding-right: 0">
              <a-date-picker
                v-model:value="formValue[item.enName]"
                :placeholder="'选择日期'"
                size="small"
                style="width: 50%"
                @change="handleChange($event, item.enName, item.type)"
              />
            </div>
            <div
              v-if="item.type === 'image' || item.type === 'images'"
              style="padding-right: 0"
            >
              <a-upload
                v-model:file-list="formValue[item.enName]"
                list-type="picture-card"
                :custom-request="beforeUpload"
                @change="handleChange($event, item.enName)"
                @preview="onPreview"
              >
                <div>
                  <div class="ant-upload-text">上传</div>
                </div>
              </a-upload>
            </div>
            <div v-if="item.type === 'richText'" style="padding-right: 0">
              <RichTextEditor
                :prop-content="formValue[item.enName]"
                @onRichTextChanged="onRichTextChanged($event, item.enName)"
              ></RichTextEditor>
            </div>
          </div>
          <div v-if="item.status" style="float: left; padding-left: 5px">
            <CheckCircleOutlined
              v-if="item.status === 'filled'"
              style="color: green"
            />
            <CloseCircleOutlined
              v-if="item.status === 'empty'"
              style="color: red"
            />
          </div>
        </div>
      </div>
    </div>
    <div style="padding-top: 10px; padding-left: 10px; text-align: right">
      <a-button
        size="small"
        type="primary"
        :loading="storeState.loading"
        @click="onFormBtnCLicked('确定')"
      >
        确定
      </a-button>
      <a-button
        size="small"
        type="normal"
        style="margin-left: 10px"
        :loading="storeState.loading"
        @click="onFormBtnCLicked('取消')"
      >
        取消
      </a-button>
    </div>
    <a-modal
      :visible="previewVisible"
      :footer="null"
      @cancel="previewVisible = !previewVisible"
    >
      <img alt="example" style="width: 100%" :src="previewImage" />
    </a-modal>
  </div>
</template>

<script lang="ts">
import { reactive, toRefs, watch, onMounted } from 'vue';
import { debounce } from 'lodash';
import { useStore } from 'vuex';
import { Upload, message } from 'ant-design-vue';
import {
  CheckCircleOutlined,
  CloseCircleOutlined,
} from '@ant-design/icons-vue';
import RichTextEditor from './rich-text-editor.vue';
import { uploadFile } from '../../utils/request';
import { formatDate, deepCopy } from '../../utils/common';
import { baseURL } from '../../utils/request';

export default {
  name: 'FormCreater',
  components: {
    'a-upload': Upload,
    RichTextEditor,
    CheckCircleOutlined,
    CloseCircleOutlined,
  },
  props: {
    schema: {
      // 表单配置
      type: Array,
      default: (): unknown[] => [],
    },
    buttons: {
      // 按钮配置
      type: Array,
      default: (): Array<unknown> => [],
    },
    propFormData: {
      type: Object,
      default: (): Record<string, string> => {
        return {};
      },
    },
  },
  // eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types
  setup(props, context) {
    let store = useStore();
    let storeState = store.state;
    console.log(storeState, 'storestate');
    const formData = reactive({
      previewImage: '',
      previewVisible: false,
      formValue: {},
      currentField: '',
    });
    const handleChange = function (val, enName, type) {
      console.log(formData.formValue, 'chagned');
      formData.currentField = enName;
      if (type === 'date' && formData.formValue[enName]) {
        formData.formValue[enName] = formatDate(formData.formValue[enName]);
      }
      checkInputStatus();
    };

    const onRichTextChanged = function (val, enName) {
      formData.formValue[enName] = val;
      checkInputStatus();
    };

    const checkInputStatus = function () {
      let allFilled = true;
      props.schema.forEach((_el) => {
        if (_el['required']) {
          if (!formData.formValue[_el.enName]) {
            _el['status'] = 'empty';
            allFilled = false;
          } else {
            _el['status'] = 'filled';
          }
        }
      });
      return allFilled;
    };

    const onFormBtnCLicked = debounce((type) => {
      // eslint-disable-next-line vue/require-explicit-emits
      let result = deepCopy(formData.formValue);
      if (type === '确定') {
        if (!checkInputStatus()) {
          message.error('请填写必填项');
          return;
        }
        props.schema.forEach((_el) => {
          if (
            (_el.type === 'image' || _el.type === 'images') &&
            result[_el.enName]
          ) {
            result[_el.enName] = result[_el.enName].map((_img) => _img.url);
          }
        });
      }
      if (props.propFormData && props.propFormData.id) {
        result['id'] = props.propFormData.id;
      }
      context.emit('onFormBtnCLicked', {
        data: result,
        type: type,
      });
    }, 500);

    const beforeUpload = function (event, enName) {
      console.log(event, enName);
      uploadFile(
        'online/explicit/upload',
        { file: event.file },
        {}, {},
        (res) => {
          let fileList = formData.formValue[formData.currentField];
          let _findIndex = fileList.findIndex((_el) => !_el.url);
          if (_findIndex > -1) {
            fileList.splice(_findIndex, 1);
          }
          res = baseURL + '/online/explicit/download?path=' + res;
          fileList.push({
            url: res,
            uid: 0 - fileList.length,
          });
          console.log(res);
        },
        (err) => {
          console.log(err);
        }
      );
    };

    const onPreview = function (file) {
      formData.previewVisible = !formData.previewVisible;
      formData.previewImage = file.url;
    };

    watch(
      () => props.propFormData,
      (newVal) => {
        // newval, preval
        console.log(newVal, 'propFromdata 123');
        formData.formValue = newVal;
        console.log(newVal);
      }
    );

    onMounted(() => {
      if (props.propFormData.id) {
        let copy = deepCopy(props.propFormData);
        let obj = {};
        props.schema.forEach((_el) => {
          if (
            (_el.type === 'image' || _el.type === 'images') &&
            copy[_el.enName]
          ) {
            copy[_el.enName] = copy[_el.enName].map((_img) => {
              return { url: _img };
            });
          }
          obj[_el.enName] = copy[_el.enName];
        });
        formData.formValue = obj;
      } else {
        formData.formValue = {};
      }
    });

    return {
      onPreview,
      onRichTextChanged,
      handleChange,
      beforeUpload,
      onFormBtnCLicked,
      storeState,
      ...toRefs(formData),
    };
  },
};
</script>
<style scoped lang="scss">
.tox-notifications-container {
  display: none !important;
}
</style>
