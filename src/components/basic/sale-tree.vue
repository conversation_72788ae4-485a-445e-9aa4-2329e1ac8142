<template>
  <a-tree-select
    :value="selectedData"
    :dropdown-style="{ maxHeight: '15.625vw', overflow: 'auto' }"
    :tree-checkable="treeCheckable"
    style="width: 280px"
    allow-clear
    placeholder="请选择"
    :max-tag-count="2"
    :tree-data="treeData"
    v-bind="$attrs"
    @change="handleValueChange"
  ></a-tree-select>
</template>

<script lang="ts" setup>
import {
  defineProps,
  withDefaults,
  onMounted,
  watchEffect,
  toRefs,
  defineEmits,
} from 'vue';
import type { TreeSelectProps } from 'ant-design-vue';
import { fetchSaleTree } from '@/server/api';
import { getGroupInTreeData } from '@/utils/common';
import { AutoSelectedMode } from '@/utils/const';

interface SaleTreeProps {
  /** 是否需要在本组件获取销售树数据，默认开启，配置为true则由本组件初始化treeData */
  needFetchData?: boolean;
  /** 自动选中的模式，默认为不自动选中，如果配置为选中自己/选中所在组则由本组件初始化selectedData */
  autoSelectedMode?: AutoSelectedMode;
  /** 是否为监管版本的合规树，区别在于接口地址不同，数据处理方式一致 */
  isSupervise?: boolean;
  /** false代表单选 / true代表多选，默认多选 */
  treeCheckable?: boolean;
  /** 如果不需要自动选中，该参数可不填；否则该id为需要选中的saleId或者groupId */
  selectedId?: number | null;
  /** 当前选中的列表，单选为number | null，多选为number[] */
  selectedData: number[] | number | null;
  /** 销售树的数据 */
  treeData: TreeSelectProps['treeData'];
}
// 给不必传的props定义默认值
const props = withDefaults(defineProps<SaleTreeProps>(), {
  // 可以开启needFetchData和autoSelectedMode，便捷地由本组件内部初始化treeData和selectedData；
  needFetchData: true,
  // 也可以关闭，在父组件中完成treeData和selectedData的初始化，用于执行一些特殊逻辑（例如需要在自动选中之后马上请求一次接口，由于初始化逻辑都在父组件中，很容易拿到这个第一次的时机，在正确的时机发送接口）
  autoSelectedMode: 0,
  isSupervise: false,
  treeCheckable: true,
  selectedId: null,
});
// 将props解构为响应式数据
const {
  needFetchData,
  autoSelectedMode,
  selectedId,
  isSupervise,
  treeCheckable,
  selectedData,
  treeData,
} = toRefs(props);
// 使用事件的方式通知父组件修改数据，避免直接修改传下来的props
const UPDATE_TREE_DATA_EVENT = 'update:treeData';
const UPDATE_SELECTED_DATA_EVENT = 'update:selectedData';
// defineEmits声明事件的时候，不能引用上面的变量，否则会引起编译错误
const emit = defineEmits(['update:treeData', 'update:selectedData']);
const init = async () => {
  if (needFetchData.value) {
    const saleTreeData = await fetchSaleTree(isSupervise.value);
    emit(UPDATE_TREE_DATA_EVENT, saleTreeData);
  }
};
// 监听回调中相关的响应式数据，任何数据改变都会重新执行
watchEffect(() => {
  switch (autoSelectedMode.value) {
    case AutoSelectedMode.None: {
      emit(UPDATE_SELECTED_DATA_EVENT, treeCheckable.value ? [] : null);
      break;
    }
    case AutoSelectedMode.Personal: {
      if (selectedId.value) {
        emit(
          UPDATE_SELECTED_DATA_EVENT,
          treeCheckable.value ? [selectedId.value] : selectedId.value
        );
      }
      break;
    }
    case AutoSelectedMode.Group: {
      // 仅当传入的id有效且树可多选时才执行
      if (selectedId.value && treeCheckable.value) {
        emit(
          UPDATE_SELECTED_DATA_EVENT,
          getGroupInTreeData(treeData.value, selectedId.value)
        );
      }
      break;
    }
    default: {
      // 什么也不做
    }
  }
});
const handleValueChange = (value: number | number[]) => {
  emit(UPDATE_SELECTED_DATA_EVENT, value);
};
onMounted(() => {
  init();
});
</script>
