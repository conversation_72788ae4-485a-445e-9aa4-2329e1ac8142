<template>
  <div class="tinymce-container">
    <input id="quillUplaoder" type="file" accept style="display: none" />
    <quill-editor
      v-model:value="content"
      theme="snow"
      :options="editorOption"
      @change="onEditorChange"
    ></quill-editor>
  </div>
</template>

<script lang="ts">
import { reactive, toRefs, watch, onMounted } from 'vue';
import { quillEditor } from 'vue3-quill';
import '@vueup/vue-quill/dist/vue-quill.snow.css';
import { uploadFile } from '../../utils/request';
import { debounce } from 'lodash';
import { baseURL } from '../../utils/request';

export default {
  name: 'FormCreater',
  components: {
    quillEditor,
  },
  props: {
    propContent: {
      type: Object,
      default: (): Record<string, unknown> => {
        return {};
      },
    },
  },
  // eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types
  setup(props, context) {
    const quillEditorData = reactive({
      content: '',
      editorOption: {
        placeholder: '请在这里输入',
        modules: {
          toolbar: {
            container: [
              ['bold', 'italic', 'underline', 'strike'], //加粗，斜体，下划线，删除线
              ['blockquote', 'code-block'], //引用，代码块
              [{ header: 1 }, { header: 2 }], // 标题，键值对的形式；1、2表示字体大小
              [{ list: 'ordered' }, { list: 'bullet' }], //列表
              [{ script: 'sub' }, { script: 'super' }], // 上下标
              [{ indent: '-1' }, { indent: '+1' }], // 缩进
              [{ direction: 'rtl' }], // 文本方向
              [{ size: ['small', false, 'large', 'huge'] }], // 字体大小
              [{ header: [1, 2, 3, 4, 5, 6, false] }], //几级标题
              [{ color: [] }, { background: [] }], // 字体颜色，字体背景颜色
              [{ font: [] }], //字体
              [{ align: [] }], //对齐方式
              ['clean'], //清除字体样式
              ['image', 'video'], //上传图片、上传视频
            ],
            handlers: {
              image: () => {
                onImageUplaod();
              },
            },
          },
        },
      },
    });

    const onImageUplaod = function () {
      let inputEl = document.getElementById('quillUplaoder');
      inputEl.click();
      inputEl.addEventListener('change', (e) => {
        let file = e.target['files'][0];
        if (file) {
          uploadFile(
            'online/explicit/upload',
            { file },
            {}, {},
            (res) => {
              console.log(res);
              if (res) {
              const fileUrl = baseURL + '/online/explicit/download?path=' + res;
                quillEditorData.content =
                  quillEditorData.content + `<img src="${fileUrl}"/>`;
              }
            },
            (err) => {
              console.log(err);
            }
          );
        }
      });
    };

    const onEditorChange = debounce(() => {
      context.emit('onRichTextChanged', quillEditorData.content);
    }, 500);

    onMounted(() => {
      if (props.propcontent) {
        quillEditorData.content = props.propcontent;
      }
    });

    watch(
      () => props.propContent,
      (newVal) => {
        // newval, preval
        quillEditorData.content = newVal;
        console.log(newVal, 'propcontent watch');
      }
    );

    return {
      onEditorChange,
      ...toRefs(quillEditorData),
    };
  },
};
</script>
<style lang="scss">
.tox-notification {
  display: none !important;
}
</style>
