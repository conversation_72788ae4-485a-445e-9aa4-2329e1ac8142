<template>
  <div class="g-sale-manager-title">
    <p></p>
    <span>{{ label }}</span>
  </div>
</template>
<script lang="ts">
import { defineComponent } from 'vue';
export default defineComponent({
  props: {
    label: {
      type: String,
    },
  },
});
</script>
<style lang="scss" scoped>
.g-sale-manager-title {
  display: flex;
  p {
    margin: 10px;
    width: 7px;
    height: 21px;
    background-color: #02a7f0;
  }
  span {
    font-size: 18px;
    line-height: 42px;
  }
}
</style>
