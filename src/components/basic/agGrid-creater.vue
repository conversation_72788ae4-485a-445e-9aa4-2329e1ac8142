<template>
  <div style="position: relative">
    <ag-grid-vue
      :id="propId"
      style="width: 100%; height: calc(100vh - 200px)"
      class="ag-theme-alpine"
      :header-height="headerHeight"
      :row-height="rowHeight"
      :column-defs="columnDefs"
      :row-data="rowData"
      side-bar="columns"
      :default-col-def="defaultColDef"
      :tooltip-show-delay="100"
      :locale-text="localeText"
      :row-selection="'multiple'"
      :on-cell-value-changed="onCellValueChanged"
      @column-resized="onColumnChanged"
      @column-moved="onColumnChanged"
      @selection-changed="onSelectionChanged"
      @grid-ready="onGridReady"
    ></ag-grid-vue>
    <div v-show="paginationShow" style="padding-top: 5px; text-align: right">
      <a-pagination
        v-model:current="currentPageNum"
        v-model:pageSize="currentPageSize"
        :page-size-options="propPageSizeList"
        size="small"
        :total="propTotalSize"
        :show-total="showTotal"
      />
    </div>
    <!-- 右侧列显隐功能 -->
    <a-popover placement="leftTop" trigger="click">
      <template #content>
        <div style="border-bottom: 1px solid #e9e9e9; padding-bottom: 10px">
          <a-checkbox
            v-model:checked="allSelected"
            @change="onCheckChange('all')"
          >
            全选
          </a-checkbox>
        </div>
        <br />
        <div>
          <div
            v-for="(sideBarItem, sideBarIndexs) of columnDefs"
            :key="sideBarItem.headerName"
          >
            <a-checkbox
              :checked="!sideBarItem.hide"
              @change="onCheckChange(sideBarIndexs)"
            >
              {{ sideBarItem.headerName }}
            </a-checkbox>
          </div>
        </div>
      </template>
      <div
        style="
          position: absolute;
          top: 0;
          right: -8px;
          color: red;
          cursor: pointer;
          overflow: hidden;
        "
      >
        <svg
          t="1636028312977"
          class="side-bar-icon"
          fill="#c8c8c8"
          viewBox="0 0 1024 1024"
          version="1.1"
          xmlns="http://www.w3.org/2000/svg"
          p-id="3078"
          width="26"
          height="26"
          style="transform: translateX(10px)"
        >
          <path
            d="M415.930119 223.790358c0-52.980346 43.003528-95.983874 95.983874-95.983874s95.983874 43.003528 95.983874 95.983874-43.003528 95.983874-95.983874 95.983874S415.930119 276.770704 415.930119 223.790358z"
            p-id="3079"
          ></path>
          <path
            d="M415.930119 511.741979c0-52.980346 43.003528-95.983874 95.983874-95.983874s95.983874 43.003528 95.983874 95.983874-43.003528 95.983874-95.983874 95.983874S415.930119 564.722325 415.930119 511.741979z"
            p-id="3080"
          ></path>
          <path
            d="M415.930119 799.865614c0-52.980346 43.003528-95.983874 95.983874-95.983874s95.983874 43.003528 95.983874 95.983874-43.003528 95.983874-95.983874 95.983874S415.930119 852.673946 415.930119 799.865614z"
            p-id="3081"
          ></path>
        </svg>
      </div>
    </a-popover>
  </div>
  <a-modal
    :width="750"
    :visible="modalVisible"
    :footer="null"
    @cancel="modalVisible = !modalVisible"
  >
    <!--eslint-disable-next-line vue/no-v-html -->
    <div style="overflow: hidden; margin-bottom: 10px">
      <!-- <a-button
        v-if="copyTextData !== ''"
        class="tag-read"
        :data-clipboard-text="copyTextData"
        type="primary"
        @click="copyText"
      >
        复制文本
      </a-button> -->
      <a-button type="primary" @click="copyTextImage">一键复制</a-button>
      <a-button style="float: right" type="primary" @click="exportAsImage">
        导出案例为图片
      </a-button>
    </div>
    <div style="height: 1px; overflow: hidden">
      <div id="copyTextImageId" ref="copyTextImageRef">
        <div>{{ copyTextData }}</div>
        <div v-for="(item, index) in imageDataArray" :key="index">
          <img :src="item" alt="" style="width: 700px" />
        </div>
      </div>
    </div>
    <div
      :id="currentDataId + '_richTextWindow'"
      style="background: #f0f0f0; padding: 10px"
    >
      <div class="rich-text-preview-wrapper" v-html="richTextContent"></div>

      <a-tag style="margin-top: 10px" color="red">
        风险提示：上述成功案例不代表对未来收益的保证，请勿据此操作。再次提醒您注意风险、理性投资。
      </a-tag>
    </div>
  </a-modal>
  <!-- 投顾直播记录查看图片 -->
  <a-modal
    :width="750"
    :visible="investModalVisible"
    :footer="null"
    @cancel="investModalVisible = !investModalVisible"
  >
    <div style="text-align: center">
      <img style="max-width: 700px" :src="investSrc" alt="" />
    </div>
  </a-modal>
</template>

<script lang="ts">
import 'ag-grid-community/dist/styles/ag-grid.css';
import 'ag-grid-community/dist/styles/ag-theme-alpine.css';

import { AgGridVue } from 'ag-grid-vue3';
import {
  watch,
  toRefs,
  reactive,
  onMounted,
  ref,
  defineComponent,
  PropType,
} from 'vue';
import { columnDefGenerator } from '../../utils/agGrid-service';
import { Popover, Pagination, Tag, message } from 'ant-design-vue';
import { useRouter } from 'vue-router';
import { useStore } from 'vuex';
import { localData, checkAuth } from '../../utils/common';
import domtoimage from 'dom-to-image';
import Clipboard from 'clipboard';
import clipboard from 'clipboard-all';
import { env } from '@/utils/request';
export default defineComponent({
  name: 'AggridCreater',
  components: {
    AgGridVue,
    'a-popover': Popover,
    'a-pagination': Pagination,
    'a-tag': Tag,
  },
  props: {
    headerHeight: {
      type: Number,
      default: () => 30,
    },
    rowHeight: {
      type: Number,
      default: () => 30,
    },
    paginationShow: {
      type: Boolean,
      default: () => true,
    },
    propPageNum: {
      type: Number,
      default: 0,
    },
    propPageSize: {
      type: Number,
      default: 0,
    },
    propTotalSize: {
      type: Number,
      default: 0,
    },
    customCol: {
      type: Boolean,
      default: true,
    },
    propId: {
      // 当前页面表格序号，从1开始
      type: String,
      default: '1',
    },
    propColumnDefs: {
      type: Array,
      default: (): unknown[] => [],
    },
    propRowData: {
      type: Array,
      default: (): unknown[] => [],
    },
    propPageSizeList: {
      type: Array as PropType<string[]>,
      default: () => ['5', '10', '20', '50'],
    },
    showTotal: {
      type: Function as PropType<(total: number, range: number[]) => unknown>,
      default: (total: number) => `共${total}条`,
    },
    onCellValueChanged: {
      type: Function as PropType<(e: any) => void>,
      default: (e: any) => {},
    },
    defaultColDef: {
      type: Object,
      default: () => ({
        resizable: true,
        sortable: true,
        filter: true,
      }),
    },
  },
  emits: [
    'onGridReady',
    'onGridCellActionTriggered',
    'onGridPageChange',
    'onGridSelectionChanged',
    'update:propPageNum',
    'update:propPageSize',
  ],
  // eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types
  setup(props, context) {
    let router = useRouter();
    let gridApi = null;
    let columnApi = null;
    let currentRoute = useRouter().currentRoute.value;
    let currentGridId = currentRoute.path + '_' + props.propId || '';
    let caseArrayObj = [];
    const store = useStore();
    let grid = reactive({
      currentDataId: 0,
      currentPageNum: 1,
      currentPageSize: 0,
      richTextContent: '',
      copyTextData: '',
      imageDataArray: [],
      modalVisible: false,
      investModalVisible: false,
      investSrc: '',
      localeText: {
        contains: '包含',
        notContains: '不包含',
        startsWith: '始于',
        endsWith: '结尾',
        andCondition: '和',
        orCondition: '或',
        equals: '等于',
        notEqual: '不等于',
      },
      allSelected: false,
      rowData: [],
      columnDefs: [],
      sideBar: null,
      // defaultColDef: {
      //   sortable: true,
      //   filter: true,
      // },
      caseArray: [],
    });
    // 选中row数据
    const onSelectionChanged = function () {
      let selectedRows = gridApi.getSelectedRows();
      context.emit('onGridSelectionChanged', {
        data: selectedRows,
      });
    };
    // 从本地缓存中读取数据
    const mapColmnDefs = function () {
      // debugger
      setTimeout(() => {
        let propCols = !props.customCol
          ? columnDefGenerator(props.propColumnDefs, store.state.fields)
          : [...props.propColumnDefs];
        const storedColConfig = localData.get(currentGridId);
        let columnDefs = [];
        if (storedColConfig) {
          storedColConfig.forEach((_el) => {
            let findIndex = propCols.findIndex(
              (_colItem) => _colItem['field'] === _el.field
            );
            if (findIndex > -1) {
              let find = propCols[findIndex];
              if (_el.width) {
                find['width'] = _el.width;
              }
              if (_el.hide) {
                find['hide'] = _el.hide;
              }
              columnDefs.push(find);
              propCols.splice(findIndex, 1);
            }
          });
          columnDefs = columnDefs.concat(
            propCols.map((_propColEl) => {
              return _propColEl
            })
          );
          grid.columnDefs = applyAuthToColumnDef(columnDefs);
        } else {
          grid.columnDefs = applyAuthToColumnDef(propCols);
        }
        addCellRendererToColumn();
        grid.allSelected = !columnDefs.find((_el) => _el.hide);
      }, 1000);
    };

    const addCellRendererToColumn = function () {
      // 为列定义添加模板
      grid.columnDefs.forEach((_el) => {
        if (_el.renderer) {
          switch (_el.renderer) {
            case 'btns': {
              _el['cellRenderer'] = (params) => {
                let rowData = params.node.data;
                let html = document.createElement('div');
                if (Array.isArray(_el.renderParams)) {
                  _el.renderParams.forEach((_el) => {
                    if (_el) {
                      let disabled = false;
                      if (_el.conditions) {
                        let _find = _el.conditions.find((_conditionEl) => {
                          if (_conditionEl.field === 'isExcellent') {
                            if (
                              rowData['status'] === '审核通过' &&
                              rowData[_conditionEl.field] === _conditionEl.value
                            ) {
                              return true;
                            } else {
                              return false;
                            }
                          } else {
                            return (
                              rowData[_conditionEl.field] === _conditionEl.value
                            );
                          }
                        });
                        if (_find) {
                          disabled = false;
                        } else {
                          disabled = true;
                        }
                      }
                      let _div = document.createElement('div');
                      _div.innerHTML = _el.name;
                      if (disabled) {
                        _div.style.cssText =
                          'float:left; color: #c8c8c8; margin-right: 10px; cursor:not-allowed';
                      } else {
                        _div.style.cssText =
                          'float:left; color: #1890ff; margin-right: 10px; cursor:pointer';
                        _div.addEventListener('click', () => {
                          cellBtnCLicked(_el, rowData);
                        });
                      }
                      html.appendChild(_div);
                    }
                  });
                }
                return html;
              };
              break;
            }
            case 'images':
            case 'richText': {
              _el['cellRenderer'] = (params) => {
                let rowData = params.node.data;
                if (
                  (Array.isArray(rowData[_el['field']]) &&
                    rowData[_el['field']].length > 0) ||
                  (!Array.isArray(rowData[_el['field']]) &&
                    rowData[_el['field']])
                ) {
                  let tempRichData = rowData.case;
                  let html = document.createElement('div');
                  tempRichData = tempRichData.replace(/<p>/g, '');
                  tempRichData = tempRichData.replace(/<\/p>/g, '');
                  tempRichData = tempRichData.replace(/<h2>/g, '');
                  tempRichData = tempRichData.replace(/<\/h2>/g, '');
                  tempRichData = tempRichData.replace(/<br>/g, '');
                  tempRichData = tempRichData.replace(
                    /<img src="(.*?)".*?>/g,
                    ''
                  );
                  if (_el['renderer'] === 'richText') {
                    html.innerHTML = tempRichData;
                    const caseArrayConfig = localData.get(caseArrayObj);
                    if (caseArrayConfig) {
                      if (
                        caseArrayConfig.find(function (x) {
                          return (
                            x.id === rowData.id &&
                            x.userId === rowData.localUserId
                          );
                        }) != undefined
                      ) {
                        html.style.cssText =
                          'float:left; color: gray; margin-right: 10px; cursor:pointer';
                      } else {
                        html.style.cssText =
                          'float:left; color: #1890ff; margin-right: 10px; cursor:pointer';
                      }
                    } else {
                      html.style.cssText =
                        'float:left; color: #1890ff; margin-right: 10px; cursor:pointer';
                    }
                  } else {
                    html.innerHTML = '查看';
                    html.style.cssText =
                      'float:left; color: #1890ff; margin-right: 10px; cursor:pointer';
                  }
                  html.addEventListener('click', (e) => {
                    gridApi.redrawRows(); // 点击后更新行数据
                    const caseArrayConfig = localData.get(caseArrayObj);
                    let caseObj = {
                      id: rowData.id,
                      userId: rowData.localUserId,
                    };
                    if (caseArrayConfig) {
                      if (
                        caseArrayConfig.find(function (x) {
                          return x.id === rowData.id;
                        }) != undefined
                      ) {
                        return
                      } else {
                        caseArrayConfig.push(caseObj);
                        localData.set(caseArrayObj, caseArrayConfig);
                      }
                    } else {
                      grid.caseArray.push(caseObj);
                      localData.set(caseArrayObj, grid.caseArray);
                    }
                    richTextRenderer(_el, rowData);
                  });
                  return html;
                }
              };
              break;
            }
            case 'list': {
              _el['wrapText'] = true;
              _el['autoHeight'] = true;
              _el['cellRenderer'] = (params) => {
                let value = params.value;
                let html = '';
                if (Array.isArray(value)) {
                  value.forEach((_it, _index) => {
                    html += `<div style="line-height: 20px;">${
                      _index + 1
                    }、${_it}</div>`;
                  });
                }
                return html;
              };
              break;
            }
            case 'link': {
              _el['cellRenderer'] = (params) => {
                let value = params.value;
                let html = '';
                let domainName = '';
                if (env === 'test') {
                  domainName = 'https://172.19.80.83:7443/gb_v3_test';
                } else if (env === 'prod' || env === 'pre') {
                  domainName = 'https://crm.10jqka.com.cn/gb_v3';
                }
                html += `<div onclick="window.location.href='${domainName}/default/account/clientsdetailsinformation?ai_id=${value}'" style="color: blue;cursor: pointer">${value}</div>`;
                return html;
              };
              break;
            }
            case 'increase': {
              _el['cellRenderer'] = (params) => {
                let value = params.value;
                let html = '';
                if (typeof value === 'number' && value >= 0) {
                  html += `<div style="color: red;text-align: right">${value}%</div>`;
                } else if (typeof value === 'number' && value < 0) {
                  html += `<div style="color: green;text-align: right">${value}%</div>`;
                }
                return html;
              };
              break;
            }
            case 'orderNumber': {
              _el['cellRenderer'] = (params) => {
                let html = '';
                html += `<div style="text-align: center">${
                  params.rowIndex + 1
                }</div>`;
                return html;
              };
              break;
            }
            case 'watchPersonNum': {
              _el['cellRenderer'] = (params) => {
                if (params.data.lookNums !== null) {
                  let html = document.createElement('div');
                  html.innerHTML = params.data.lookNums;
                  html.style.cssText =
                    'float:left; color: #1890ff; margin-right: 10px; cursor:pointer';
                  html.addEventListener('click', (e) => {
                    router.push({
                      path: '/community-operation/course-person',
                      query: {
                        resourceId: params.data.resourceId,
                      },
                    });
                  });
                  return html;
                }
              };
              break;
            }
            case 'addImage': {
              _el['cellRenderer'] = (params) => {
                // let html = '';
                let html = document.createElement('div');
                if (params.data.type && params.data.type === 3) {
                  html.innerHTML = '查看';
                  html.style.cssText =
                    'float:left; color: #1890ff; margin-right: 10px; cursor:pointer';
                  html.addEventListener('click', (e) => {
                    grid.investSrc = params.data.link;
                    grid.investModalVisible = true;
                  });
                } else {
                  html.innerHTML = '无';
                  // html += `<div>无</div>`;
                }
                return html;
              };
              break;
            }
          }
        }
      });
    };
    // 富文本渲染
    const richTextRenderer = function (e, rowData) {
      let field = e.field;
      let data = rowData[field];

      // 复制文本
      grid.copyTextData = '';
      let tempRichData = rowData[field];
      if (!(typeof tempRichData === 'object')) {
        tempRichData = tempRichData.replace(/<p>/g, '');
        tempRichData = tempRichData.replace(/<\/p>/g, '');
        tempRichData = tempRichData.replace(/<br>/g, '');
        tempRichData = tempRichData.replace(/<img src="(.*?)".*?>/g, '');
        grid.copyTextData = tempRichData;
      }
      if (e.renderer === 'images' && Array.isArray(data)) {
        let str = '';
        data.forEach((_el) => {
          str += `<div style="overflow:hidden; border-radius: 10px; margin-bottom: 10px;"><img src="${_el}"><div style="width: 100%;height:20px;text-align: center;cursor:pointer;color: rgb(24, 144, 255)" class="btn">复制上方图片</div></div>`;
        });
        data = str;
      } else if (
        Array.isArray(rowData['screenShots']) &&
        rowData['screenShots'].length > 0
      ) {
        grid.imageDataArray = rowData['screenShots'];
        let str = '';
        rowData['screenShots'].forEach((_el) => {
          str += `<div class="imgWrapper" style="overflow:hidden; border-radius: 10px; margin-bottom: 10px;"><img src="${_el}"><div style="width: 100%;height:20px;text-align: center;cursor:pointer;color: rgb(24, 144, 255)" class="btn">复制上方图片</div></div>`;
        });
        data += str;
      }
      grid.currentDataId = rowData.id;
      let _domId = Date.now().toString();
      grid.richTextContent = `<div id="${_domId}">${data}</div>`;
      grid.modalVisible = true;
      // 鼠标移入移出
      setTimeout(() => {
        let _dom = document.getElementById(_domId);
        _dom.addEventListener(
          'mouseenter',
          function (e) {
            let _btn: any = e.target;
            if (_btn && _btn.childNodes.length === 2 && _btn.childNodes[1]) {
              _btn.childNodes[1].style.display = 'block';
              _btn.childNodes[1].onclick = async function () {
                copyImg(_btn.childNodes[0].currentSrc);
              };
            }
          },
          true
        );
        // _dom.addEventListener(
        //   'mouseleave',
        //   function (e) {
        //     let _btn: any = e.target;
        //     if (_btn && _btn.childNodes.length === 2 && _btn.childNodes[1]) {
        //       // _btn.childNodes[1].style.display = 'none';
        //     }
        //   },
        //   true
        // );
      }, 100);
    };
    const copyTextImageRef = ref(null);
    const copyTextImage = async function () {
      let copyTextImageId = document.getElementById('copyTextImageId');
      // let res = await clipboard(copyTextImageId);
      let res = await clipboard(copyTextImageRef.value);
      if (res) {
        message.success('复制成功');
      }
    };
    const copyImg = async function (src) {
      const setToClipboard = (blob) => {
        const { ClipboardItem } = window;
        const data = [new ClipboardItem({ [blob.type]: blob })];
        return navigator.clipboard.write(data);
      };
      const xhr = new XMLHttpRequest();
      xhr.responseType = 'blob';
      xhr.onload = function () {
        if (xhr.status == 200 || xhr.readyState == 4) {
          const canvas = document.createElement('canvas');
          const ctx = canvas.getContext('2d');
          const img = new Image();
          var a = new FileReader();
          a.readAsDataURL(xhr.response);
          a.onload = function (e) {
            var baseUrl = e.target.result; //读取的结果在result中
            img.src = baseUrl as any;
            img.onload = function () {
              canvas.width = img.width;
              canvas.height = img.height;
              ctx.drawImage(img, 0, 0, img.width, img.height);
              canvas.toBlob((blob) => {
                setToClipboard(blob);
              });
            };
          };
        }
      };
      xhr.open('GET', src, true);
      xhr.send();
    };
    // 操作中按钮点击
    const cellBtnCLicked = function (e, rowData) {
      context.emit('onGridCellActionTriggered', {
        data: rowData,
        type: e.name,
      });
    };
    // 列改变
    const onColumnChanged = function (e) {
      if (!e.column) return;
      const storedColConfig = localData.get(currentGridId);
      const columnDefs = e.columnApi.columnModel.gridColumns;
      let arr = columnDefs.map((_el) => {
        let obj = {
          field: _el.colId,
        };
        if (storedColConfig) {
          let find = storedColConfig.find(
            (_localEl) => _localEl.field === _el.colId
          );
          if (find && find.width) {
            obj['width'] = find.width;
          }
          if (find && find.hide) {
            obj['hide'] = true;
          }
        }
        if (_el.colId === e.column.colId) {
          obj['width'] = e.column.actualWidth;
        }
        return obj;
      });
      localData.set(currentGridId, arr);
    };
    // 右侧列显隐功能
    const onCheckChange = (param) => {
      if (param === 'all') {
        grid.columnDefs.forEach((_el) => {
          _el.hide = !grid.allSelected;
        });
      } else {
        grid.columnDefs[param]['hide'] = !grid.columnDefs[param]['hide'];
      }
      let defs = grid.columnDefs.map((_el) => {
        let obj = {
          field: _el.field,
        };
        if (_el.width) {
          obj['width'] = _el.width;
        }
        if (_el.hide) {
          obj['hide'] = _el.hide;
        }
        return obj;
      });
      localData.set(currentGridId, defs);
      grid.allSelected = !grid.columnDefs.find((_el) => _el.hide);
    };

    const onGridReady = (params) => {
      gridApi = params.api;
      columnApi = params.columnApi;
      context.emit('onGridReady', gridApi, columnApi);
    };

    let checkAuthRef = null;
    const applyAuthToColumnDef = function (defs) {
      defs.forEach((_col) => {
        if (_col.renderParams) {
          let _arr = _col.renderParams.filter((_el) =>
            checkAuthRef(_el.authCode)
          );
          _col.renderParams = _arr;
        }
      });
      return defs;
    };
    // 分页
    const onPageChange = function () {
      context.emit('onGridPageChange', {
        pageSize: grid.currentPageSize,
        pageNum: grid.currentPageNum,
      });
    };
    // 复制文本
    const copyText = function () {
      let node = null;
      node = document.getElementById(grid.currentDataId + '_richTextWindow');
      var clipboard = new Clipboard('.tag-read');
      clipboard.on('success', (e) => {
        message.success('复制成功！');
      });
      clipboard.on('error', (e) => {
        message.error('该浏览器不支持复制');
      });
    };
    // 图片导出
    const exportAsImage = function () {
      let node = null;
      node = document.getElementById(grid.currentDataId + '_richTextWindow');
      try {
        domtoimage.toJpeg(node, { quality: 0.95 }).then(function (dataUrl) {
          var link = document.createElement('a');
          link.download = Date.now() + '.jpeg';
          link.href = dataUrl;
          link.click();
          message.success('图片导出成功！');
        });
      } catch (err) {
        message.error(err || '无法导出为图片');
      }
    };

    watch(
      () => props.propRowData,
      (newVal) => {
        if (newVal) {
          grid.rowData = newVal;
        }
      },
      {
        immediate: true,
      }
    );

    /** 监听props.propPageNum */
    watch(
      () => props.propPageNum,
      (newVal) => {
        if (newVal !== grid.currentPageNum) {
          grid.currentPageNum = newVal;
        }
      }
    );
    /** 监听props.propPageSize */
    watch(
      () => props.propPageSize,
      (newVal) => {
        if (newVal !== grid.currentPageSize) {
          grid.currentPageSize = newVal;
        }
      }
    );
    /** 监听gird.currentPageNum */
    watch(
      () => grid.currentPageNum,
      (newVal) => {
        if (newVal) {
          if (newVal !== props.propPageNum) {
            context.emit('update:propPageNum', newVal);
          }
          onPageChange();
        }
      }
    );

    /** 监听gird.currentPageSize */
    watch(
      () => grid.currentPageSize,
      (newVal) => {
        if (newVal !== props.propPageSize) {
          context.emit('update:propPageSize', newVal);
          if (grid.currentPageNum !== 1) {
            grid.currentPageNum = 1;
          } else {
            onPageChange();
          }
        }
      }
    );

    watch(
      () => props.propColumnDefs,
      (newVal) => {
        currentGridId = currentRoute.path + '_' + props.propId || '';
        grid.columnDefs = [];
        // debugger
        if (newVal) mapColmnDefs();
      }
    );

    watch(
      () => store.state.fields,
      () => {
        store.state.fields.length && mapColmnDefs();
      },
      {
        immediate: true,
      }
    );

    onMounted(() => {
      checkAuthRef = checkAuth();
      grid.currentPageNum = props.propPageNum;
      grid.currentPageSize = props.propPageSize;
      mapColmnDefs();
    });

    return {
      copyText,
      exportAsImage,
      onPageChange,
      onColumnChanged,
      onCheckChange,
      onGridReady,
      onSelectionChanged,
      copyTextImage,
      copyTextImageRef,
      ...toRefs(grid),
    };
  },
});
</script>
<style lang="scss" scopeds>
@import '~ag-grid-community/src/styles/ag-grid.scss';
@import '~ag-grid-community/src/styles/ag-theme-alpine/sass/ag-theme-alpine-mixin';
.ag-header-viewport {
  background: #e1eaf1 !important;
  font-size: 14px !important;
}

.ag-theme-alpine .ag-header-cell,
.ag-theme-alpine .ag-header-group-cell {
  padding-left: 6px;
  padding-right: 6px;
}

.ag-theme-alpine .ag-cell,
.ag-theme-alpine .ag-full-width-row .ag-cell-wrapper.ag-row-group {
  padding-left: 6px;
  padding-right: 6px;
  line-height: 28px;
  font-size: 14px;
}

.rich-text-preview-wrapper {
  img {
    width: 100%;
  }
}
</style>
