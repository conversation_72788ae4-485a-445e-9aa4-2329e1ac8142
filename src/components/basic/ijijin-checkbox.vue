<!-- 多选框组件 -->
<template>
  <a-checkbox
    style="margin-right: 4px"
    v-model:checked="checkAll"
    :indeterminate="indeterminate"
    @change="onCheckAllChange"
  >
    全部
  </a-checkbox>
  <a-checkbox-group v-model:value="checkedList" :options="options" />
</template>
<script lang="ts">
import { defineComponent, reactive, toRefs, watch, toRaw } from 'vue';

type plainOptionsItem = {
  name: string;
  value: string;
};

export default defineComponent({
  emits: ['onTransmission'],
  props: {
    name: {
      type: String,
      required: true,
    },
    options: {
      type: Array,
      required: true,
    },
    // 通过控制ifNeedTXData将数据通过onTransmission事件传输到父组件
    ifNeedTXData: {
      type: Boolean,
      required: true,
    },
  },
  setup(props, context) {
    const { name, options, ifNeedTXData } = toRefs(props);
    const plainOptions = options.value;
    const state = reactive({
      indeterminate: false,
      checkAll: false,
      checkedList: [],
    });
    // 全选
    const onCheckAllChange = (e: any) => {
      let newPlainOptions = [];
      plainOptions.map((item: plainOptionsItem) => {
        newPlainOptions.push(item.value);
      });
      Object.assign(state, {
        checkedList: e.target.checked ? newPlainOptions : [],
        indeterminate: false,
      });
    };
    // 控制全选/半选样式
    watch(
      () => state.checkedList,
      (val) => {
        state.indeterminate = !!val.length && val.length < plainOptions.length;
        state.checkAll = val.length === plainOptions.length;
      }
    );
    // 传输数据的时机
    watch(
      () => ifNeedTXData.value,
      () => {
        context.emit('onTransmission', {
          name: name.value,
          value: toRaw(state.checkedList),
        });
      }
    );
    return {
      ...toRefs(state),
      onCheckAllChange,
    };
  },
});
</script>
