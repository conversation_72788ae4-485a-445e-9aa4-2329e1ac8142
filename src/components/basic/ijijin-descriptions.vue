<!-- 描述列表组件  -->
<template>
  <a-descriptions :title="mainTitle" :column="column">
    <a-descriptions-item
      v-for="(item, key) in enumInfo"
      :label="key"
      :key="item"
    >
      {{ dataSource[item] }}
    </a-descriptions-item>
    <slot></slot>
  </a-descriptions>
</template>
<script lang="ts">
import { defineComponent } from 'vue';
export default defineComponent({
  props: ['mainTitle', 'column', 'dataSource', 'enumInfo'],
});
</script>
