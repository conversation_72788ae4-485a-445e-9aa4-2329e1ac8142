<template>
  <div class="product-detail">
    <div
      v-for="item in detailModal"
      :key="item.key"
      :class="`detail-item m-item-${item.key}`"
    >
      <!-- 产品代码 -->
      <template v-if="item.editKey === 'code'">
        <span class="label">{{ item.label }}：</span>
        <div class="value text">
          <a-input
            v-model:value="data[item.key]"
            :bordered="false"
            :disabled="option !== 'add'"
            @change="handleCodeChange"
          />
        </div>
      </template>
      <!-- 内部推荐等级 -->
      <template v-else-if="item.editKey === 'inter_recommend_level'">
        <span class="label">{{ item.label }}：</span>
        <div class="value text">
          <a-select
            v-model:value="data[item.key]"
            size="small"
            :disabled="option === 'scan'"
            allow-clear
          >
            <a-select-option
              v-for="id in Object.keys(recommandLevel)"
              :key="id"
              :value="Number(id)"
            >
              {{ recommandLevel[id] }}
            </a-select-option>
          </a-select>
        </div>
      </template>
      <!-- 销售佣金 && 最低持有期 -->
      <template
        v-else-if="
          item.editKey === 'sale_commission' ||
          item.editKey === 'lowest_hold_day'
        "
      >
        <span class="label">{{ item.label }}：</span>
        <div class="value text">
          <a-input
            v-model:value="data[item.key]"
            :bordered="false"
            :disabled="option === 'scan'"
            @change="(e) => handleDataChange(e, item.key)"
          />
        </div>
      </template>
      <!-- 2022.5.6 是否参加万元户活动 -->
      <template v-else-if="item.editKey === 'relatedInfo'">
        <p>{{ item.label }}：</p>
        <div
          v-for="(activity, index) in saleActivityList || []"
          :key="activity.taa_id"
        >
          <span class="label">{{ activity.activity_name }}：</span>
          <span class="value">
            <a-radio-group
              v-model:value="data[item.key][index].is_valid"
              :disabled="option === 'scan'"
            >
              <a-radio :value="0">不参加</a-radio>
              <a-radio :value="1">参加</a-radio>
            </a-radio-group>
          </span>
        </div>
      </template>
      <!-- 销售话术 -->
      <template v-else-if="item.editKey === 'sale_words'">
        <span class="label textarea">{{ item.label }}：</span>
        <div class="value">
          <a-textarea
            v-model:value="data[item.key]"
            :auto-size="true"
            :disabled="option === 'scan'"
          />
        </div>
      </template>
      <!-- 买入/卖出规则 -->
      <template v-else-if="item.editKey === 'rule'">
        <span class="label textarea">{{ item.label }}：</span>
        <div
          :class="{ 'value table edit-rule': true, edit: option !== 'scan' }"
        >
          <div v-for="col in item.columns" :key="col.key" class="table-col">
            <span class="col-title">{{ col.title }}</span>
            <span
              v-for="(_data, index) in data[item.key]"
              :key="index"
              class="col-cell"
            >
              <span v-if="col.key === 'item'">
                {{ col.map[_data[col.key]] }}
              </span>
              <a-input
                v-else
                v-model:value="data[item.key][index].time"
                :bordered="false"
                :disabled="option === 'scan'"
              />
            </span>
          </div>
        </div>
      </template>
      <!-- 文字项 -->
      <template v-else-if="item.value === 'text'">
        <span class="label">{{ item.label }}：</span>
        <div class="value text">
          <a-input
            :value="item.map ? item.map[data[item.key]] : data[item.key]"
            :bordered="false"
            :disabled="true"
          />
        </div>
      </template>
      <!-- 多行文字项 -->
      <template v-else-if="item.value === 'textarea'">
        <span class="label">{{ item.label }}：</span>
        <div class="value textarea">
          <a-textarea
            :value="data[item.key]"
            :auto-size="true"
            :disabled="true"
          />
        </div>
      </template>
      <!-- 表格项 -->
      <template v-else-if="item.value === 'table'">
        <span class="label">{{ item.label }}：</span>
        <div class="value table">
          <div v-for="col in item.columns" :key="col.key" class="table-col">
            <span class="col-title">{{ col.title }}</span>
            <span
              v-for="(_data, index) in data[item.key]"
              :key="index"
              class="col-cell"
            >
              {{ col.map ? col.map[_data[col.key]] : _data[col.key] }}
            </span>
          </div>
        </div>
      </template>
    </div>
  </div>
</template>

<script lang="ts">
import { watch, defineComponent, reactive, toRefs, PropType } from 'vue';
import { transDetailData, transPostDetail } from './api';
import { message } from 'ant-design-vue';
import { debounce } from 'lodash';

export default defineComponent({
  name: 'ProductDetail',
  props: {
    code: {
      type: String,
      default: () => '',
    },
    checked: {
      type: Boolean,
      default: () => false,
    },
    visible: {
      type: Boolean,
      default: () => false,
    },
    type: {
      type: String,
      default: () => '',
    },
    detailModal: {
      type: Object,
      default: () => ({}),
    },
    detailData: {
      type: Object as PropType<any>,
      default: () => ({}),
    },
    option: {
      type: String,
      default: () => '',
    },
    saleActivityList: {
      type: Array as PropType<
        {
          taa_id: number;
          activity_name: string;
        }[]
      >,
      default: () => [],
    },
    recommandLevel: {
      type: Object,
      default: () => {},
    },
    submit: {
      type: Boolean,
      default: () => false,
    },
    onSubmit: {
      type: Function,
      default: () => () => {},
    },
  },
  emits: ['update:checked', 'update:submit', 'update:code'],
  setup(props, { emit }) {
    const state = reactive<{
      data: { [x: string]: any };
    }>({
      data: {},
    });

    /** watch */
    watch(
      () => [props.detailData, props.type, props.visible],
      ([data, type, visible]) => {
        try {
          if (visible) {
            if (props.option === 'edit') {
              emit('update:checked', true);
            }
            state.data = {
              relatedInfo: props.saleActivityList.map((item) => {
                return {
                  ...item,
                  is_valid: 0,
                };
              }),
              ...transDetailData(data, type),
            };
          }
        } catch (e) {
          console.error(e);
        }
      }
    );
    watch(
      () => props.submit,
      (submit, prevSubmit) => {
        if (submit && !prevSubmit) {
          const post = transPostDetail(
            { ...props.detailData },
            state.data,
            props.type
          );
          post.relatedInfo = state.data.relatedInfo;
          console.log('props.submit', post);
          emit('update:submit', false);
          if (post) props.onSubmit(post);
          else message.error('请正确填写所有必填项！');
        }
      }
    );
    // methods
    const handleCodeChange = (e) => {
      emit('update:code', e.target.value);
    };
    /**
     * 单项数据校验
     * @param e event
     * @param key 表单项key
     */
    const handleDataChange = debounce((e, key: string) => {
      let _input = e.target.value;
      if (key === 'sale_commission' || key === 'lowest_hold_day') {
        var reg = new RegExp(
          `^([0-9]+\\.[0-9]+)?[0-9]*${key === 'sale_commission' ? '%' : '天'}?$`
        );
        if (!reg.test(_input)) {
          message.warning(`请填写正确的数据！`);
          emit('update:checked', false);
        } else {
          emit('update:checked', true);
        }
      }
    }, 500);

    return { ...toRefs(state), handleCodeChange, handleDataChange };
  },
});
</script>

<style lang="less" scoped>
@import './index.less';
</style>
