.product-detail {
  width: 1400px;
  max-height: 720px;
  display: flex;
  flex-flow: column wrap;
  justify-content: flex-start;
  align-content: flex-start;
  align-items: flex-start;
  .detail-item {
    padding: 8px 16px;
    display: flex;
    border-left: 1px #cccccc55 solid;
    .label {
      width: 144px;
      margin-right: 12px;
      font-size: 16px;
      line-height: 22px;
    }
    .value {
      &.text {
        max-width: 150px;
        font-size: 12px;
        line-height: 16px;
      }
      &.table {
        display: flex;
        .table-col {
          display: flex;
          flex-flow: column nowrap;
          &:nth-last-child(1) {
            text-align: end;
          }
          > * {
            padding: 2px 12px;
            color: rgba(0, 0, 0, 0.4);
          }
          .col-title {
            border-bottom: 1px #cccccc solid;
          }
          .col-cell {
            height: 18px;
            font-size: 12px;
            line-height: 18px;
          }
        }
      }
      &.edit-rule {
        &.edit > .table-col > * {
          color: #000;
        }
        input {
          width: 50px;
          height: 18px;
          padding: 0;
          font-size: 12px;
          line-height: 18px;
          text-align: end;
        }
      }
      input {
        height: 22px;
        border-bottom: solid 1px #ccc;
        &[class*='disabled'] {
          color: rgba(0, 0, 0, 0.4);
          border-bottom: none;
        }
      }
      input,
      textarea {
        width: 260px;
      }
      textarea {
        min-height: 60px;
        max-height: 180px !important;
      }
    }
    &.m-item-inter_recommend_level > .value > div {
      width: 60px;
    }
    &.m-item-relatedInfo {
      flex-flow: column;
      > p {
        font-size: 16px;
        margin-bottom: 0;
      }
      > div .label {
        padding-right: 32px;
        display: inline-block;
        text-align: right;
        color: #999;
        font-size: 14px;
      }
    }
  }
}