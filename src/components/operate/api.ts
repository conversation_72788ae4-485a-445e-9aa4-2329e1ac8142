import PARAMS from '@/views/operate/assets/const';

const default_buy_rule = [
  { item: 'buy_commit', time: '' },
  { item: 'buy_confirm_share', time: '' },
  { item: 'select_income', time: '' },
];
const default_sell_rule = [
  { item: 'sell_commit', time: '' },
  { item: 'sell_confirm_share', time: '' },
  { item: 'funds_arrive', time: '' },
];

// 转换详情弹窗数据
export const transDetailData: (...data) => any = (data: any, type: string) => {
  const _data = JSON.parse(JSON.stringify(data));
  if (Object.keys(_data).length === 0) {
    if (type === 'gm') {
      _data.buy_rule = JSON.parse(JSON.stringify(default_buy_rule));
      _data.sell_rule = JSON.parse(JSON.stringify(default_sell_rule));
      return _data;
    }
    return {};
  }
  _data.lowest_hold_day = _data.lowest_hold_day
    ? `${_data.lowest_hold_day}天`
    : '';
  _data.sale_commission = _data.sale_commission
    ? _data.sale_commission + '%'
    : '';
  switch (type) {
    case 'gdlc': {
      const profit_info = Object.keys(_data.profit_info);
      const hs_profit_info = Object.keys(_data.hs_profit_info);
      (_data.anti_risk_capacity || []).forEach((item) => {
        item.rate += '%';
        item.max_draw_down += '%';
      });

      _data.profit_info = profit_info.map((item) => ({
        time: item,
        syValue: (_data.profit_info[item] || '--') + '%',
      }));
      _data.hs_profit_info = hs_profit_info.map((item) => ({
        time: item,
        syValue: (_data.hs_profit_info[item] || '--') + '%',
      }));

      console.log(_data);
      return _data;
    }
    case 'sm': {
      _data.sales_rate += '%';
      _data.manage_rate += '%';
      _data.subsce_rate += '%';
      _data.perfm_accrual_rate += '%';
      _data.purchase_rate += '%';

      return _data;
    }
    case 'gm': {
      const ruleItem = (rule) => {
        const _rule = { ...rule };
        _rule.time = _data[rule.item];
        return _rule;
      };
      _data.fund_invest_type = PARAMS.FUND_TYPE[_data.fund_invest_type];
      _data.custody_fee += '%';
      _data.manage_fee += '%';
      _data.sales_serv_fee += '%';
      _data.if_new = _data.if_new === 1 ? '新发' : '非新发';
      _data.buy_rule = default_buy_rule.map(ruleItem);
      _data.sell_rule = default_sell_rule.map(ruleItem);
      _data.buy_fee_rate = (_data.front_fee_rate || {}).buy_fee_rate_list;
      _data.sell_fee_rate = (_data.front_fee_rate || {}).sell_fee_rate_list;
      _data.operate_fee_rate = [
        { type: 'manage_fee', value: _data.manage_fee },
        { type: 'custody_fee', value: _data.custody_fee },
        { type: 'sales_serv_fee', value: _data.sales_serv_fee },
      ];
      return _data;
    }
  }
};
// 转换详情数据发送
export const transPostDetail: (...data) => any = (
  props: any,
  data: any,
  type: string
) => {
  const empty = ['null%', '--%', '--', 'null', '', '%', null, undefined];
  // 非空校验
  const {
    // 共有
    inter_recommend_level,
    sale_commission,
    lowest_hold_day,
    // 单独
    sale_words,
    buy_rule,
    sell_rule,
  } = data;
  if (
    ~empty.indexOf(inter_recommend_level) ||
    ~empty.indexOf(sale_commission) ||
    ~empty.indexOf(lowest_hold_day)
  )
    return false;

  const _data = {
    ...props,
    inter_recommend_level: inter_recommend_level,
    sale_commission: Number(sale_commission.replace('%', '')),
    lowest_hold_day: lowest_hold_day.replace(/天/g, ''),
  };
  switch (type) {
    case 'gdlc':
    case 'sm': {
      if (!sale_words) return false;
      _data.sale_words = sale_words;
      return _data;
    }
    case 'gm': {
      let checked = true;
      buy_rule.forEach((rule) => {
        _data[rule.item] = rule.time;
        checked = checked && rule.time;
      });
      sell_rule.forEach((rule) => {
        _data[rule.item] = rule.time;
        checked = checked && rule.time;
      });
      if (!checked) return false;
      return _data;
    }
  }
};
