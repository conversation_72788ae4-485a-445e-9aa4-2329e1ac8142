<template>
  <span v-for="(item, index) in menuChildren" :key="index">
    <a-menu-item
      v-if="!item.children"
      :key="item.name + 'index'"
      @click="gotoUrl(item.name)"
    >
      <template #icon>
        <FileTextOutlined />
      </template>
      {{ item.name }}
    </a-menu-item>
    <a-sub-menu v-if="item.children" :key="item.name + 'index'">
      <template #icon>
        <FileTextOutlined />
      </template>
      <template #title>
        {{ item.name }}
      </template>
      <MenuTreeItem :menu-children="item.children"></MenuTreeItem>
    </a-sub-menu>
  </span>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import { FileTextOutlined } from '@ant-design/icons-vue';
import { useRouter } from 'vue-router';

export default defineComponent({
  name: 'MenuTreeItem',
  components: { FileTextOutlined },
  props: {
    menuChildren: {
      type: Array,
      default: () => [],
    },
  },
  setup(props) {
    console.log(props.menuChildren, 'props');
    let router = useRouter();
    const gotoUrl = (name) => {
      console.log(name);
      router.push({ name: name });
    };
    return {
      gotoUrl,
    };
  },
});
</script>

<style lang="scss" scoped></style>
