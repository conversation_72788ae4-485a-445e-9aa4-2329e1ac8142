<template>
  <a-layout style="height: 100vh">
    <a-layout-header class="header" style="height: 40px; padding: 0 20px">
      <div style="width: 100%; height: 100%; overflow: hidden">
        <div style="float: left; line-height: 40px; color: white">
          {{ currentRoute.value.meta.title }}
        </div>
        <div
          style="float: right; color: white; line-height: 36px; cursor: pointer"
          @click="dialog.visible = !dialog.visible"
        >
          设置cookie
        </div>
      </div>
    </a-layout-header>
    <a-layout>
      <a-layout-sider width="200" style="background: #fff">
        <MenuTree></MenuTree>
      </a-layout-sider>
      <a-layout style="padding: 10px">
        <a-layout-content
          :style="{
            background: '#fff',
            padding: '10px',
            margin: 0,
            minHeight: '280px',
          }"
        >
          <router-view></router-view>
        </a-layout-content>
      </a-layout>
    </a-layout>
  </a-layout>

  <a-modal
    v-model:visible="dialog.visible"
    :width="400"
    :title="dialog.title"
    :top="10"
    destroy-on-close
    :closable="true"
    :footer="null"
  >
    <div style="margin-top: 10px">
      <div style="margin: 10px 0">
        <a-textarea v-model:value="dialog.cookie" :rows="10"></a-textarea>
      </div>
      <a-button @click="saveCookie">保存</a-button>
    </div>
  </a-modal>
</template>

<script lang="ts">
import { defineComponent, ref, computed, reactive } from 'vue';
import { Layout, LayoutHeader } from 'ant-design-vue';
import MenuTree from './menu-tree.vue';
import { useRouter } from 'vue-router';

export default defineComponent({
  name: 'Layout',
  components: {
    MenuTree: MenuTree,
    'a-layout-header': LayoutHeader,
    [Layout.name]: Layout,
    [Layout.Content.name]: Layout.Content,
    [Layout.Sider.name]: Layout.Sider,
  },
  setup() {
    const collapsed = ref<boolean>(false);
    const asiderWidth = computed(() => (collapsed.value ? 80 : 208));
    let currentRoute: Record<string, unknown> = reactive({ name: '' });
    let routerObj = useRouter();
    let dialog = reactive({
      visible: false,
      title: '设置cookie',
      cookie: '',
    });
    currentRoute.value = routerObj.currentRoute.value;

    const saveCookie = function () {
      console.log(dialog.cookie);
      let _split = dialog.cookie.split(';');
      if (dialog.cookie) {
        _split.forEach((_el) => {
          document.cookie = _el;
        });
      }
      dialog.visible = false;
    };
    return {
      saveCookie,
      dialog,
      currentRoute,
      collapsed,
      asiderWidth,
    };
  },
});
</script>

<style lang="scss" scoped></style>
