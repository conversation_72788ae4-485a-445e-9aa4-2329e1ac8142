<template>
  <a-menu
    mode="inline"
    :default-selected-keys="['1']"
    :default-open-keys="['sub1']"
    :style="{ height: '100%', borderRight: 0 }"
    @select="onMenuSelected"
  >
    <MenuTreeItem :menu-children="routeList"></MenuTreeItem>
  </a-menu>
</template>

<script lang="ts">
import { defineComponent, onMounted, reactive, toRefs } from 'vue';
import { useRouter } from 'vue-router';
import MenuTreeItem from './menu-tree-item.vue';

export default defineComponent({
  name: 'MenuTree',
  components: { MenuTreeItem },
  props: {
    collapsed: {
      type: Boolean,
    },
  },
  emits: ['update:collapsed'],
  setup() {
    let routes: Record<string, unknown> = reactive({ routeList: [] });
    let routerObj = useRouter();
    routes.routeList = routerObj.options.routes;

    const onMenuSelected = (param) => {
      console.log(param, 'selected');
    };

    onMounted(() => {
      console.log(routes.routeList, 'route');
    });
    return { ...toRefs(routes), onMenuSelected };
  },
});
</script>

<style lang="scss" scoped></style>
