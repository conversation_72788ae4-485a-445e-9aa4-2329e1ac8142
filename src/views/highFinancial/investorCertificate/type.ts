export type ReconListTableData = {
  index: number; // 序号
  key: string;
  operate_asset: string; // 操作[合格投资者]
  operate_proinvestor: string; // 操作[专业投资者]【优先审核】
  cust_name: string; // 客户姓名
  id_card: string; // 身份证号
  cust_id: string; // 客户号
  new_asset_photos: string[]; // 资产收入证明(最新)
  new_investment_photos: string[]; // 投资经历证明(最新)
  asset_photos: string[]; // 资产收入证明
  investment_photos: string[]; // 投资经历证明
  asset_flag: string; // 资产认证标识标识[合格投资者]
  proinvestor_flag: string; // 资产认证标识标识[专业投资者]
  mentionProfit: string; // 不通过原因
  mtime: string; // 修改时间
  order_infos: string[]; // 投资经历订单信息
};

export enum AssetFlag {
  No_Approve = 0,
  ToBe_Audit,
  Approved,
  Approved_fail,
}

export enum ProinvestorFlag {
  No_Approve = 0,
  ToBe_Audit,
  Approved,
  Approved_fail,
}

export enum OTHER_REASON_ID {
  Local = '1370',
  PRO = '1384',
}
