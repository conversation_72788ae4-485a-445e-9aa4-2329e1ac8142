import { message } from 'ant-design-vue';
import { reactive } from 'vue';

import ajax from '@/server/http';
import { ReconListTableData, OTHER_REASON_ID } from './type';
import { localTest } from '@/server/api';

export function auditHook(refresh) {
  const auditState = reactive<{
    reasonVisible: boolean;
    reasonOptions: {
      param_code: string;
      param_name: string;
      disabled: boolean;
    }[];
    selectReason: Array<string>;
    otherReason: string;
    selectedList: ReconListTableData[];
    operateType: string;
  }>({
    reasonVisible: false,
    reasonOptions: [],
    selectReason: [],
    otherReason: '',
    selectedList: [],
    operateType: '',
  });

  const init = () => {
    ajax('get_investors_reasons', { parent_code: 1374 }).then((res) => {
      if (res) {
        res.forEach((item) => (item.disabled = false));
        auditState.reasonOptions = res || [];
      } else {
        auditState.reasonOptions = [];
      }
    });
  };

  const handleOpenReasonModal = (
    record: ReconListTableData[],
    flag: string
  ) => {
    auditState.reasonVisible = true;
    auditState.selectReason = [];
    auditState.otherReason = '';
    auditState.selectedList = record;
    auditState.operateType = flag;
  };
  const handleAudit = (
    record: ReconListTableData[],
    flag: string,
    audit: string
  ) => {
    const cust_info_list = record.reduce((pre, cur) => {
      const obj = {
        cust_id: cur.cust_id,
        order_infos: cur.order_infos,
        new_asset_photos: cur.new_asset_photos,
        new_investment_photos: cur.new_investment_photos,
        asset_photos: cur.asset_photos,
        investment_photos: cur.investment_photos,
        asset_flag: cur.asset_flag,
        proinvestor_flag: cur.proinvestor_flag,
      };
      if (flag === 'proinvestor') {
        if (audit === 'pass') {
          obj.proinvestor_flag = '2';
          obj.asset_flag = '2';
        } else {
          obj.proinvestor_flag = '3';
        }
      } else {
        if (audit === 'noPass') {
          obj.proinvestor_flag = '3';
          obj.asset_flag = '3';
        } else {
          obj.asset_flag = '2';
          obj.proinvestor_flag = '0';
        }
      }
      return pre.concat({ ...obj });
    }, []);
    let fail_reason = '';
    if (audit === 'noPass') {
      const intersectionArr = auditState.reasonOptions.filter(function (val) {
        return auditState.selectReason.indexOf(val.param_code) > -1;
      });
      if (intersectionArr.length === 0) {
        message.error('请选择备注内容');
        return;
      }
      const otherReasonId = localTest
        ? OTHER_REASON_ID['Local']
        : OTHER_REASON_ID['PRO'];
      if (intersectionArr[0].param_code === otherReasonId) {
        fail_reason = auditState.otherReason.replace(/^\s+|\s+$/g, '');
        if (fail_reason === '') {
          message.error('请填写其他原因');
          return;
        }
      } else {
        fail_reason = intersectionArr.map((item) => item.param_name).join('; ');
      }
    }
    const params = {
      cust_info_list,
      fail_reason,
    };
    ajax('investors_check', params).then((res) => {
      if (res) {
        auditState.reasonVisible = false;
        refresh();
        message.success('操作成功');
      }
    });
  };
  const handleNoPassConform = () => {
    handleAudit(auditState.selectedList, auditState.operateType, 'noPass');
  };
  const handlePassConform = (record: ReconListTableData[], flag: string) => {
    handleAudit(record, flag, 'pass');
  };

  // 保证最多可以选3个，如果有其他原因只能单选
  const handleChangeReason = () => {
    const otherReasonId = localTest
      ? OTHER_REASON_ID['Local']
      : OTHER_REASON_ID['PRO'];
    if (auditState.selectReason.length === 0) {
      auditState.reasonOptions.forEach((item) => {
        item.disabled = false;
      });
      return;
    }
    if (
      auditState.selectReason.length === 1 ||
      auditState.selectReason.length === 2
    ) {
      if (auditState.selectReason[0] === otherReasonId) {
        auditState.reasonOptions.forEach((item) => {
          item.disabled = item.param_code !== otherReasonId;
        });
        return
      }

      auditState.reasonOptions.forEach((item) => {
        item.disabled = item.param_code === otherReasonId;
      });
      return;
    }
    if (auditState.selectReason.length >= 3) {
      auditState.reasonOptions.forEach((item) => {
        if (auditState.selectReason.indexOf(item.param_code) <= -1) {
          item.disabled = true;
        }
      });
      return;
    }
  };
  init();

  return {
    auditState,
    handleOpenReasonModal,
    handleNoPassConform,
    handlePassConform,
    handleChangeReason,
  };
}
