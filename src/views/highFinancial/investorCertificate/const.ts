import { AssetFlag, ProinvestorFlag } from './type';

const PAGE_SIZE = 20;
const PAGE_SIZE_LIST = ['10', '20', '50', '100'];
const COLUMNS = [
  {
    dataIndex: 'index',
    title: '序号',
    width: 80,
  },
  {
    dataIndex: 'operate_asset',
    title: '操作[合格投资者]',
    width: 255,
    slotName: 'detail1',
  },
  {
    dataIndex: 'operate_proinvestor',
    title: '操作[专业投资者]【优先审核】',
    width: 225,
    slotName: 'detail2',
  },
  {
    dataIndex: 'cust_name',
    title: '客户名称',
    width: 260,
    slotName: 'custname',
  },
  {
    dataIndex: 'cust_id',
    title: '客户号',
    width: 160,
  },
  {
    dataIndex: 'new_investment_photos',
    title: '投资经历证明[最新]',
    width: 450,
    slotName: 'imgOrder',
  },
  {
    dataIndex: 'new_asset_photos',
    title: '资产收入证明[最新]',
    width: 450,
    slotName: 'img',
  },
  {
    dataIndex: 'investment_photos',
    title: '投资经历证明',
    width: 450,
    slotName: 'imgOrder',
  },
  {
    dataIndex: 'asset_photos',
    title: '资产收入证明',
    width: 450,
    slotName: 'img',
  },
  {
    dataIndex: 'asset_flag',
    title: '资产认证标识[合格投资者]',
    width: 220,
    slotName: 'assetFlagState',
  },
  {
    dataIndex: 'proinvestor_flag',
    title: '资产认证标识[专业投资者]',
    width: 220,
    slotName: 'proinvestorFlagState',
  },
  {
    dataIndex: 'fail_reason',
    title: '不通过原因',
    width: 120,
  },
  {
    dataIndex: 'mtime',
    title: '修改时间',
    width: 250,
    slotName: 'formatTime',
  },
];
const assetOptions = [
  {
    label: '未认证',
    value: AssetFlag.No_Approve
  },
  {
    label: '待审核',
    value: AssetFlag.ToBe_Audit
  },
  {
    label: '已认证',
    value: AssetFlag.Approved
  },
  {
    label: '认证失败',
    value: AssetFlag.Approved_fail
  }
]
const proinvestorOptions = [
  {
    label: '未认证',
    value: ProinvestorFlag.No_Approve
  },
  {
    label: '待审核',
    value: ProinvestorFlag.ToBe_Audit
  },
  {
    label: '已认证',
    value: ProinvestorFlag.Approved
  },
  {
    label: '认证失败',
    value: AssetFlag.Approved_fail
  }
]

const PARAMS = {
  PAGE_SIZE,
  PAGE_SIZE_LIST,
  COLUMNS,
  assetOptions,
  proinvestorOptions
};

export default PARAMS;
