<template>
  <div class="m-img-div" @click="handleClick">
    <template v-if="base64">
      <img :src="base64" alt="" class="m-img" />
      <a-modal
        v-model:visible="visible"
        ok-text="关闭"
        :closable="false"
        @ok="() => (visible = false)"
        class="m-modal"
      >
        <img :src="base64" alt="" class="m-modal-img" />
      </a-modal>
    </template>
    <template v-else>
      <div class="m-error">
        <span>加载失败</span>
        <span class="retry-text">点击重试</span>
      </div>
    </template>
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, onMounted, toRefs } from 'vue';
import ajax from '@/server/http';

export default defineComponent({
  props: {
    s3: {
      type: String,
      default: () => '',
    },
  },
  setup(props) {
    const state = reactive({
      base64: '',
      visible: false,
    });

    const loadImage = async () => {
      try {
        const res = await ajax('getS3ImageUrl', {
          bucket_name: 'thsjj-jj-account-s3.fund-userphoto',
          object_id: props.s3.replace('/s3/fund-userphoto/', '') || '',
        }, {
          headers: {
            'Content-Type': 'application/json',
          },
        });
        if (res.image_base64) {
          state.base64 = `data:image/jpeg;base64,${res.image_base64}`;
        } else {
          state.base64 = '';
        }
      } catch (error) {
        state.base64 = '';
      }
    };

    const handleClick = () => {
      if (state.base64) {
        // 如果已加载成功，则预览
        const img = new window.Image();
        img.src = state.base64;
        const newWin = window.open('');
        const div = document.createElement('div');
        div.style.display = 'flex';
        div.style.justifyContent = 'center';
        div.style.alignItems = 'center';
        div.appendChild(img);
        newWin.document.write(div.outerHTML);
      } else {
        // 如果加载失败，则重试
        loadImage();
      }
    };

    onMounted(() => {
      if (props.s3) {
        loadImage();
      }
    });

    return {
      ...toRefs(state),
      handleClick,
    };
  },
});
</script>

<style lang="less" scoped>
.m-img-div {
  width: 60px;
  height: 60px;
  margin-right: 10px;
  margin-bottom: 16px;
  background: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;

  .m-img {
    width: 100%;
    height: 100%;
  }

  .m-error {
    display: flex;
    flex-direction: column;
    align-items: center;
    color: #999;
    font-size: 12px;

    .retry-text {
      color: #1890ff;
      margin-top: 4px;
    }
  }
}

.m-modal {
  .m-modal-img {
    height: auto;
    width: 400px;
  }
}
</style>
