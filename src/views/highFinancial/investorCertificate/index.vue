<template>
  <div class="investorCertificate-list">
    <div class="investorCertificate-list-search filter-container">
      <a-input
        v-model:value="cust_id"
        allow-clear
        placeholder="请输入客户号"
        style="width: 220px"
      />
      <a-select
        v-model:value="asset_flag"
        placeholder="请选择合格投资者认证状态"
        allow-clear
        style="width: 220px"
      >
        <a-select-option
          v-for="option in assetOptions"
          :key="option.value"
          :value="option.value"
        >
          {{ option.label }}
        </a-select-option>
      </a-select>
      <a-select
        v-model:value="proinvestor_flag"
        placeholder="请选择专业投资者认证状态"
        allow-clear
        style="width: 220px"
      >
        <a-select-option
          v-for="option in proinvestorOptions"
          :key="option.value"
          :value="option.value"
        >
          {{ option.label }}
        </a-select-option>
      </a-select>
      <div class="m-filter-btns">
        <a-button type="primary" @click="handleSearch">查询</a-button>
        <a-button @click="handleMoreNoPass">批量审核不通过</a-button>
      </div>
    </div>
    <div class="investorCertificate-list-table">
      <custom-table
        v-model:page-num="pageNum"
        v-model:page-size="pageSize"
        :table-total="total"
        :table-data="tableData"
        :columns="columns"
        :row-selection="{
          selectedRowKeys: selectedRowKeys,
          onChange: handleSelectChange,
        }"
        :default-page-size="PAGE_SIZE"
        :page-size-list="PAGE_SIZE_LIST"
        :row-class-name="rowClassName()"
        :scroll="{ x: 2600, y: 660 }"
        :on-submit="onTableSubmit"
      >
        <template #assetFlagState="{ record }">
          <div>{{ filterAssetFlagState(record) }}</div>
        </template>

        <template #proinvestorFlagState="{ record }">
          <div>{{ filterProinvestorFlagState(record) }}</div>
        </template>
        <template #custname="{ record }">
          <div>{{ record.cust_name }}</div>
          <div>{{ record.id_card }}</div>
        </template>
        <template #img="{ text, record }">
          <div class="m-img-container">
            <template v-if="isTextImage(text)">
              <Image :s3="item" v-for="item in text" :key="item" />
            </template>
            <template v-else>
              <div>
                <p v-for="item in text" :key="item">
                  {{ item ? item.split('##DATE##')[0] : '' }}
                </p>
              </div>
            </template>
          </div>
        </template>
        <template #imgOrder="{ text, record }">
          <div class="m-img-container">
            <template v-if="isImage(text, record)">
              <Image :s3="item" v-for="item in text" :key="item" />
            </template>
            <template v-else>
              <div>
                <div>
                  <span>订单编号：</span>
                  <span>{{ filterOrders(record)[0] }}</span>
                </div>
                <div>
                  <span>交易时间：</span>
                  <span>{{ filterOrders(record)[1] }}</span>
                </div>
              </div>
            </template>
          </div>
        </template>
        <template #formatTime="{ text, record }">
          {{ filterTime(text) }}
        </template>
        <!-- 详情 -->
        <template #detail1="{ record }">
          <a-button
            v-if="authority.isOthers"
            type="link"
            @click="handlePassConform([record], 'asset')"
          >
            通过
          </a-button>
          <a-button
            type="link"
            @click="handleOpenReasonModal([record], 'asset')"
          >
            不通过
          </a-button>
        </template>
        <template #detail2="{ record }">
          <a-button
            type="link"
            @click="handlePassConform([record], 'proinvestor')"
          >
            通过
          </a-button>
          <!-- <a-button
            type="link"
            @click="handleOpenReasonModal([record], 'proinvestor')"
          >
            不通过
          </a-button> -->
        </template>
      </custom-table>
    </div>
    <a-modal
      v-model:visible="reasonVisible"
      ok-text="确定不通过"
      :closable="false"
      @ok="handleNoPassConform"
    >
      <div class="m-reason-remark">
        <div>备注内容：</div>
        <div>
          <a-select
            v-model:value="selectReason"
            placeholder="请选择备注内容"
            allow-clear
            style="width: 220px"
            mode="multiple"
            @change="handleChangeReason"
          >
            <a-select-option
              v-for="option in reasonOptions"
              :key="option.param_code"
              :value="option.param_code"
              :disabled="option.disabled"
            >
              {{ option.param_name }}
            </a-select-option>
          </a-select>

          <a-textarea
            v-if="selectReason[0] === numberFlag"
            v-model:value="otherReason"
            placeholder="请输入其他原因"
            style="width: 460px"
            :maxlength="70"
            :rows="4"
          />
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script lang="ts">
import { computed, defineComponent, onMounted, reactive, toRefs } from 'vue';
import { message } from 'ant-design-vue';
import moment from 'moment';

import CustomTable, {
  useCustomTable,
} from '@/components/common/CustomTable.vue';
import Image from './image.vue';

import ajax from '@/server/http';
import { RoleAuth } from '@/utils/const';
import PARAMS from './const';
import { ReconListTableData, AssetFlag, ProinvestorFlag } from './type';
import { auditHook } from './hooks';
import { localTest } from '@/server/api';

type iState = {
  total: number; // 表格记录总数
  tableData: ReconListTableData[]; // 表格数据
  cust_id: string; // 客户号
  asset_flag: number; // 合格投资者认证状态
  proinvestor_flag: number; // 专业投资者认证状态
  selectedRowKeys: string[]; // 勾选的数据
  numberFlag: string;
  roleList: number[];
};

export default defineComponent({
  name: 'ReconciliateReport',
  components: {
    CustomTable,
    Image,
  },
  setup() {
    const { table, onTableSubmit, onTableChange, rowClassName } =
      useCustomTable(PARAMS.PAGE_SIZE);
    const state = reactive<iState>({
      total: 0,
      tableData: [],
      cust_id: '',
      asset_flag: AssetFlag.ToBe_Audit,
      proinvestor_flag: ProinvestorFlag.ToBe_Audit,
      selectedRowKeys: [],
      numberFlag: localTest ? '1370' : '1384',
      roleList: [],
    });
    /** methods */
    // 初始化
    const init = () => {
      fetchTable();
      ajax('sale_info').then((res) => {
        state.roleList = res.roleIdSet;
      });
    };
    const {
      auditState,
      handleOpenReasonModal,
      handlePassConform,
      handleNoPassConform,
      handleChangeReason,
    } = auditHook(init);
    // 查询表格
    const fetchTable = () => {
      ajax('get_investors_list', {
        cust_id: state.cust_id || '',
        asset_flag: state.asset_flag === undefined ? '' : state.asset_flag,
        proinvestor_flag:
          state.proinvestor_flag === undefined ? '' : state.proinvestor_flag,
        pageNum: table.pageNum,
        pageSize: table.pageSize,
      })
        .then((res) => {
          if (res) {
            state.total = res.total;
            state.tableData = res.record;
            state.tableData.map((item, i: number) => {
              item.index = i + 1 + (table.pageNum - 1) * table.pageSize;
              item.key = item.cust_id;
            });
          } else {
            state.total = 0;
            state.tableData = [];
          }
        })
        .catch(() => {
          state.total = 0;
          state.tableData = [];
        });
    };
    // 搜索产品
    const handleSearch = () => {
      onTableChange(fetchTable);
    };
    // 勾选数据
    const handleSelectChange = (selectedRowKeys: string[]) => {
      console.log('selectedRowKeys changed: ', selectedRowKeys);
      state.selectedRowKeys = selectedRowKeys;
    };
    // 批量审核不通过
    const handleMoreNoPass = () => {
      const list = [];
      state.selectedRowKeys.map((item) => {
        state.tableData.map((n) => {
          if (item === n.cust_id) {
            list.push(n);
          }
        });
      });
      if (list.length === 0) {
        message.error('请勾选数据');
        return;
      }
      handleOpenReasonModal(list, 'asset');
    };
    const filterAssetFlagState = (record: ReconListTableData) => {
      const item = PARAMS.assetOptions.find(
        (n) => n.value === parseInt(record.asset_flag)
      );
      if (item) {
        return item.label;
      } else {
        return '--';
      }
    };
    const filterProinvestorFlagState = (record: ReconListTableData) => {
      const item = PARAMS.proinvestorOptions.find(
        (n) => n.value === parseInt(record.proinvestor_flag)
      );
      if (item) {
        return item.label;
      } else {
        return '--';
      }
    };
    const isImage = (text: any, record: ReconListTableData) => {
      let noS3 = Boolean(
        Array.isArray(record.order_infos) &&
          record.order_infos.length > 0 &&
          record.order_infos[0]
      );

      if (noS3) {
        return false;
      } else {
        return true;
      }
    };
    const isTextImage = (text: any) => {
      const isImg =
        Array.isArray(text) &&
        text.length > 0 &&
        (text[text.length - 1].includes('/s3/') ||
          text[text.length - 1].includes('/tohangqing/'));
      return isImg;
    };
    const filterOrders = (record: ReconListTableData) => {
      const order_infos = record.order_infos[0];
      const list = order_infos.split(',');
      return list;
    };
    const filterTime = (text: string | null) => {
      if (!text) {
        return '--';
      }
      return moment(text.replace(/CST/gi, 'GMT+0800')).format(
        'YYYY-MM-DD HH:mm:ss'
      );
    };

    // 权限判断：普通销售、销售主管、其他
    const authority = computed(() => {
      const auth = {
        isMember: false,
        isGroup: false,
        isOthers: false,
      };
      if (
        state.roleList.includes(RoleAuth.groupMember) &&
        !state.roleList.includes(RoleAuth.groupLeader) &&
        !state.roleList.includes(RoleAuth.chief)
      ) {
        auth.isMember = true;
      } else if (
        state.roleList.includes(RoleAuth.groupLeader) &&
        !state.roleList.includes(RoleAuth.chief)
      ) {
        auth.isGroup = true;
      } else {
        auth.isOthers = true;
      }
      return auth;
    });

    // 根据权限判断表头（普通销售、销售主管、其他）
    const columns = computed(() => {
      const publicArr = PARAMS.COLUMNS.slice(3);
      if (authority.value.isMember) {
        return PARAMS.COLUMNS.slice(0, 1).concat(publicArr);
      } else if (authority.value.isGroup) {
        return PARAMS.COLUMNS.slice(0, 2).concat(publicArr);
      }
      return PARAMS.COLUMNS;
    });

    onMounted(init);
    return {
      ...toRefs(table),
      ...toRefs(state),
      ...toRefs(auditState),
      ...PARAMS,
      onTableSubmit: onTableSubmit(fetchTable),
      rowClassName,
      handleSearch,
      handleSelectChange,
      handleOpenReasonModal,
      handleChangeReason,
      handleMoreNoPass,
      handlePassConform,
      handleNoPassConform,
      filterAssetFlagState,
      filterProinvestorFlagState,
      isImage,
      isTextImage,
      filterOrders,
      filterTime,
      authority,
      columns,
    };
  },
});
</script>

<style lang="less" scoped>
@import './index.less';
</style>
