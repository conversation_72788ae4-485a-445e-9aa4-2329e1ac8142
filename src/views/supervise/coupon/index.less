// 券模板
.m-coupon-template-list,
.m-coupon-list {
  .m-table-content {
    .ant-input[disabled] {
      color: rgba(0, 0, 0, 0.85);
      background-color: #fff;
    }
    .ant-btn-link {
      padding: 0 4px;
    }
  }
}
.m-coupon-template-detail {
  form > .m-form-content > .ant-form-item:nth-last-child(1) {
    margin-left: auto;
    margin-top: 10px;
    position: relative;
    .ant-form-item-control-input-content {
      display: flex;
      flex-flow: row nowrap;
      button {
        margin-right: 20px;
      }
      button:nth-last-child(1) {
        margin-right: 0;
      }
    }
  }
}
.m-coupon-detail,
.m-coupon-template-detail {
  .m-page-subtitle {
    margin-bottom: 8px;
    font-size: 16px;
    color: #29b1ff;
  }
  .ant-form {
    padding: 0;
    background: transparent;
  }
  .m-form-wrapper {
    width: 80%;
    .m-form-content-wrapper {
      width: 100%;
      display: flex;
    }
    .m-form-content {
      width: 50%;
      display: flex;
      flex-flow: column nowrap;
      .ant-form-item {
        margin-bottom: 24px !important;
      }
      .m-form-item-custom {
        width: 100%;
        display: flex;
        align-items: center;
        margin-bottom: 10px;
        &:last-of-type {
          margin-bottom: 0;
        }
        > * {
          margin-right: 8px;
        }
        .ant-form-item {
          margin-bottom: 0 !important;
        }
      }
      .split-line {
        height: 2px;
        width: 100%;
        background: #8f8e8e;
        margin: 10px 0;
      }
      .tag-header {
        margin-bottom: 10px;
        display: flex;
        justify-content: space-between;
        .tag-header-select {
          margin-right: 10px;
          width: calc(100% - 42px);
        }
        .tag-header-delete {
          flex-shrink: 0;
        }
      }
      .tag-param-item {
        margin-bottom: 5px !important;
        .tag-param-item-label {
          word-break: break-all;
          overflow: hidden;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          white-space: normal;
          line-height: 1;
          word-wrap: break-word;
          width: 110px;
        }
        .param-date-picker, .param-input-number {
          width: 100%;
        }
      }
      .tag-param-item:last-child {
        margin-bottom: 0 !important;
      }
    }
    .m-form-options {
      margin-top: 20px;
      margin-left: 50%;
      button {
        margin-right: 16px;
      }
    }
  }
}

// 各明细页
// 活动明细
.m-actDetail-table,
.m-detail-receive,
.m-detail-use,
.m-detail-red {
  .filter-wrapper {
    .ant-picker-range {
      width: 240px;
    }
  }
}
// 领取明细
.m-detail-receive {
  width: 60%;
}
// 使用明细
.m-detail-use {
  .m-table-content {
    .header-fund_name,
    .cell-fund_name {
      text-align: center;
    }
    .cell-fund_name {
      > div:nth-child(1) {
        color: #40a9ff;
      }
      > div:nth-child(2) {
        color: #ff7878;
      }
    }
  }
}

// 筛选项间隔
.m-statistic-table,
.m-red-query-table,
.m-actDetail-table,
.m-detail-receive,
.m-detail-use,
.m-detail-red {
  .filter-wrapper {
    > :nth-child(n + 2) {
      margin-left: 20px;
    }
  }
}
.m-disabled-jump {
  color: rgba(0, 0, 0, 0.25);
  cursor: not-allowed;
}
