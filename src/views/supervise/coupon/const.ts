import { couponFormItem } from '@/views/packet/type';
export const requiredRule = [{ required: true }];
export const intRules = [
  {
    required: true,
    pattern: /^(0|[1-9]\d*)?$/,
    message: '请输入自然数！',
  },
];
export const numberRules = [
  {
    required: true,
    pattern: /^(0|[1-9]\d*)(\.\d+)?$/,
    message: '请输入数字！',
  },
];
/** 满减 */
export const COUPON_TEMPLATE_DETAIL_FORM_MJ: couponFormItem[] = [
  {
    label: '具体规则',
    rules: requiredRule,
    slotName: 'rule_mj',
    editPartDisabled: true,
  },
];
/** 折扣 */
export const COUPON_TEMPLATE_DETAIL_FORM_ZK: couponFormItem[] = [
  {
    label: '折扣力度',
    rules: requiredRule,
    slotName: 'rule_zk',
    editPartDisabled: true,
  },
  {
    label: '使用次数',
    slotName: 'usedNum',
    editPartDisabled: true,
    rules: requiredRule,
  },
];

/** 投放数量 */
export const COUPON_TEMPLATE_DETAIL_FORM_PUT_NUM: couponFormItem = {
  label: '投放总量',
  name: 'put_in_qty',
  rules: intRules,
  placeholder: '请输入投放总量',
  slotName: 'input',
  editPartDisabled: true,
  maxlength: 9,
};
// 券模板
/** 券模板审核状态 */
export const couponVerifyList = [
  {
    label: '审核中',
    value: '0',
  },
  {
    label: '审核通过',
    value: '1',
  },
  {
    label: '审核失败',
    value: '2',
  },
];
/** 券模板审核状态表 */
export const couponVerifyMap = {};
couponVerifyList.forEach((item) => {
  couponVerifyMap[item.value] = item.label;
});
/** 券模板状态 */
export const couponTemplateStatusList = [
  {
    label: '审核中',
    value: '-1',
  },
  {
    label: '有效',
    value: '0',
  },
  {
    label: '暂停',
    value: '1',
  },
  {
    label: '中止',
    value: '2',
  },
];
/** 券模板状态表 */
export const couponTemplateStatusMap = {};
couponTemplateStatusList.forEach((item) => {
  couponTemplateStatusMap[item.value] = item.label;
});
/** 券模板详情页 - 券模板类型列表 */
export const couponTypeList = [
  {
    label: '满减券',
    value: '1',
  },
];
/** 券模板列表页列表项 */
export const COUPON_TEMPLATE_LIST_COLUMNS: TableColumn[] = [
  {
    dataIndex: 'project_id',
    title: '项目ID',
    width: 80,
  },
  {
    dataIndex: 'project_name',
    title: '项目名称',
  },
  {
    dataIndex: 'template_id',
    title: '券模板ID',
    width: 100,
  },
  {
    dataIndex: 'template_name',
    title: '券模板名称',
    width: 160,
  },
  {
    dataIndex: 'template_type',
    title: '类型',
    slotName: 'couponType',
  },
  {
    dataIndex: 'template_rule',
    title: '具体规则',
    width: 220,
  },
  {
    dataIndex: 'template_status',
    title: '状态',
    slotName: 'couponTemplateStatus',
  },
  {
    dataIndex: 'audit_status',
    title: '审核状态',
    slotName: 'couponVerifyStatus',
  },
  {
    dataIndex: 'put_in_qty',
    title: '投放总量',
    slotName: 'put_in_qty',
  },
  {
    dataIndex: 'residue_receive_qty',
    title: '剩余可领取数',
    width: 140,
  },
  {
    dataIndex: 'receive_qty',
    title: '领取数',
  },
  {
    dataIndex: 'used_qty',
    title: '使用数',
  },
  {
    dataIndex: 'expire_qty',
    title: '到期数',
  },
  {
    dataIndex: 'used_rate',
    title: '使用率',
  },
  {
    dataIndex: 'used_amount',
    title: '已使用金额',
    width: 120,
  },
  {
    dataIndex: 'used_volume',
    title: '优惠券带来销量',
    width: 160,
  },
  {
    dataIndex: 'remark',
    title: '备注',
    slotName: 'textarea',
    width: 200,
  },
  {
    dataIndex: 'apply_audit_text',
    title: '修改记录',
    slotName: 'textarea',
    width: 200,
  },
  {
    dataIndex: 'option',
    title: '操作',
    slotName: 'option',
    width: 200,
    fixed: 'right',
  },
];
/** 角色权限 */
export enum ROLE_PERMISSION {
  /** 具有审核权限 */
  VERIFY = 'verify',
  /** 没有审核权限，只有操作权限 */
  OPERATE = 'operate',
}
/** 合规页面的头部表单数组 */
export const COUPON_TEMPLATE_DETAIL_FORM_HEADER_HG: couponFormItem[] = [
  {
    label: '项目ID',
    name: 'project_id',
    rules: requiredRule,
    slotName: 'input',
    placeholder: '请输入项目ID',
    editPartDisabled: true,
  },
  {
    label: '券模板类型',
    name: 'template_type',
    rules: requiredRule,
    slotName: 'select',
    placeholder: '请选择券模板类型',
    editPartDisabled: true,
    options: couponTypeList,
  },
];
