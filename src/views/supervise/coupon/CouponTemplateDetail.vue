<template>
  <div class="m-coupon-template-detail" style="width: 60%">
    <p class="m-page-title">券模板详情</p>
    <div class="m-form-wrapper">
      <a-form
        ref="formRef"
        name="couponTemplateDetailForm"
        :model="state.editForm"
        label-align="left"
        :label-col="{ span: 8 }"
        :wrapper-col="{ span: 16 }"
        @finish="handleFinish"
      >
        <div class="m-form-content">
          <div
            v-for="(content, contentIndex) in renderFormContent()"
            :key="contentIndex"
          >
            <template v-for="(item, index) in content" :key="index">
              <span
                v-if="item.show && !item.show(state.editForm, state)"
              ></span>
              <form-item-input
                v-else
                v-model:model-value="state.editForm[item.name]"
                :label="(item.label as string)"
                :name="item.name"
                :rules="item.rules"
                :auto-size="item.autoSize"
                :label-col="item.labelCol"
                :wrapper-col="item.wrapperCol"
                :slot-name="item.slotName"
              >
                <template #input>
                  <a-input
                    v-model:value="state.editForm[item.name]"
                    :placeholder="item.placeholder"
                    :addon-before="item.addonBefore"
                    :addon-after="item.addonAfter"
                    :maxlength="item.maxlength"
                    allow-clear
                  />
                </template>
                <template #select>
                  <a-select
                    v-model:value="state.editForm[item.name]"
                    :placeholder="item.placeholder"
                    allow-clear
                    :show-search="item.showSearch"
                    :options="itemOptions(item)"
                    :filter-option="item.filterOption"
                  ></a-select>
                </template>
                <template #textarea>
                  <a-textarea
                    v-model:value="state.editForm[item.name]"
                    :placeholder="item.placeholder"
                    :auto-size="item.autoSize"
                    allow-clear
                  />
                </template>
                <template #rule_mj>
                  <div
                    v-for="(couponItem, couponIndex) in state.editForm
                      .coupon_money_list"
                    :key="couponIndex"
                    class="m-form-item-custom"
                  >
                    <span>满</span>
                    <a-form-item
                      :name="['coupon_money_list', couponIndex, 'coupon_limit']"
                      :rules="numberRules"
                    >
                      <a-input
                        v-model:value="couponItem.coupon_limit"
                        placeholder="请输入满减门槛金额"
                        allow-clear
                      />
                    </a-form-item>
                    <span>减</span>
                    <a-form-item
                      :name="['coupon_money_list', couponIndex, 'coupon_value']"
                      :rules="numberRules"
                    >
                      <a-input
                        v-model:value="couponItem.coupon_value"
                        placeholder="请输入满减金额"
                        allow-clear
                      />
                    </a-form-item>
                  </div>
                  <a-button
                    type="primary"
                    style="margin-right: 20px; margin-top: 20px"
                    @click="handleAddDiscount"
                  >
                    添加规则
                  </a-button>
                  <a-button
                    :disabled="state.editForm.coupon_money_list.length <= 1"
                    type="primary"
                    @click="handleDeleteDiscount"
                  >
                    删除规则
                  </a-button>
                </template>
                <template #rule_zk>
                  <div class="m-form-item-custom">
                    <a-form-item name="sub_rate" :rules="numberRules">
                      <a-input
                        v-model:value="state.editForm.sub_rate"
                        style="width: 140px"
                        placeholder="请输入折扣率"
                        allow-clear
                      />
                    </a-form-item>
                    <span>折</span>
                  </div>
                </template>
                <template #usedNum>
                  <div class="m-form-item-custom">
                    <a-form-item
                      name="frequency"
                      :rules="[{ ...intRules[0], required: true }]"
                      style="width: 140px"
                    >
                      <a-input
                        v-model:value="state.editForm.frequency"
                        placeholder="请输入使用次数"
                        allow-clear
                        :disabled="state.editForm.limitless"
                      />
                    </a-form-item>
                    <span>次</span>
                    <a-form-item name="limitless">
                      <a-checkbox
                        v-model:checked="state.editForm.limitless"
                        @change="handleLimitless"
                      >
                        无限次
                      </a-checkbox>
                    </a-form-item>
                  </div>
                </template>
              </form-item-input>
            </template>
          </div>
          <a-form-item :wrapper-col="{ span: 8 }">
            <!-- 这里是券模版的提交按钮 -->
            <a-button
              type="primary"
              html-type="submit"
              :loading="store.state.loading"
            >
              提交
            </a-button>
            <a-button @click="handleBack">返回</a-button>
          </a-form-item>
        </div>
      </a-form>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { reactive, ref } from 'vue';
import { useRouter } from 'vue-router';
import store from '@/store';
import { couponTemplateTableData, couponFormItem } from '@/views/packet/type';
import {
  numberRules,
  intRules,
  COUPON_TEMPLATE_DETAIL_FORM_MJ,
  COUPON_TEMPLATE_DETAIL_FORM_ZK,
  COUPON_TEMPLATE_DETAIL_FORM_PUT_NUM,
  COUPON_TEMPLATE_DETAIL_FORM_HEADER_HG,
} from './const';
import FormItemInput from '@/components/packet/FormItemInput.vue';
import ajax from '@/server/http';
import { FormInstance, message } from 'ant-design-vue';
import { formatMoney } from '@/utils/fn';
import { CheckboxChangeEvent } from 'ant-design-vue/es/_util/EventInterface';
import { isStrictltIncrease } from '@/views/packet/fn';

type iState = {
  /** 详情编辑表单 */
  editForm: couponTemplateTableData;
};

// state
const router = useRouter();
const formRef = ref<FormInstance>();
const state = reactive<iState>({
  editForm: {
    coupon_money_list: [
      {
        level: 0,
        coupon_limit: '',
        coupon_value: '',
      },
    ],
  },
});

/** 提交表单，判断编辑类型，editPart提交项不同 */
const postForm = async (form: couponTemplateTableData) => {
  /** 需要特殊处理的字段：折扣率、投放总量、使用次数无限次 */
  let data: couponTemplateTableData = form;
  // 默认填充一个唯一的模板名称，不需要用户输入
  data.template_name = `满减红包${new Date().getTime()}`;
  const isDuplicate = await ajax('checkCouponTemplateNameIsDuplicate', {
    couponTemplateName: data.template_name,
  });
  if (isDuplicate) {
    message.error('内部错误，请重试');
    return;
  }
  // 特殊字段处理
  if (state.editForm.template_type === '2') {
    data.sub_rate = Number(formatMoney(form.sub_rate / 10, { precision: 3 }));
    data.limitless = Number(form.limitless);
  }
  if (state.editForm.template_type === '1') {
    const couponRulesList = data.coupon_money_list;
    const couponLimitList = couponRulesList.map((item) => item.coupon_limit);
    const couponValueList = couponRulesList.map((item) => item.coupon_value);
    if (
      !isStrictltIncrease(couponLimitList) ||
      !isStrictltIncrease(couponValueList)
    ) {
      message.error('红包不满足逐级递增');
      return;
    }
    for (let i = 0; i < couponLimitList.length; i++) {
      if (Number(couponLimitList[i]) < Number(couponValueList[i])) {
        message.error('红包使用门槛不能小于红包金额');
        return;
      }
    }
    couponRulesList.forEach((item, index) => {
      item.level = index;
    });
  }
  const res = await ajax('couponTemplateAdd', data);
  if (res) {
    message.success('提交成功');
    handleBack();
  }
};
/** 渲染表单块 */
const renderFormContent = () => {
  const formContents = [COUPON_TEMPLATE_DETAIL_FORM_HEADER_HG];
  switch (state.editForm.template_type) {
    case '1':
      formContents.push(COUPON_TEMPLATE_DETAIL_FORM_MJ);
      break;
    case '2':
      formContents.push(COUPON_TEMPLATE_DETAIL_FORM_ZK);
      break;
    default:
      break;
  }
  formContents.push([COUPON_TEMPLATE_DETAIL_FORM_PUT_NUM]);
  return formContents;
};
/** 表单项options */
const itemOptions = (item: couponFormItem) => {
  if (Array.isArray(item.options)) return item.options;
  else return item.options(state.editForm, state);
};

// controller
/** 提交表单 */
const handleFinish = (value: couponTemplateTableData) => {
  postForm(value);
};
/** 返回上一级 */
const handleBack = () => {
  router.back();
};
/** 是否无限次 */
const handleLimitless = (e: CheckboxChangeEvent) => {
  const checked = e.target.checked;
  const maxFrequency = 999999999;
  state.editForm.frequency = checked ? maxFrequency : null;
  formRef.value.validateFields(['frequency']);
};
// 添加满减规则
const handleAddDiscount = () => {
  const couponRulesNum = state.editForm.coupon_money_list.length;
  state.editForm.coupon_money_list.push({
    level: couponRulesNum + 1,
    coupon_limit: '',
    coupon_value: '',
  });
};
// 删除满减规则
const handleDeleteDiscount = () => {
  state.editForm.coupon_money_list.pop();
};
</script>

<style lang="less">
@import './index.less';
</style>
