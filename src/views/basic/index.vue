<template>
  <div id="root"></div>
</template>

<script lang="ts" setup>
import amisEnv from '@/utils/amisEnv';
import ajax from '@/server/http';
import {
  AMIS_ENV,
  DEFAULT_SYSTEM_ID,
  env,
  baseURL,
  baseWebURL,
} from '@/utils/request';
import { onUnmounted, ref, watch, withDefaults, defineProps } from 'vue';
import { useRouter } from 'vue-router';
import { useStore } from 'vuex';
import { amisRemoteEmbed, amisLocalEmbed } from '@king-fisher/crm-amis-next';
import registerCommonFilter from '@/utils/registerCommonFilter';
import {
  fetchSaleTree,
  fetchUserAdmin,
  fetchActivityTree,
  fetchSaleInfo,
  callTelPhone,
} from '@/server/api';

interface Props {
  pageId?: number; // 页面ID
  extraData?: Record<string, any>; // 额外的数据
}

const props = withDefaults(defineProps<Props>(), {
  pageId: 0,
  extraData: () => ({}),
});

const router = useRouter();
const store = useStore();
const amisScoped = ref(null);
const amisLib = window.amisRequire('amis');
registerCommonFilter(amisLib);

const cleanupAmisScoped = () => {
  try {
    amisScoped.value?.unmount?.();
    amisScoped.value = null;
  } catch (error) {
    console.warn(error);
  }
};

// 加载本地配置
const loadLocalSchema = async (id: string, data: Record<string, any>) => {
  const jsonSchema = await ajax('getKAmisSchema', {
    key: `page_${id}_${env === 'prod' ? 'prod' : 'test'}`,
  });
  const amisSchema = JSON.parse(jsonSchema || '{}');
  const scoped = await amisLocalEmbed(
    '#root',
    amisSchema,
    {},
    { data },
    amisEnv()
  );
  amisScoped.value = scoped;
};

// 加载远程配置
const loadRemoteSchema = async (
  id: number,
  systemId: number,
  version: string | undefined,
  data: Record<string, any>
) => {
  const scoped = await amisRemoteEmbed(
    '#root',
    {
      id,
      systemId,
      env: AMIS_ENV,
      version,
      useDbData: true,
    },
    {
      useLocalCache: true,
    },
    { data },
    amisEnv()
  );
  amisScoped.value = scoped;
};

// 监听路由变化和属性变化
watch(
  [
    () => router.currentRoute.value.query,
    () => store.state.userInfo,
    () => props.extraData,
    () => props.pageId,
  ],
  async ([to]) => {
    // 清理旧的amisScoped
    cleanupAmisScoped();

    const { id, systemId, version, isLocal } = to as {
      id?: string;
      systemId?: string;
      version?: string;
      isLocal?: string;
    };

    if (!id) return;

    // 统一处理公共数据
    const commonData = {
      env,
      baseURL,
      baseWebURL,
      userInfo: store.state.userInfo,
      fetchSaleTree,
      fetchUserAdmin,
      fetchActivityTree,
      fetchSaleInfo,
      callTelPhone,
      ...props.extraData,
    };

    if (isLocal === 'true') {
      await loadLocalSchema(id, commonData);
    } else {
      const pageId = props.pageId || parseInt(id, 10);
      const finalSystemId = Number(systemId || DEFAULT_SYSTEM_ID);
      await loadRemoteSchema(pageId, finalSystemId, version, commonData);
    }
  },
  { immediate: true, deep: true }
);

onUnmounted(() => {
  cleanupAmisScoped();
});
</script>

<style lang="scss">
#root {
  @import '../../styles/amis.scss';
}
</style>
