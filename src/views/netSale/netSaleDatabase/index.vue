<template>
  <div style="height: 100%">
    <section id="netSaleDatabase"></section>
  </div>
</template>

<script lang="ts">
import amisEnv from '@/utils/amisEnv';
import ajax from '@/server/http';
import { message, notification } from 'ant-design-vue';
import { getUrlParam } from '@/utils/fn';
import { amisRemoteEmbed } from '@king-fisher/crm-amis-next';
import { DEFAULT_SYSTEM_ID, AMIS_ENV } from '@/utils/request';
import { fetchActivityTree, fetchSaleTree } from '@/server/api';

export default {
  data() {
    return {
      amisScoped: null,
      saleInfo: {},
      saleTree: [],
      activityTree: [],
      termInfo: {},
      breakPointList: [],
      breakPointMap: [],
      positionLift: [],
    };
  },
  async created() {
    const htmlDOM = document.querySelector('html');
    htmlDOM.style.fontSize = '11px';
    Promise.all([
      this.handleGetSaleInfo(),
      fetchSaleTree(),
      fetchActivityTree({ condition: 1 }), // 只查询网销活动来源
      this.handleGetTermInfo(),
      this.handleQueryBreakPointList(),
      this.handleQueryPositionLift(),
    ]).then(
      ([
        saleInfo,
        saleTree,
        activityTree,
        termInfo,
        { break_point_list, break_point_map },
        positionLift,
      ]) => {
        this.saleInfo = saleInfo;
        this.saleTree = saleTree;
        this.activityTree = activityTree;
        this.termInfo = termInfo;
        this.breakPointList = break_point_list || [];
        this.breakPointMap = break_point_map || {};
        this.positionLift = positionLift || [];
        this.handleRegisterAction();
        const CURRENT_PAGE_ID = 1123;
        amisRemoteEmbed(
          '#netSaleDatabase',
          {
            id: CURRENT_PAGE_ID,
            systemId: DEFAULT_SYSTEM_ID,
            env: AMIS_ENV,
          },
          {
            // 开启本地缓存
            useLocalCache: true,
          },
          {
            data: {
              apiPrefix:
                DEV_ENV && getUrlParam('mock', 'hash')
                  ? 'http://yapi.myhexin.com/yapi/mock_v2/311188/crm/fund'
                  : '',
              saleInfo: this.saleInfo,
              saleTree: this.saleTree,
              activityTree: this.activityTree,
              termInfo: this.termInfo,
              breakPointList: this.breakPointList,
              breakPointMap: this.breakPointMap,
              positionLift: this.positionLift,
            },
          },
          amisEnv()
        ).then((scoped) => {
          this.amisScoped = scoped;
        });
      }
    );
  },
  unmounted() {
    this.amisScoped.unmount();
  },
  methods: {
    // 注册自定义事件
    handleRegisterAction() {
      const amisLib = window.amisRequire('amis');
      // 拨号事件
      amisLib.registerAction('call-tel-phone', {
        run: (action, renderer, event) => {
          const { tel_as, user_id, sale_id } = action.args;
          this.handleCallTelPhone(tel_as, user_id, sale_id);
        },
      });
      // 加微事件
      amisLib.registerAction('add-wx', {
        run: (action, renderer, event) => {
          const { tel_as, user_id, sale_id } = action.args;
          this.handleAddWX(tel_as, user_id, sale_id);
        },
      });
    },
    // 获取当前销售信息
    handleGetSaleInfo() {
      return new Promise((resolve, reject) => {
        ajax('sale_info').then(res => {
          resolve(res || {});
        });
      });
    },
    // 获取客户等级和筛选区间信息
    handleGetTermInfo() {
      return new Promise((resolve, reject) => {
        ajax('queryDatabaseQueryTerm').then(res => {
          resolve(res || {});
        });
      });
    },
    // 获取断点信息
    handleQueryBreakPointList() {
      return new Promise((resolve, reject) => {
        ajax('breakPointV2', {
          condition: '1', // 只查询网销活动来源
        }).then(res => {
          if (res) {
            const break_point_list = res.map((breakPoints, index) => {
              breakPoints['break_point_info'] = breakPoints.value;
              breakPoints['children'] = breakPoints.break_point_list;
              return breakPoints;
            });
            const break_point_map = {};
            break_point_list.forEach(breakPoint => {
              breakPoint.children.forEach(item => {
                break_point_map[item.id] = item.break_point_info;
              });
            });
            resolve({ break_point_list, break_point_map });
          } else {
            resolve({});
          }
        });
      });
    },
    // 获取当前时间
    getCurrentTime() {
      const date = new Date();
      const year = date.getFullYear();
      const month = date.getMonth() + 1;
      const day = date.getDate();
      const hour = date.getHours();
      const minute = date.getMinutes();
      const second = date.getSeconds();
      return (
        year +
        '-' +
        month +
        '-' +
        day +
        ' ' +
        hour +
        ':' +
        minute +
        ':' +
        second
      );
    },
    // 拨打电话功能实现
    handleCallTelPhone(pseudocode: string, user_id: string, sale_id: string) {
      ajax('getPhoneBase64', {
        pseudocode,
      }).then((data: string) => {
        const a = document.createElement('a');
        a.setAttribute('href', `crm://m=1&p=${data}`);
        a.setAttribute('id', 'js_a');
        if (document.getElementById('js_a')) {
          document.body.removeChild(document.getElementById('js_a'));
        }
        document.body.appendChild(a);
        a.click();
        message.success('操作成功');
        this.recordCallTelPhone(user_id, sale_id);
      });
    },
    // 记录拨号
    recordCallTelPhone(user_id: string, sale_id: string) {
      const currentTime = this.getCurrentTime();
      ajax('post_record_call', {
        user_id,
        sale_id,
        dial_time: currentTime,
      }).catch(err => {
        console.log('recordCallTelPhone fail', err);
      });
    },
    // 添加微信
    handleAddWX(pseudocode: string, userid: string, sale_id: string) {
      ajax(
        'getWXMobile',
        {
          userid,
          mobile: pseudocode.replace(/#/gi, ''),
          type: 1,
        },
        {}
      ).then(res => {
        if (res) {
          this.handleCopy(res);
          notification['success']({
            message: '已成功复制到剪贴板',
          });
        } else {
          notification['error']({
            message: '获取微信号失败',
          });
        }
      });
    },
    // 复制
    handleCopy(content) {
      const dom = document.createElement('input');
      dom.setAttribute('value', content);
      document.body.appendChild(dom);
      dom.select();
      document.execCommand('copy');
      document.body.removeChild(dom);
      message.success('复制成功');
    },
    // 获取持仓标签选项
    handleQueryPositionLift() {
      return new Promise((resolve, reject) => {
        ajax('QueryPositionLift').then(res => {
          if (res) {
            resolve(res);
          } else {
            resolve([]);
          }
        });
      });
    },
  },
};
</script>

<style lang="less">
#netSaleDatabase {
  .cxd-Panel-heading {
    display: none;
  }
  .cxd-Panel-body {
    padding: 0;
  }
  .cxd-Select-clear,
  .cxd-Select-placeholder {
    font-size: 1rem;
    line-height: 1;
  }
  .cxd-TextControl-input {
    font-size: 1rem;
    & input {
      height: 1.5rem;
    }
  }
  .cxd-CheckboxesControl {
    .cxd-Grid {
      &:not(:last-child) {
        margin-bottom: 0.4rem;
      }
    }
  }
  .cxd-ResultBox-value-wrap {
    font-size: 1rem;
    line-height: 1;
  }
}
</style>
