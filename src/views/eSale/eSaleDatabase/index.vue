<template>
  <div style="height: 100%">
    <section id="eSaleDatabase"></section>
  </div>
</template>

<script lang="ts">
import amisEnv from '@/utils/amisEnv';
import ajax from '@/server/http';
import { message } from 'ant-design-vue';
import { getUrlParam } from '@/utils/fn';
import { amisRemoteEmbed } from '@king-fisher/crm-amis-next';
import { DEFAULT_SYSTEM_ID, AMIS_ENV } from '@/utils/request';
import { fetchActivityTree, fetchSaleTree } from '@/server/api';

interface PositionInterval {
  param_code: string;
  param_name: string;
}
export default {
  data() {
    return {
      amisScoped: null,
      saleInfo: {},
      saleTree: [],
      activityTree: [],
      queryTerm: {},
    };
  },
  async created() {
    const htmlDOM = document.querySelector('html');
    htmlDOM.style.fontSize = '11px';
    Promise.all([
      this.handleGetSaleInfo(),
      fetchSaleTree(),
      fetchActivityTree(),
      this.handleGetQueryTerm(),
    ])
      .then(([saleInfo, saleTree, activityTree, queryTerm]) => {
        this.saleInfo = saleInfo;
        this.saleTree = saleTree;
        this.activityTree = activityTree;
        // 处理后端返回的持仓区间，将万元以下的区间合并成一个
        const newPositionTerm = (queryTerm.position_term || []).filter(
          (item: PositionInterval) => +item.param_code > 10000
        );
        newPositionTerm.unshift({
          param_code: '10001',
          param_name: '持仓万元以下',
        });
        this.queryTerm = {
          ...queryTerm,
          position_term: newPositionTerm,
        };
        this.handleRegisterAction();
        this.handleRemoteEmbed();
      })
      .catch(error => {
        console.error(error);
        this.handleRemoteEmbed();
      });
  },
  unmounted() {
    this.amisScoped.unmount();
  },
  methods: {
    // 远程加载页面配置
    handleRemoteEmbed() {
      const CURRENT_PAGE_ID = 1017;
      amisRemoteEmbed(
        '#eSaleDatabase',
        {
          id: CURRENT_PAGE_ID,
          systemId: DEFAULT_SYSTEM_ID,
          env: AMIS_ENV,
        },
        {
          // 开启本地缓存
          useLocalCache: true,
        },
        {
          data: {
            apiPrefix:
              location.href.includes('localhost') && getUrlParam('mock', 'hash')
                ? 'https://yapi.myhexin.com/yapi/mock_v2/311188/crm/fund'
                : '',
            saleInfo: this.saleInfo,
            saleTree: this.saleTree,
            activityTree: this.activityTree,
            queryTerm: this.queryTerm,
          },
        },
        amisEnv()
      ).then(scoped => {
        this.amisScoped = scoped;
      });
    },
    // 注册自定义事件
    handleRegisterAction() {
      const amisLib = window.amisRequire('amis');
      // 拨号事件
      amisLib.registerAction('call-tel-phone', {
        run: (action, renderer, event) => {
          const { tel_as } = action.args;
          this.handleCallTelPhone(tel_as);
        },
      });
    },
    // 获取当前销售信息
    handleGetSaleInfo() {
      return new Promise((resolve, reject) => {
        ajax('sale_info')
          .then(res => {
            resolve(res || {});
          })
          .catch(error => {
            reject(error);
          });
      });
    },
    // 获取检索条件
    handleGetQueryTerm() {
      return new Promise((resolve, reject) => {
        ajax('queryDatabaseQueryTerm')
          .then(res => {
            resolve(res || {});
          })
          .catch(error => {
            reject(error);
          });
      });
    },
    // 拨打电话功能实现
    handleCallTelPhone(pseudocode: string) {
      ajax('getPhoneBase64', {
        pseudocode,
      })
        .then((data: string) => {
          const a = document.createElement('a');
          a.setAttribute('href', `crm://m=1&p=${data}`);
          a.setAttribute('id', 'js_a');
          if (document.getElementById('js_a')) {
            document.body.removeChild(document.getElementById('js_a'));
          }
          document.body.appendChild(a);
          a.click();
          message.success('操作成功');
        })
        .catch(err => {
          message.error('操作失败', err);
        });
    },
  },
};
</script>

<style lang="less">
#eSaleDatabase {
  .cxd-Panel-heading {
    display: none;
  }
  .cxd-Panel-body {
    padding: 0;
  }
  .cxd-Select-clear,
  .cxd-Select-placeholder {
    font-size: 1rem;
    line-height: 1;
  }
  .cxd-TextControl-input {
    font-size: 1rem;
    & input {
      height: 1.5rem;
    }
  }
  .cxd-CheckboxesControl {
    .cxd-Grid {
      &:not(:last-child) {
        margin-bottom: 0.4rem;
      }
    }
  }
}
</style>
