<template>
  <div class="m-black-menu-add">
    <p class="m-page-title">新增黑名单</p>
    <div class="m-form-wrapper" style="width: 50%">
      <a-form
        :model="state.formData"
        name="blackMenu"
        label-align="left"
        :label-col="{ span: 4 }"
        :wrapper-col="{ span: 18 }"
        @finish="onFinish"
      >
        <a-form-item
          label="黑名单类型"
          name="blackType"
          :rules="[{ required: true }]"
        >
          <a-checkbox-group
            v-model:value="state.formData.blackType"
            :options="menuTypeList"
          />
        </a-form-item>
        <a-form-item
          label="导入方式"
          name="importType"
          :rules="[{ required: true }]"
        >
          <a-radio-group
            v-model:value="state.formData.importType"
            :options="importTypeList"
          />
        </a-form-item>
        <a-form-item
          label="导入数据"
          name="idList"
          :rules="[
            { required: true },
            { pattern: /^[\d\n#]+$/, message: '请输入正确的数据格式' },
          ]"
        >
          <div>
            <a-textarea
              v-model:value="state.formData.idList"
              :auto-size="{ minRows: 4, maxRows: 12 }"
            />
            <div class="m-form-importData">
              <a-form-item>
                <a-upload
                  v-model:file-list="state.formData.userFile"
                  accept=".txt"
                  :before-upload="handleUpload"
                  :max-count="1"
                >
                  <a-button size="small">txt上传</a-button>
                </a-upload>
                <a-button type="link" @click="handleExample">查看范例</a-button>
              </a-form-item>
              <p>说明：导入的号码支持分行识别（不允许有空格或标点符号）</p>
            </div>
          </div>
        </a-form-item>
        <a-form-item label="备注" name="remark">
          <a-textarea
            v-model:value="state.formData.remark"
            :auto-size="{ minRows: 2, maxRows: 4 }"
            allow-clear
          />
        </a-form-item>
        <div class="m-form-options">
          <a-button type="primary" html-type="submit" :disabled="!auth.addAuth">
            添加
          </a-button>
        </div>
      </a-form>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { readTxtFile } from '@/utils/fn';
import { onMounted, reactive } from 'vue';
import { menuTypeList, importTypeList, defaultBlackType, leaderRoles } from './const';
import { blackFormData } from './type';
import ajax from '@/server/http';
import { message } from 'ant-design-vue';
import { fetchSaleInfo } from '@/server/api';

type iState = {
  formData: blackFormData;
};

// state
const state = reactive<iState>({
  formData: {
    blackType: defaultBlackType,
    importType: '1',
  },
});
const auth = reactive<{
  addAuth: number;
}>({
  addAuth: 0,
});

// methods
/** 初始化 */
const init = () => {
  getUserAuth();
};
/** 获取用户权限 */
const getUserAuth = async () => {
  const add = await fetchSaleInfo(null, leaderRoles);
  auth.addAuth = add && add.role;
};
/** 提交表单 */
const postForm = async (form: blackFormData) => {
  const data: blackFormData = {
    blackType: (form.blackType as number[]).join(),
    importType: form.importType,
    idList: (form.idList as string).split('\n').filter((str) => str),
    remark: form.remark || '',
  };
  const res = await ajax('addBlackMenu', data);
  if (res) {
    const successStr = `成功导入${res.successNum}人，`;
    const failStr =
      res.failList.length > 5 || res.failList.length === 0
        ? `导入失败${res.failList.length}人`
        : `${res.failList.join()}导入失败`;
    message.success({
      content: successStr + failStr,
      class: 'm-notice',
    });
  }
};

// controllers
/** 表单校验通过 */
const onFinish = (form: blackFormData) => {
  postForm(form);
};
/** 上传文件 */
const handleUpload = (file: File) => {
  readTxtFile(
    file,
    (text) => {
      state.formData.idList = text as string;
    },
    { encoding: 'GB2312' }
  );
  return false;
};
/** 查看范例 */
const handleExample = () => {
  ajax(
    'downloadBlackMenuExample',
    {},
    {},
    { getByElement: true, fileName: '范例.txt' }
  );
};
onMounted(init);
</script>

<style lang="less">
@import './index.less';
</style>
