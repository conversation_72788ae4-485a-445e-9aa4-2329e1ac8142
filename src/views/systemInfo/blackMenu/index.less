.m-black-menu-add {
  .m-form-wrapper {
    form {
      padding: 10px;
      .m-form-importData {
        > div:nth-child(1) {
          margin-top: 8px;
          display: flex;
          align-items: center;
          > * {
            margin-bottom: 0 !important;
          }
        }
        > p {
          color: red;
        }
      }
      .m-form-options {
        width: 91.667%;
        margin: 20px 0 10px 0;
        display: flex;
        > button {
          margin-left: auto;
        }
      }
    }
  }
}
.m-notice {
  width: 300px;
  left: calc(50% - 150px);
  top: 60vh;
  position: fixed;
}
