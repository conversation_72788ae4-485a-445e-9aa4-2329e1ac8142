<template>
  <article>
    <a-card title="基金小喇叭设置" :style="{ paddingBottom: '80px' }">
      <a-card title="活动来源">
        <a-select
          v-model:value="state.hornConfig.sources"
          mode="multiple"
          style="width: 100%"
          show-search
          show-arrow
          allow-clear
          option-filter-prop="label"
          placeholder="选择活动来源"
          :options="getActOption()"
        ></a-select>
      </a-card>
      <a-card title="资料类型" :style="{ marginTop: '16px' }">
        <a-select
          v-model:value="state.hornConfig.tags"
          mode="multiple"
          style="width: 100%"
          show-search
          show-arrow
          allow-clear
          option-filter-prop="label"
          placeholder="选择活动来源"
          :options="getTagOption()"
        ></a-select>
      </a-card>
      <a-card title="触发文案示例" :style="{ marginTop: '16px' }">
        <p>
          【用户状态变更】用户:【用户名】,最新状态为:用户类型名称,请及时联系
        </p>
        <p>【用户参与活动】用户:【用户名】,参与活动:活动名称,请及时联系</p>
      </a-card>
      <a-button
        size="medium"
        :style="{ marginTop: '16px', width: '100px' }"
        class="medium-button"
        @click="handleSubmit"
      >
        提交数据
      </a-button>
    </a-card>
  </article>
</template>

<script setup lang="ts">
import { onMounted, reactive } from 'vue';
import ajax from '@/server/http';
import {
  HornConfigProps,
  hornEnumProps,
  List,
  HornInterfaceProps,
  activityEnumProps,
} from './type';
import { message } from 'ant-design-vue';
type checkBoxType = {
  label: string;
  value: string;
};

type iState = {
  activitySourceArr: checkBoxType[];
  dataTypeArr: string[];
  triggerTextArr: string[];
  hornConfig: HornConfigProps;
  hornEnum: hornEnumProps;
};
// 基金用户类型
const  COMMON_PARENT_CODE = 1265;
const MAX_PAGE = 999;
const state = reactive<iState>({
  activitySourceArr: [],
  dataTypeArr: [],
  triggerTextArr: [],
  hornConfig: {
    tags: [],
    sources: [],
  },
  hornEnum: {
    soruceEnum: {
      list: [],
    },
    activityEnum: {
      record: [],
    },
  },
});
// 做一个类型转换 活动来源地values
const getActOption = () => {
  return state.hornEnum.activityEnum.record.map((e) => {
    return { value: Number(e.activity_id), label: e.activity_name };
  });
};
// 做一个类型转换 资料类型的values
const getTagOption = () => {
  return state.hornEnum.soruceEnum.list.map((e) => {
    return { value: Number(e.param_code), label: e.param_name };
  });
};
/** watch */
/** methods */
// 初始化
const init = () => {
  fetchTrumpet();
};
// 查询表格
const fetchTrumpet = () => {
  ajax('get_notice_activity_enum', { pageNum: 1, pageSize: MAX_PAGE })
    .then((res: activityEnumProps) => {
      if (res) {
        state.hornEnum.activityEnum = res;
      }
    })
    .catch(() => {});
  ajax('get_notice_source_enum', {
    parent_code: COMMON_PARENT_CODE,
    split_code: ':',
    pageNum: 1,
    pageSize: MAX_PAGE,
  })
    .then((res: List[]) => {
      if (res) {
        state.hornEnum.soruceEnum.list = res;
      }
    })
    .catch(() => {});
  ajax('get_notice_config')
    .then((res: HornInterfaceProps) => {
      if (res) {
        state.hornConfig.tags = res.tags;
        state.hornConfig.sources = res.sources;
      } else {
        state.hornConfig.tags = [];
        state.hornConfig.sources = [];
      }
    })
    .catch(() => {
      state.hornConfig.tags = [];
      state.hornConfig.sources = [];
    });
};
// 处理提交
const handleSubmit = () => {
  ajax('post_notice_set_config', {
    tags: state.hornConfig.tags,
    sources: state.hornConfig.sources,
  })
    .then((res: any) => {
      if (res) {
        message.success('提交成功');
        console.log('success', res);
      }
    })
    .catch(() => {
      message.error('网络异常');
    });
};
// 跳转销售详情页

onMounted(init);
</script>
<style lang="less">
.medium-button {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  margin-top: 16px;
}

// @import './style/index';
</style>
