export interface Tag_name_map {
  /** 标签id */
  // key: string;
  /** name */
  // value: string;
  [key: string]: string;
}

export interface Source_name_map {
  /** 活动id */
  // key: string;
  /** name */
  // value: string;
  [key: string]: string;
}

/** @description 接口地址: /crm/fund/horn/v1/get_config */
export interface HornInterfaceProps {
  /** 标签id列表 */
  tags?: number[];
  /** 活动id列表 */
  sources?: number[];
  /** 标签名称map */
  tag_name_map: Tag_name_map[];
  /** 活动名称map */
  source_name_map: Source_name_map[];
}
export interface HornConfigProps {
  /** 标签id列表 */
  tags: number[];
  /** 活动id列表 */
  sources: number[];
}
export interface ActivityRecord {
  /** 活动id */
  activity_id: number;
  /** 活动名称 */
  activity_name: string;
}

/** @description 接口地址: /crm/fund/tracking/data/v1/get_activity_list */
export interface activityEnumProps {
  /** 总数 */
  total?: number;
  /** 页大小 */
  size?: number;
  /** 页码 */
  current?: number;
  /** 数据 */
  record: ActivityRecord[];
}
export interface List {
  /** 参数code */
  param_code: number;
  /** 参数名称 */
  param_name: string;
}

/** @description 接口地址: /crm/fund/common/param/v1/get_param_config */
export interface soruceEnumProps {
  list: List[];
}
export interface hornEnumProps {
  soruceEnum: soruceEnumProps;
  activityEnum: activityEnumProps;
}
