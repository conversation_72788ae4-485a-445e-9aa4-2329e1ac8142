<template>
  <div style="height: 100%">
    <section id="root"></section>
  </div>
</template>

<script lang="ts">
import amisEnv from '@/utils/amisEnv';
import ajax from '@/server/http';
import { RoleAuth } from '@/utils/const';
import { message, notification } from 'ant-design-vue';
import { getUrlParam } from '@/utils/fn';
import { fetchSaleTree } from '@/server/api';
import { amisRemoteEmbed } from '@king-fisher/crm-amis-next';
import { DEFAULT_SYSTEM_ID, AMIS_ENV } from '@/utils/request';

export default {
  data() {
    return {
      amisScoped: null,
      saleInfo: {},
      saleTree: []
    };
  },
  async created() {
    this.saleInfo = await this.handleGetSaleInfo();
    this.saleTree = await fetchSaleTree();
    const highAuthorityList = [RoleAuth.chief];
    const CURRENT_PAGE_ID = 1107;
    this.handleRegisterAction();
    amisRemoteEmbed(
      '#root',
      {
        id: CURRENT_PAGE_ID,
        systemId: DEFAULT_SYSTEM_ID,
        env: AMIS_ENV,
      },
      {
        // 开启本地缓存
        useLocalCache: true,
      },
      {
        data: {
          apiPrefix:
            DEV_ENV && getUrlParam('mock', 'hash')
              ? 'http://yapi.myhexin.com/yapi/mock_v2/311188/crm/fund'
              : '',
          saleInfo: this.saleInfo,
          // 高级权限
          highAuthority: highAuthorityList.some((it) =>
            this.saleInfo.roleIdSet?.includes(it)
          ),
          saleTree: this.saleTree
        },
      },
      amisEnv()
    ).then((scoped) => {
      this.amisScoped = scoped;
    });
  },
  unmounted() {
    this.amisScoped.unmount();
  },
  methods: {
    // 注册自定义事件
    handleRegisterAction() {
      const amisLib = window.amisRequire('amis');
      // 拨号事件
      amisLib.registerAction('call-tel-phone', {
        run: (action, renderer, event) => {
          const { tel_as, user_id, sale_id } = action.args;
          this.handleCallTelPhone(tel_as, user_id, sale_id);
        },
      });
      // 加微事件
      amisLib.registerAction('add-wx', {
        run: (action, renderer, event) => {
          console.log(action, renderer, event);
          const { tel_as, user_id, sale_id } = action.args;
          this.handleAddWX(tel_as, user_id, sale_id);
        },
      });
    },
    // 获取当前销售信息
    handleGetSaleInfo() {
      return new Promise((resolve, reject) => {
        ajax('sale_info').then((res) => {
          if (res) {
            resolve(res);
          } else {
            reject(res);
          }
        });
      });
    },
    // 获取当前时间
    getCurrentTime() {
      const date = new Date();
      const year = date.getFullYear();
      const month = date.getMonth() + 1;
      const day = date.getDate();
      const hour = date.getHours();
      const minute = date.getMinutes();
      const second = date.getSeconds();
      return (
        year +
        '-' +
        month +
        '-' +
        day +
        ' ' +
        hour +
        ':' +
        minute +
        ':' +
        second
      );
    },
    // 记录拨号
    recordCallTelPhone(user_id: string, sale_id: string) {
      const currentTime = this.getCurrentTime();
      ajax('post_record_call', {
        user_id,
        sale_id,
        dial_time: currentTime,
      }).catch((err) => {
        console.log('recordCallTelPhone fail', err);
      });
      this.handleSaveOperateLog(user_id, sale_id, '00');
    },
    // 拨打电话功能实现
    handleCallTelPhone(pseudocode: string, user_id: string, sale_id: string) {
      ajax('getPhoneBase64', {
        pseudocode,
      }).then((data: string) => {
        const a = document.createElement('a');
        a.setAttribute('href', `crm://m=1&p=${data}`);
        a.setAttribute('id', 'js_a');
        if (document.getElementById('js_a')) {
          document.body.removeChild(document.getElementById('js_a'));
        }
        document.body.appendChild(a);
        a.click();
        message.success('操作成功');
        this.recordCallTelPhone(user_id, sale_id);
      });
    },
    // 添加微信
    handleAddWX(pseudocode: string, userid: string, sale_id: string) {
      ajax(
        'getWXMobile',
        {
          userid,
          mobile: pseudocode.replace(/#/gi, ''),
          type: 1,
        },
        {}
      ).then((res) => {
        if (res) {
          this.handleCopy(res);
          this.handleSaveOperateLog(userid, sale_id, '01');
          notification['success']({
            message: '已成功复制到剪贴板',
          });
        } else {
          notification['error']({
            message: '获取微信号失败',
          });
        }
      });
    },
    // 复制
    handleCopy(content) {
      const dom = document.createElement('input');
      dom.setAttribute('value', content);
      document.body.appendChild(dom);
      dom.select();
      document.execCommand('copy');
      document.body.removeChild(dom);
      message.success('复制成功');
    },
    // 销售操作记录保存
    handleSaveOperateLog(user_id, sale_id, op_type) {
      ajax('saveOperateLog', {
        target_user_id: user_id,
        op_sale_id: sale_id,
        op_type,
      })
        .then(() => {
          // 刷新列表组件，更新列表中的操作时间
          let crudElement = this.amisScoped.getComponentById('u:ca590ae044ad');
          crudElement.reload();
        })
        .catch((err) => {
          console.log('saveOperateLog fail', err);
        });
    },
  },
};
</script>

<style>
.cxd-Tree {
  max-height: 60vh !important;
}
</style>
