const defaultPageSize = 20;
const pageSizeList = ['10', '20', '50', '100'];
const columns = [
  {
    dataIndex: 'fofFundName',
    title: '投资账户名称',
    width: 140,
  },
  {
    dataIndex: 'openAccountDate',
    title: '开户日期',
    width: 70,
  },
  {
    dataIndex: 'firstTradeDate',
    title: '首次投资日期',
    width: 70,
  },
  {
    dataIndex: 'orgName',
    title: '投管人名称',
    width: 140,
  },
  {
    dataIndex: 'saleName',
    title: '销售人员',
    width: 60,
  },
  {
    dataIndex: 'divide',
    title: '关联销售业绩占比',
    width: 60,
    slotName: 'divide'
  },
  {
    dataIndex: 'operate',
    title: '操作',
    fixed: 'right',
    width: 50,
    slotName: 'detail',
  },
];
export const tableRelate = {
  defaultPageSize,
  pageSizeList,
  columns,
};

export const initConfigState = {
  // 是否显示弹框
  visible: false,
  fofId: '',
  title: '',
  // 关联销售分成比例
  saleDivideList: [{
    saleId: undefined,
    divide: '',
    id: Date.now(),
  }],
};
