import { reactive, toRaw } from 'vue';
import { message } from 'ant-design-vue';
import dayjs from 'dayjs';

import ajax from '@/server/http';

import { initConfigState } from './constants';

import { ReconListTableData, IConfigState, TOnConfirmConfig } from './types';

export const onConfigHook = (fetchTable) => {
  let onConfigState = reactive<IConfigState>(initConfigState);
  const onConfig = (record: ReconListTableData) => {
    onConfigState = reactive<IConfigState>(initConfigState);
    onConfigState.visible = true;
    onConfigState.fofId = record.fofId;
    onConfigState.title = record.fofFundName;
    ajax('getDivideList', {
      fofIds: [record.fofId],
      startDate: record.openAccountDate
        ? dayjs(record.openAccountDate).format('YYYYMMDD')
        : '',
      endDate: record.openAccountDate
        ? dayjs(record.openAccountDate).format('YYYYMMDD')
        : '',
    }).then((res) => {
      const list = res.list || [];
      if (list.length > 0) {
        onConfigState.saleDivideList = res.list.reduce((pre, cur) => {
          return pre.concat({
            saleId: cur.saleId,
            divide: cur.divide,
          });
        }, []);
      }
    });
  };
  const onConfirmConfig: TOnConfirmConfig = (configState) => {
    const query = toRaw(configState.value);
    const postQuery = {
      fofId: onConfigState.fofId,
      saleDivideList: query.saleDivideList,
    };
    const postFn = ajax('addDivide', postQuery);
    postFn.then(() => {
      setTimeout(() => {
        message.success('操作成功');
      }, 0)
      onConfigState.visible = false;
      fetchTable();
    });
  };

  return {
    onConfigState,
    onConfig,
    onConfirmConfig,
  };
};
