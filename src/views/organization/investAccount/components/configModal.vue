<template>
  <a-modal v-model:visible="configState.visible" ok-text="提交" :closable="false" width="700px" title="配置投资账户" @ok="onOk">
    <div class="u-config-modal-container">
      <a-form ref="formRef" :model="configState">
        <h3 style="margin: 0 0 20px;">{{ `投资账户名称：${configState.title}` }}</h3>
        <invest-config :config-state="configState"></invest-config>
      </a-form>
    </div>
  </a-modal>
</template>

<script lang="ts">
import { defineComponent, PropType, computed, ref } from 'vue';
import currency from 'currency.js';
import { message } from 'ant-design-vue';

import InvestConfig from './investConfig.vue';

import { initConfigState } from '../constants';

import { IConfigState, TOnConfirmConfig } from '../types';
import { toFixed } from '@/utils/fn';

export default defineComponent({
  name: 'ConfigModal',
  components: {
    InvestConfig
  },
  props: {
    onConfigState: {
      type: Object as PropType<IConfigState>,
      default: () => initConfigState,
    },
    onConfirmConfig: {
      type: Function as PropType<TOnConfirmConfig>,
      default: () => { },
    },
  },

  emits: {
    'update:onConfigState'(payload) {
      return payload;
    },
  },
  setup(props, context) {
    const formRef = ref();
    const configState = computed({
      get: () => props.onConfigState,
      set: (val) => {
        context.emit('update:onConfigState', val);
      },
    });

    const onOk = () => {
      formRef.value.validate().then(() => {
        const saleDivideList = configState.value.saleDivideList || []
        const totalDivide = saleDivideList.reduce((pre, cur) => {
          return pre.add(currency(cur.divide))
        }, currency(0))
        if (totalDivide.value.toFixed(2) !== '100.00') {
          message.error('提成权重之和得为100')
          return
        }
        props.onConfirmConfig(configState)
      })
    }

    return {
      formRef,
      configState,
      onOk,
    };
  },
});
</script>
