<template>
  <a-space v-for="(saleDivideItem, index) in configState.saleDivideList" :key="saleDivideItem.id"
    style="display: flex; margin-bottom: 8px" align="baseline">
    <a-form-item :name="['saleDivideList', index, 'saleId']" label="姓名" required>
      <a-select v-model:value="saleDivideItem.saleId" show-search :options="saleIdsOptions" :filter-option="filterOption"
        allow-clear :disabled="disabled" style="width: 230px"></a-select>
    </a-form-item>
    <a-form-item label="提成权重(%)" :name="['saleDivideList', index, 'divide']" required>
      <a-input-number v-model:value="saleDivideItem.divide" :disabled="disabled" style="width: 136px" :min="0" :max="100" :step="0.01"
        string-mode />
    </a-form-item>

    <MinusCircleOutlined v-if="!disabled" @click="del(saleDivideItem)" />
  </a-space>
  <a-form-item>
    <a-button v-if="!disabled" type="dashed" block @click="add">
      <PlusOutlined />
      添加
    </a-button>
  </a-form-item>
</template>

<script lang="ts">
import { defineComponent, PropType, reactive, toRefs } from 'vue';
import { MinusCircleOutlined, PlusOutlined } from '@ant-design/icons-vue';

import ajax from '@/server/http';

import { initConfigState } from '../constants';

import { ISaleDivideItem, IConfigState } from '../types';
import { message } from 'ant-design-vue';

type iState = {
  saleIdsOptions: { value: string; label: string }[]; // 投资账户下拉
};

export default defineComponent({
  name: 'InvestConfig',
  components: {
    MinusCircleOutlined,
    PlusOutlined,
  },
  props: {
    configState: {
      type: Object as PropType<IConfigState>,
      default: () => initConfigState,
    },
    disabled: {
      type: Boolean,
      default: () => false,
    }
  },
  setup(props) {
    const state = reactive<iState>({
      saleIdsOptions: [],
    });
    const add = () => {
      // eslint-disable-next-line vue/no-mutating-props
      props.configState.saleDivideList.push({
        saleId: undefined,
        divide: undefined,
        id: Date.now(),
      });
    };

    const del = (saleDivideItem: ISaleDivideItem) => {
      if (props.configState.saleDivideList.length === 1) {
        message.error('至少保留一个')
        return
      }
      let index = props.configState.saleDivideList.indexOf(saleDivideItem);
      if (index !== -1) {
        // eslint-disable-next-line vue/no-mutating-props
        props.configState.saleDivideList.splice(index, 1);
      }
    };

    const filterOption = (input: string, option: any) => {
      return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
    };

    // 获取销售人员
    const getDivideSaleList = () => {
      ajax('getDivideSaleList', { isNeedSelf: false }).then((res) => {
        if (res) {
          state.saleIdsOptions = (res || []).reduce((pre, cur) => {
            return pre.concat({
              label: cur.saleName,
              value: cur.saleId,
            });
          }, []);
        }
      });
    };

    const init = () => {
      getDivideSaleList();
    };

    init();

    return {
      ...toRefs(state),
      ...toRefs(props),
      add,
      del,
      filterOption,
    };
  },
});
</script>
