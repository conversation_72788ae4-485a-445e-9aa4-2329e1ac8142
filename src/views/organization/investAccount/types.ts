import { WritableComputedRef } from "vue";

import { EType } from "./constants";

export type ReconListTableData = {
  index: number; // 序号
  key: string;
  id: string;
  operate: string; // 操作
  orgId: string;
  fofId: string;
  fofFundName: string; // 投资账户名称
  openAccountDate: string; // 开户日期
  firstTradeDate: string; // 首次投资日期
  orgName: string; // 投管人名称
  saleName: string; // 销售人员
  divide: string; // 关联销售业绩占比
};

export interface ISaleDivideItem {
  saleId: string;
  divide: string;
  id: number;
}

export interface IConfigState {
  // 是否显示弹框
  visible: boolean;
  fofId: string;
  title: string;
  // 关联销售分成比例
  saleDivideList: ISaleDivideItem[];
}

export type TOnConfirmConfig = (configState: WritableComputedRef<IConfigState>) => void;
