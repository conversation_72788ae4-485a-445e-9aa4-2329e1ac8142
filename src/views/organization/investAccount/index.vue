<template>
  <div class="cost-register-list">
    <div class="cost-register-list-search filter-container">
      <div class="filter-item">
        <span>开户日期</span>
        <a-range-picker v-model:value="dateRange" />
      </div>
      <div class="filter-item">
        <span>销售人员</span>
        <a-select
          v-model:value="saleIds"
          show-search
          mode="multiple"
          :options="saleIdsOptions"
          :filter-option="filterOption"
          allow-clear
          style="width: 220px"
        ></a-select>
      </div>
      <div class="filter-item">
        <span>投管人名称</span>
        <a-select
          v-model:value="orgId"
          show-search
          :options="orgOptions"
          :filter-option="filterOption"
          allow-clear
          style="width: 220px"
          @change="(val) => changeOrg(val)"
        ></a-select>
      </div>
      <div class="filter-item">
        <span>投资账户名称</span>
        <a-select
          v-model:value="fofIds"
          show-search
          mode="multiple"
          :options="investAccountOptions"
          :filter-option="filterOption"
          allow-clear
          style="width: 220px"
        ></a-select>
      </div>
      <div class="m-filter-btns">
        <a-button type="primary" @click="handleSearch">查询</a-button>
        <a-button @click="handleExport">导出</a-button>
      </div>
    </div>
    <div class="cost-register-list-table">
      <custom-table
        v-model:page-num="pageNum"
        v-model:page-size="pageSize"
        :table-total="total"
        :table-data="tableData"
        :columns="columns"
        :default-page-size="defaultPageSize"
        :page-size-list="pageSizeList"
        :row-class-name="rowClassName()"
        :scroll="{ y: 660 }"
        :sticky="false"
        :on-submit="onTableSubmit"
      >
        <!-- 详情 -->
        <template #detail="{ record }">
          <a-button type="link" @click="onConfig(record)">配置</a-button>
        </template>
        <template #divide="{ record }">
          <span>{{ filterDivide(record.divide) }}</span>
        </template>
      </custom-table>
    </div>
    <config-modal
      :on-config-state="onConfigState"
      :on-confirm-config="onConfirmConfig"
    />
  </div>
</template>

<script lang="ts">
import { defineComponent, onMounted, reactive, toRefs } from 'vue';
import { message } from 'ant-design-vue';
import dayjs from 'dayjs';
import type { Dayjs } from 'dayjs';

import CustomTable, {
  useCustomTable,
} from '@/components/common/CustomTable.vue';
import ConfigModal from './components/configModal.vue';

import { onConfigHook } from './hook';

import ajax from '@/server/http';
import { tableRelate } from './constants';
import { ReconListTableData } from './types';

type RangeValue = [Dayjs, Dayjs];
type iState = {
  total: number; // 表格记录总数
  tableData: ReconListTableData[]; // 表格数据
  orgId: string; // 投管人
  fofIds: string[]; // 投资账户
  saleIds: string[]; // 销售人员
  dateRange: RangeValue; // 生效期
  orgOptions: { value: string; label: string }[]; // 投管人下拉
  investAccountOptions: { value: string; label: string }[]; // 投资账户下拉
  saleIdsOptions: { value: string; label: string }[]; // 投资账户下拉
};

export default defineComponent({
  name: 'InvestAccount',
  components: {
    CustomTable,
    ConfigModal,
  },
  setup() {
    const { table, onTableSubmit, onTableChange, rowClassName } =
      useCustomTable(tableRelate.defaultPageSize);
    const { onConfigState, onConfig, onConfirmConfig } =
      onConfigHook(fetchTable);
    const state = reactive<iState>({
      total: 0,
      tableData: [],
      saleIds: [],
      orgId: '',
      fofIds: [],
      dateRange: [dayjs().subtract(1, 'month'), dayjs()],
      orgOptions: [],
      investAccountOptions: [],
      saleIdsOptions: [],
    });

    const init = () => {
      getDivideSaleList();
      getOrganization();
      fetchTable();
    };
    // 获取投管人
    const getOrganization = () => {
      ajax('getOrganization').then((res) => {
        if (res) {
          state.orgOptions = (res || []).reduce((pre, cur) => {
            return pre.concat({
              label: cur.orgName,
              value: cur.orgId,
            });
          }, []);
        }
      });
    };
    const changeOrg = (val: string) => {
      getInvestAccount(val);
    };
    // 获取投资账户
    const getInvestAccount = (orgId: string) => {
      if (!orgId) {
        state.investAccountOptions = [];
        return;
      }
      ajax('getInvestAccount', { orgId }).then((res) => {
        if (res) {
          state.investAccountOptions = (res || []).reduce((pre, cur) => {
            return pre.concat({
              label: cur.fofName,
              value: cur.fofId,
            });
          }, []);
        }
      });
    };
    // 获取销售人员
    const getDivideSaleList = () => {
      ajax('getDivideSaleList', { isNeedSelf: false }).then((res) => {
        if (res) {
          state.saleIdsOptions = (res || []).reduce((pre, cur) => {
            return pre.concat({
              label: cur.saleName,
              value: cur.saleId,
            });
          }, []);
        }
      });
    };
    const filterOption = (input: string, option: any) => {
      return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
    };
    // 查询表格
    function fetchTable() {
      ajax('getDivideList', {
        startDate: state.dateRange[0]
          ? dayjs(state.dateRange[0]).format('YYYYMMDD')
          : '',
        endDate: state.dateRange[1]
          ? dayjs(state.dateRange[1]).format('YYYYMMDD')
          : '',
        orgId: state.orgId,
        fofIds: state.fofIds,
        saleIds: state.saleIds,
        pageNo: table.pageNum,
        pageSize: table.pageSize,
      })
        .then((res) => {
          if (res) {
            state.total = res.total;
            state.tableData = res.list || [{}];
          } else {
            state.total = 0;
            state.tableData = [];
          }
        })
        .catch(() => {
          state.total = 0;
          state.tableData = [];
        });
    }
    // 搜索
    const handleSearch = () => {
      onTableChange(fetchTable);
    };

    const handleExport = () => {
      ajax('exportDivide', {
        startDate: state.dateRange[0]
          ? dayjs(state.dateRange[0]).format('YYYYMMDD')
          : '',
        endDate: state.dateRange[1]
          ? dayjs(state.dateRange[1]).format('YYYYMMDD')
          : '',
        orgId: state.orgId,
        fofIds: state.fofIds.join(','),
        saleIds: state.saleIds.join(','),
      }).then((res) => {
        message.success('导出成功');
      });
    };

    const filterDivide = (val: number) => {
      if (!val) {
        return '--';
      }
      return `${val}%`;
    };

    onMounted(init);
    return {
      ...toRefs(table),
      ...toRefs(state),
      ...tableRelate,
      onTableSubmit: onTableSubmit(fetchTable),
      rowClassName,
      filterOption,
      changeOrg,
      handleSearch,
      handleExport,
      onConfig,
      onConfigState,
      onConfirmConfig,
      filterDivide,
    };
  },
});
</script>

<style lang="less" scoped>
@import './index.less';
</style>
