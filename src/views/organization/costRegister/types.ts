import { WritableComputedRef } from "vue";
import type { Dayjs } from 'dayjs';

import { EType } from "./constants";
import { EApplyType } from "../costInvestAudit/constants";

export type ReconListTableData = {
  index: number; // 序号
  key: string;
  id: string;
  operate_asset: string; // 操作
  name: string; // 成本项名称
  type: string; // 成本类型
  costAmount: string; // 金额（元）/折扣（扣减部分比例）
  startDate: string; // 开始日期
  endDate: string; // 结束日期
  describe: string; // 说明
  fileName: string; // 证明材料
  remark: string; // 备注
  saleName: string; // 关联销售人员
  mtime: string; // 关联投管人
  mentionProfit: string; // 关联投资帐户
  order_infos: string[]; // 投资经历订单信息
};

export interface IInvestItem {
  orgId: string;
  fofIds: string[];
  id: number;
}

export interface IDistriFundCode {
  fundCode: string;
  saleRatio: string;
  customerRatio: string;
  id: number;
}

export interface IConfigState {
  // 是否显示弹框
  visible: boolean;
  id: string;
  // 成本项名称
  name: string;
  // 成本类型
  type: EType;
  // 扣减比例
  costRatio: string;
  // 成本金额
  costAmount: string,
  // 开始日期
  startDate: Dayjs | string;
  // 结束日期
  endDate: Dayjs | string;
  // 成本项说明
  describe: string;
  // 投管人/投资账户信息
  costProductList: IInvestItem[];
  saleId: string;
  // 分配基金管理费、销售服务费
  commissionRatioInfoList: IDistriFundCode[];
  // 备注
  remark: string;
  // 附件
  fileList: any[];
  fileName: string;
  // 申请人
  applyRoleName: string;
  // 申请类型枚举
  applyType: EApplyType;
  // 申请类型
  applyTypeName: string;
  // 操作类型
  handleType: string;
  // 是否禁用
  disabled?: boolean;
  // 审核不通过原因
  reason?: string;
  // 是否可以审核
  canApproval?: boolean;
  // 审核日志
  history?: any[];
}

export type TOnConfirmConfig = (configState: WritableComputedRef<IConfigState>) => void;
