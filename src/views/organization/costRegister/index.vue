<template>
  <div class="cost-register-list">
    <div class="cost-register-list-search filter-container">
      <div class="filter-item">
        <span>生效期</span>
        <a-range-picker v-model:value="dateRange" />
      </div>
      <div class="filter-item">
        <span>投管人名称</span>
        <a-select
          v-model:value="orgId"
          show-search
          mode="multiple"
          :options="orgOptions"
          :filter-option="filterOption"
          allow-clear
          style="width: 220px"
        ></a-select>
      </div>
      <div class="filter-item">
        <span>成本类型</span>
        <a-select
          v-model:value="type"
          show-search
          mode="multiple"
          :options="typeOptions"
          :filter-option="filterOption"
          allow-clear
          style="width: 220px"
        ></a-select>
      </div>

      <div class="m-filter-btns">
        <a-button type="primary" @click="handleSearch">查询</a-button>
        <a-button @click="onConfig({ flag: '0', record: '' })">新增</a-button>
        <a-button @click="handleExport">导出</a-button>
      </div>
    </div>
    <div class="cost-register-list-table">
      <custom-table
        v-model:page-num="pageNum"
        v-model:page-size="pageSize"
        :table-total="total"
        :table-data="tableData"
        :columns="columns"
        :default-page-size="defaultPageSize"
        :page-size-list="pageSizeList"
        :row-class-name="rowClassName()"
        :scroll="{ x: 2600, y: 660 }"
        :sticky="false"
        :on-submit="onTableSubmit"
      >
        <template #type="{ record }">
          {{ typeObj[record.type] }}
        </template>
        <template #rel="{ record }">
          {{ record.rel === '1' ? '是' : '否'  }}
        </template>
        <template #fileName="{ record }">
          <div>
            <span> {{ record.fileName  }}</span>
            <a-button @click="() =>viewFile(record.fileName)">
            点击预览
          </a-button>
          </div>
         
        </template>
        <!-- 详情 -->
        <template #detail="{ record }">
          <a-button type="link" @click="onConfig({ flag: '0', record })">
            配置
          </a-button>
          <a-popconfirm
            :title="`确认删除[${record.name}]？`"
            ok-text="是"
            cancel-text="否"
            @confirm="() => onDel(record)"
          >
            <a-button type="link">删除</a-button>
          </a-popconfirm>
        </template>
      </custom-table>
    </div>
    <config-modal
      :on-config-state="onConfigState"
      :on-confirm-config="onConfirmConfig"
    />
    <view-file ref="viewFileRef"></view-file>
  </div>
</template>

<script lang="ts">
import { defineComponent, onMounted, reactive, toRefs, ref } from 'vue';
import { message } from 'ant-design-vue';
import dayjs from 'dayjs';
import type { Dayjs } from 'dayjs';

import CustomTable, {
  useCustomTable,
} from '@/components/common/CustomTable.vue';
import ConfigModal from './components/configModal.vue';
import ViewFile from './components/viewFile.vue';

import { onConfigHook } from './hook';

import ajax from '@/server/http';
import { tableRelate, typeOptions, typeObj } from './constants';
import { ReconListTableData } from './types';

type RangeValue = [Dayjs, Dayjs];
type iState = {
  total: number; // 表格记录总数
  tableData: ReconListTableData[]; // 表格数据
  orgId: string[]; // 投管人
  type: string[]; // 成本项类型
  dateRange: RangeValue; // 生效期
  orgOptions: { value: string; label: string }[]; // 投管人下拉
};

export default defineComponent({
  name: 'CostRegister',
  components: {
    CustomTable,
    ConfigModal,
    ViewFile,
  },
  setup() {
    const viewFileRef = ref();
    const { table, onTableSubmit, onTableChange, rowClassName } =
      useCustomTable(tableRelate.defaultPageSize);
    const { onConfigState, onConfig, onConfirmConfig } =
      onConfigHook(fetchTable);
    const state = reactive<iState>({
      total: 0,
      tableData: [],
      type: [],
      orgId: [],
      dateRange: [dayjs(), dayjs()],
      orgOptions: [],
    });

    const init = () => {
      getOrganization();
      fetchTable();
    };
    // 获取投管人
    const getOrganization = () => {
      ajax('getOrganization').then((res) => {
        if (res) {
          state.orgOptions = (res || []).reduce((pre, cur) => {
            return pre.concat({
              label: cur.orgName,
              value: cur.orgId,
            });
          }, []);
        }
      });
    };
    const filterOption = (input: string, option: any) => {
      return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
    };
    // 查询表格
    function fetchTable() {
      ajax('getCostList', {
        startDate: state.dateRange[0]
          ? dayjs(state.dateRange[0]).format('YYYYMMDD')
          : '',
        endDate: state.dateRange[1]
          ? dayjs(state.dateRange[1]).format('YYYYMMDD')
          : '',
        type: state.type.join(','),
        orgId: state.orgId.join(','),
        pageNum: table.pageNum,
        pageSize: table.pageSize,
      })
        .then((res) => {
          if (res) {
            state.total = res.total;
            state.tableData = res.list;
          } else {
            state.total = 0;
            state.tableData = [];
          }
        })
        .catch(() => {
          state.total = 0;
          state.tableData = [];
        });
    }
    // 搜索
    const handleSearch = () => {
      onTableChange(fetchTable);
    };

    const handleExport = () => {
      ajax('exportCostInfo', {
        startDate: state.dateRange[0]
          ? dayjs(state.dateRange[0]).format('YYYYMMDD')
          : '',
        endDate: state.dateRange[1]
          ? dayjs(state.dateRange[1]).format('YYYYMMDD')
          : '',
        type: state.type.join(','),
        orgId: state.orgId.join(','),
      }).then((res) => {
        message.success('导出成功');
      });
    };

    const onDel = (record: ReconListTableData) => {
      ajax('delCostInfo', {
        id: record.id,
        name: record.name
      }).then(() => {
        message.success('删除成功');
        handleSearch();
      });
    };

    const viewFile = (fileName: string) => {
      viewFileRef.value.viewFile(fileName)
    };

    onMounted(init);
    return {
      ...toRefs(table),
      ...toRefs(state),
      ...tableRelate,
      typeOptions,
      typeObj,
      onTableSubmit: onTableSubmit(fetchTable),
      rowClassName,
      filterOption,
      handleSearch,
      handleExport,
      onConfig,
      onDel,
      onConfigState,
      onConfirmConfig,
      viewFileRef,
      viewFile
    };
  },
});
</script>

<style lang="less" scoped>
@import './index.less';
</style>
