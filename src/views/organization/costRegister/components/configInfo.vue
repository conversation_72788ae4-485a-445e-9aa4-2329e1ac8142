<template>
  <div class="u-config-modal-container">
    <a-form ref="formRef" :model="configState">
      <h3 style="margin: 0 0 20px">成本项信息</h3>
      <a-divider />
      <a-form-item label="成本项名称" required name="name">
        <a-input
          v-model:value="configState.name"
          :disabled="configState.disabled"
        />
      </a-form-item>
      <a-form-item label="成本类型" required name="type">
        <a-select
          v-model:value="configState.type"
          :disabled="configState.disabled"
        >
          <a-select-option
            v-for="option in typeOptions"
            :key="option.value"
            :value="option.value"
          >
            {{ option.label }}
          </a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="开始日期" required name="startDate">
        <a-date-picker
          v-model:value="configState.startDate"
          style="width: 100%"
          :disabled="configState.disabled"
        />
      </a-form-item>
      <a-form-item
        v-if="
          [EType.PerformanceDeduce, EType.CustomReturn].includes(
            configState.type
          )
        "
        label="扣减比例(%)"
        required
        name="costRatio"
      >
        <a-input-number
          v-model:value="configState.costRatio"
          style="width: 100%"
          :min="0"
          :max="100"
          :step="0.01"
          string-mode
          :disabled="configState.disabled"
        />
      </a-form-item>
      <a-form-item
        v-if="
          [EType.FixedCost, EType.FeeReimbursement].includes(configState.type)
        "
        label="成本金额(元)"
        required
        name="costAmount"
      >
        <a-input-number
          v-model:value="configState.costAmount"
          style="width: 100%"
          :min="0"
          :step="0.01"
          string-mode
          :disabled="configState.disabled"
        />
      </a-form-item>
      <distri-fundCode
        v-if="[EType.Gainsharing].includes(configState.type)"
        :config-state="configState"
      ></distri-fundCode>
      <a-form-item
        v-if="
          [EType.CustomReturn, EType.PerformanceDeduce].includes(
            configState.type
          )
        "
        label="结束日期"
        required
        name="endDate"
      >
        <a-date-picker
          v-model:value="configState.endDate"
          style="width: 100%"
          :disabled="configState.disabled"
        />
      </a-form-item>
      <a-form-item label="成本项说明" required name="describe">
        <a-textarea
          v-model:value="configState.describe"
          :disabled="configState.disabled"
        />
      </a-form-item>
      <a-form-item name="fileList" label="证明材料">
        <a-upload
          v-if="!configState.disabled"
          v-model:fileList="configState.fileList"
          :multiple="false"
          :max-count="1"
          :before-upload="checkImageType"
          :custom-request="customRequest"
        >
          <a-button>
            <template #icon>
              <UploadOutlined />
            </template>
            点击上传
          </a-button>
        </a-upload>
        <div v-if="configState.disabled">
          <span>{{ configState.fileName }}</span>
          <a-button @click="viewFile">
            <template #icon>
              <UploadOutlined />
            </template>
            点击预览
          </a-button>
        </div>
      </a-form-item>
      <div v-if="[EType.FeeReimbursement].includes(configState.type)">
        <a-divider />
        <h3 style="margin: 20px 0">销售人员关联关系</h3>
        <a-form-item label="销售人员" required name="saleId">
          <a-select
            v-model:value="configState.saleId"
            show-search
            :disabled="configState.disabled"
            :options="saleIdsOptions"
            :filter-option="filterOption"
            allow-clear
            style="width: 220px"
          ></a-select>
        </a-form-item>
      </div>
      <invest-account
        v-if="![EType.FeeReimbursement].includes(configState.type)"
        :config-state="configState"
      ></invest-account>
      <a-divider />
      <a-form-item label="备注" name="remark">
        <a-textarea
          v-model:value="configState.remark"
          :disabled="configState.disabled"
        />
      </a-form-item>
    </a-form>
    <view-file ref="viewFileRef"></view-file>
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  PropType,
  computed,
  ref,
  toRefs,
  reactive,
} from 'vue';
import { message } from 'ant-design-vue';

import InvestAccount from './investAccount.vue';
import DistriFundCode from './distriFundCode.vue';
import ViewFile from './viewFile.vue';

import ajax from '@/server/http';

import { initConfigState, EType, typeOptions } from '../constants';

import { IConfigState, TOnConfirmConfig } from '../types';

interface iState {
  saleIdsOptions: { value: string; label: string }[];
}

export default defineComponent({
  name: 'ConfigModal',
  components: {
    InvestAccount,
    DistriFundCode,
    ViewFile,
  },
  props: {
    onConfigState: {
      type: Object as PropType<IConfigState>,
      default: () => initConfigState,
    },
    onConfirmConfig: {
      type: Function as PropType<TOnConfirmConfig>,
      default: () => {},
    },
  },

  emits: {
    'update:onConfigState'(payload) {
      return payload;
    },
  },
  setup(props, context) {
    const state = reactive<iState>({
      saleIdsOptions: [],
    });
    const formRef = ref();
    const viewFileRef = ref();
    const configState = computed({
      get: () => props.onConfigState,
      set: (val) => {
        context.emit('update:onConfigState', val);
      },
    });

    const onOk = () => {
      formRef.value.validate().then(() => {
        props.onConfirmConfig(configState);
      });
    };

    // 获取销售人员
    const getDivideSaleList = () => {
      ajax('getDivideSaleList', { isNeedSelf: false }).then((res) => {
        if (res) {
          state.saleIdsOptions = (res || []).reduce((pre, cur) => {
            return pre.concat({
              label: cur.saleName,
              value: cur.saleId,
            });
          }, []);
        }
      });
    };

    const checkImageType = (file) => {
      const isImage = file.type.startsWith('image/');

      if (!isImage) {
        message.error('只能上传图片文件');
      }

      return isImage;
    };

    const customRequest = ({ file, onSuccess, onError }) => {
      const formData = new FormData();
      formData.append('file', file);
      ajax('uploadCostFile', formData)
        .then((res) => {
          // eslint-disable-next-line vue/no-mutating-props
          props.onConfigState.fileName = res;
          onSuccess(res);
        })
        .catch((err) => {
          onError(err.message);
        });
    };

    const viewFile = () => {
      viewFileRef.value.viewFile(props.onConfigState.fileName);
    };

    const init = () => {
      getDivideSaleList();
    };

    const filterOption = (input: string, option: any) => {
      return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
    };

    init();

    return {
      ...toRefs(state),
      formRef,
      viewFileRef,
      configState,
      onOk,
      EType,
      typeOptions,
      filterOption,
      checkImageType,
      customRequest,
      viewFile,
    };
  },
});
</script>
