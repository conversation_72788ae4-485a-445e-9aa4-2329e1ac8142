<template>
  <a-space
    v-for="(
      commissionRationItem, index
    ) in configState.commissionRatioInfoList"
    :key="commissionRationItem.id"
    style="display: flex; margin-bottom: 8px"
    align="baseline"
  >
    <a-form-item
      :name="['commissionRatioInfoList', index, 'fundCode']"
      label="基金代码"
      required
    >
      <a-input
        v-model:value="commissionRationItem.fundCode"
        :options="[]"
        style="width: 90px"
        :disabled="configState.disabled"
      />
    </a-form-item>
    <a-form-item
      label="管理费分成比例(%)"
      :name="['commissionRatioInfoList', index, 'customerRatio']"
      required
    >
      <a-input-number
        v-model:value="commissionRationItem.customerRatio"
        style="width: 80px"
        :min="0"
        :max="100"
        :step="0.01"
        string-mode
        :disabled="configState.disabled"
      />
    </a-form-item>
    <a-form-item
      label="销售服务费分成比例(%)"
      :name="['commissionRatioInfoList', index, 'saleRatio']"
      required
    >
      <a-input-number
        v-model:value="commissionRationItem.saleRatio"
        style="width: 80px"
        :min="0"
        :max="100"
        :step="0.01"
        string-mode
        :disabled="configState.disabled"
      />
    </a-form-item>
    <MinusCircleOutlined
      v-if="!configState.disabled"
      @click="del(commissionRationItem)"
    />
  </a-space>
  <a-form-item>
    <a-button v-if="!configState.disabled" type="dashed" block @click="add">
      <PlusOutlined />
      添加
    </a-button>
  </a-form-item>
</template>

<script lang="ts">
import { defineComponent, PropType, toRefs } from 'vue';
import { message } from 'ant-design-vue';
import { MinusCircleOutlined, PlusOutlined } from '@ant-design/icons-vue';

import { initConfigState } from '../constants';

import { IDistriFundCode, IConfigState } from '../types';

export default defineComponent({
  name: 'DistriFundCode',
  components: {
    MinusCircleOutlined,
    PlusOutlined,
  },
  props: {
    configState: {
      type: Object as PropType<IConfigState>,
      default: () => initConfigState,
    },
  },
  setup(props) {
    const add = () => {
      // eslint-disable-next-line vue/no-mutating-props
      props.configState.commissionRatioInfoList.push({
        fundCode: '',
        saleRatio: '',
        customerRatio: '',
        id: Date.now(),
      });
    };

    const del = (commissionRationItem: IDistriFundCode) => {
      if (props.configState.commissionRatioInfoList.length === 1) {
        message.error('至少保留一个');
        return;
      }
      let index =
        props.configState.commissionRatioInfoList.indexOf(
          commissionRationItem
        );
      if (index !== -1) {
        // eslint-disable-next-line vue/no-mutating-props
        props.configState.commissionRatioInfoList.splice(index, 1);
      }
    };
    return {
      ...toRefs(props),
      add,
      del,
    };
  },
});
</script>
