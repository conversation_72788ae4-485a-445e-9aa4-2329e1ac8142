<template>
  <a-modal v-if="configState.visible" v-model:visible="configState.visible" ok-text="提交审核" :closable="false" width="1160px" title="成本项详情" @ok="onOk">
    <config-info ref="configInfoRef" :on-config-state="onConfigState" :on-confirm-config="onConfirmConfig"></config-info>
  </a-modal>
</template>

<script lang="ts">
import { defineComponent, PropType, computed, ref, toRefs } from 'vue';

import ConfigInfo from './configInfo.vue';

import { initConfigState } from '../constants';

import { IConfigState, TOnConfirmConfig } from '../types';

export default defineComponent({
  name: 'ConfigModal',
  components: {
    ConfigInfo,
  },
  props: {
    onConfigState: {
      type: Object as PropType<IConfigState>,
      default: () => initConfigState,
    },
    onConfirmConfig: {
      type: Function as PropType<TOnConfirmConfig>,
      default: () => { },
    },
  },
  emits: {
    'update:onConfigState'(payload) {
      return payload;
    },
  },
  setup(props, context) {
    const configInfoRef = ref()
    const configState = computed({
      get: () => props.onConfigState,
      set: (val) => {
        context.emit('update:onConfigState', val);
      },
    });

    const onOk = () => {
      configInfoRef.value.onOk()
    }

    return {
      configState,
      onOk,
      ...toRefs(props),
      configInfoRef
    };
  },
});
</script>
