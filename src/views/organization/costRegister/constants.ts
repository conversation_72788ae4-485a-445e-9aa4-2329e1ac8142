
import dayjs from "dayjs";

const defaultPageSize = 20;
const pageSizeList = ['10', '20', '50', '100'];
const columns = [
  {
    dataIndex: 'name',
    title: '成本项名称',
    width: 200,
  },
  {
    dataIndex: 'type',
    title: '成本类型',
    width: 160,
    slotName: 'type',
  },
  {
    dataIndex: 'costAmount',
    title: '成本金额（元）',
    width: 200,
  },
  {
    dataIndex: 'costRatio',
    title: '扣减比例（%）',
    width: 200,
  },
  {
    dataIndex: 'startDate',
    title: '开始日期',
    width: 100,
  },
  {
    dataIndex: 'endDate',
    title: '结束日期',
    width: 100,
  },
  {
    dataIndex: 'describe',
    title: '说明',
    width: 350,
  },
  {
    dataIndex: 'fileName',
    title: '证明材料',
    width: 220,
    slotName: 'fileName',
  },
  {
    dataIndex: 'remark',
    title: '备注',
    width: 220,
  },
  {
    dataIndex: 'saleName',
    title: '关联销售人员',
    width: 100,
  },
  {
    dataIndex: 'rel',
    title: '是否关联投资账户',
    width: 100,
    slotName: 'rel',
  },
  {
    dataIndex: 'operate',
    title: '操作',
    fixed: 'right',
    width: 150,
    slotName: 'detail',
  },
];
export const tableRelate = {
  defaultPageSize,
  pageSizeList,
  columns,
};

// 成本项类型
export enum EType {
  // 固定成本
  FixedCost = "1",
  // 业绩扣减比例
  PerformanceDeduce = "2",
  // 客户返点
  CustomReturn = "3",
  // 费用报销
  FeeReimbursement = "4",
  // 收入分成
  Gainsharing = "5",
}
export const typeOptions = [
  {
    label: '客户返点',
    value: EType.CustomReturn,
  },
  {
    label: '业绩扣减比例',
    value: EType.PerformanceDeduce,
  },
  {
    label: '固定成本',
    value: EType.FixedCost,
  },
  {
    label: '费用报销',
    value: EType.FeeReimbursement,
  },
  {
    label: '收入分成',
    value: EType.Gainsharing,
  },
];

export const typeObj = {
  [EType.CustomReturn]: '客户返点',
  [EType.PerformanceDeduce]: '业绩扣减比例',
  [EType.FixedCost]: '固定成本',
  [EType.FeeReimbursement]: '费用报销',
  [EType.Gainsharing]: '收入分成',
}

export enum EHandleType {
  CostRegister = '0',
  CostInvestAudit = '1'
}

export const initConfigState = {
  // 是否显示弹框
  visible: false,
  id: '',
  // 成本项名称
  name: '',
  // 成本类型
  type: EType.CustomReturn,
  // 扣减比例
  costRatio: '',
  // 成本金额
  costAmount: '',
  // 开始日期
  startDate: '',
  // 结束日期
  endDate: '',
  // 成本项说明
  describe: '',
  // 材料上传
  fileList: [],
  fileName: '',
  // 投管人/投资账户信息
  costProductList: [{
    orgId: undefined,
    fofIds: [],
    id: Date.now(),
  }],
  saleId: '',
  // 分配基金管理费、销售服务费
  commissionRatioInfoList: [{
    fundCode: '',
    saleRatio: '',
    customerRatio: '',
    id: Date.now(),
  }],
  // 备注
  remark: '',
  // 申请人
  applyRoleName: '',
  applyType: '',
  // 申请类型
  applyTypeName: '',
  handleType: EHandleType.CostRegister,
  // 是否禁用
  disabled: false,
  // 审核不通过原因
  reason: '',
  // 是否可以审核
  canApproval: false,
  // 审核日志
  history: []
};

export const getInitConfigState =() => {
  return JSON.parse(JSON.stringify(initConfigState))
}
