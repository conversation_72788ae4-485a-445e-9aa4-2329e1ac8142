const defaultPageSize = 20;
const pageSizeList = ['10', '20', '50', '100'];
const columns = [
  {
    dataIndex: 'name',
    title: '成本项名称',
    width: 200,
  },
  {
    dataIndex: 'type',
    title: '成本类型',
    width: 160,
    slotName: 'type',
  },
  {
    dataIndex: 'costAmount',
    title: '成本金额（元）',
    width: 200,
  },
  {
    dataIndex: 'costRatio',
    title: '扣减比例（%）',
    width: 200,
  },
  {
    dataIndex: 'startDate',
    title: '开始日期',
    width: 100,
  },
  {
    dataIndex: 'endDate',
    title: '结束日期',
    width: 100,
  },
  {
    dataIndex: 'describe',
    title: '说明',
    width: 350,
  },
  {
    dataIndex: 'fileName',
    title: '证明材料',
    width: 220,
    slotName: 'fileName',
  },
  {
    dataIndex: 'remark',
    title: '备注',
    width: 220,
  },
  {
    dataIndex: 'saleName',
    title: '关联销售人员',
    width: 100,
  },
  {
    dataIndex: 'rel',
    title: '是否关联商机',
    width: 100,
    slotName: 'rel',
  },
  {
    dataIndex: 'operate',
    title: '操作',
    fixed: 'right',
    width: 150,
    slotName: 'detail',
  },
];
export const tableRelate = {
  defaultPageSize,
  pageSizeList,
  columns,
};

// 成本项类型
export enum EDeductionype {
  // 资源扣减
  ResourceDeduction = "1",
  // 合作扣减
  CooperativeDeduction = "2",
  // 返点扣减
  ReturnDecution = "3",
  // 差旅费
  TravelExpenses = "4",
  // 收入分成
  Gainsharing = "5",
}
export const typeOptions = [
  {
    label: '返点扣减',
    value: EDeductionype.ReturnDecution,
  },
  {
    label: '合作扣减',
    value: EDeductionype.CooperativeDeduction,
  },
  {
    label: '资源扣减',
    value: EDeductionype.ResourceDeduction,
  },
  {
    label: '差旅费',
    value: EDeductionype.TravelExpenses,
  },
  {
    label: '收入分成',
    value: EDeductionype.Gainsharing,
  },
];

export const typeObj = {
  [EDeductionype.ReturnDecution]: '返点扣减',
  [EDeductionype.CooperativeDeduction]: '合作扣减',
  [EDeductionype.ResourceDeduction]: '资源扣减',
  [EDeductionype.TravelExpenses]: '差旅费',
  [EDeductionype.Gainsharing]: '收入分成',
}

export enum EHandleType {
  CostRegister = '0',
  CostInvestAudit = '1'
}

export const initConfigState = {
  // 是否显示弹框
  visible: false,
  id: '',
  // 成本项名称
  name: '',
  // 成本类型
  type: EDeductionype.ReturnDecution,
  // 扣减比例
  costRatio: '',
  // 成本金额
  costAmount: '',
  // 开始日期
  startDate: '',
  // 结束日期
  endDate: '',
  // 成本项说明
  describe: '',
  // 材料上传
  fileList: [],
  fileName: '',
  // 投管人/商机信息
  costProductList: [{
    orgId: undefined,
    opportunityIds: [],
    id: Date.now(),
  }],
  saleId: '',
  // 分配基金管理费、销售服务费
  commissionRatioInfoList: [{
    fundCode: '',
    saleRatio: '',
    customerRatio: '',
    id: Date.now(),
  }],
  // 备注
  remark: '',
  // 申请人
  applyRoleName: '',
  // 申请类型
  applyTypeName: '',
  handleType: EHandleType.CostRegister,
  // 是否禁用
  disabled: false,
  // 审核不通过原因
  reason: '',
  // 是否可以审核
  canApproval: false,
  // 审核日志
  history: []
};

export const getInitConfigState =() => {
  return JSON.parse(JSON.stringify(initConfigState))
}
