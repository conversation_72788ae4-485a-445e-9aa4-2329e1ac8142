<template>
  <a-modal
    v-model:visible="visible"
    ok-text="关闭"
    :closable="false"
    width="560px"
    title="文件预览"
    @ok="onOk"
  >
    <a-image :width="400" :height="400" :src="src" />
  </a-modal>
</template>

<script lang="ts">
import { defineComponent, reactive, ref, toRefs } from 'vue';

import ajax from '@/server/http';

interface iState {
  visible: boolean;
  src: string;
}

export default defineComponent({
  name: 'ViewFile',
  setup() {
    const state = reactive<iState>({
      visible: false,
      src: '',
    });
    const viewFile = (fileName) => {
      state.visible = true;
      ajax('viewCostFile', { fileName }).then((res) => {
        state.src = `data:image/png;base64,${res}`;
      });
    };
    const onOk = () => {
      state.visible = false;
    };

    return {
      ...toRefs(state),
      viewFile,
      onOk,
    };
  },
});
</script>
