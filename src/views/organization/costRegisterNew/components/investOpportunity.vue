<template>
  <a-divider />
  <h3 style="margin: 20px 0">投管人/商机关联关系</h3>
  <a-space
    v-for="(investItem, index) in configState.costProductList"
    :key="investItem.id"
    style="display: flex; margin-bottom: 8px"
    align="baseline"
  >
    <a-form-item
      :name="['costProductList', index, 'orgId']"
      label="投管人"
      required
    >
      <a-select
        v-model:value="investItem.orgId"
        show-search
        :options="orgOptions"
        :filter-option="filterOption"
        style="width: 320px"
        :disabled="configState.disabled"
        @change="(val) => changeOrg(val, investItem)"
      ></a-select>
    </a-form-item>
    <a-form-item
      label="商机"
      :name="['costProductList', index, 'opportunityIds']"
      required
    >
      <a-select
        v-model:value="investItem.opportunityIds"
        show-search
        mode="multiple"
        :options="opportunityOptionsObj[investItem.id]"
        :filter-option="filterOption"
        allow-clear
        style="width: 500px"
        :disabled="configState.disabled"
      ></a-select>
    </a-form-item>
    <MinusCircleOutlined
      v-if="!configState.disabled"
      @click="del(investItem)"
    />
  </a-space>
  <a-form-item>
    <a-button v-if="!configState.disabled" type="dashed" block @click="add">
      <PlusOutlined />
      添加
    </a-button>
  </a-form-item>
</template>

<script lang="ts">
import { defineComponent, PropType, toRefs, reactive, watch } from 'vue';
import { message } from 'ant-design-vue';
import { MinusCircleOutlined, PlusOutlined } from '@ant-design/icons-vue';

import ajax from '@/server/http';

import { initConfigState } from '../constants';

import { IInvestItem, IConfigState } from '../types';

type TOptions = { value: string; label: string }[];

interface iState {
  orgOptions: TOptions; //投管人下拉
  opportunityOptionsObj: {
    [index: string]: TOptions;
  }; //商机下拉
}

export default defineComponent({
  name: 'InvestOpportunity',
  components: {
    MinusCircleOutlined,
    PlusOutlined,
  },
  props: {
    configState: {
      type: Object as PropType<IConfigState>,
      default: () => initConfigState,
    },
  },
  setup(props) {
    const watchFn = (list) => {
      list.map((item) => {
        if (item.orgId) {
          changeOrg(item.orgId, item);
        }
      });
    };
    watchFn(props.configState.costProductList);
    watch(
      () => props.configState.costProductList,
      (newValue) => {
        newValue.map((item) => {
          changeOrg(item.orgId, item);
        });
      }
    );
    // eslint-disable-next-line vue/no-setup-props-destructure
    const id = props.configState.costProductList[0].id;
    const state = reactive<iState>({
      orgOptions: [],
      opportunityOptionsObj: {
        [id]: [],
      },
    });
    // 获取投管人
    const getOrganization = () => {
      ajax('getOrganization').then((res) => {
        if (res) {
          state.orgOptions = (res || []).reduce((pre, cur) => {
            return pre.concat({
              label: cur.orgName,
              value: cur.orgId,
            });
          }, []);
        }
      });
    };
    // 获取商机列表
    function getOpportunityList(orgId: string, investItem: IInvestItem) {
      ajax('getOpportunityList', { orgId }).then((res) => {
        if (res) {
          state.opportunityOptionsObj[investItem.id] = (res || []).reduce(
            (pre, cur) => {
              return pre.concat({
                label: cur.opportunityName,
                value: cur.opportunityId,
              });
            },
            [{ value: -1, label: '始终勾选全部商机' }]
          );
        }
      });
    };

    const init = () => {
      getOrganization();
    };

    function changeOrg(val: string, investItem: IInvestItem) {
      if (!val) {
        return;
      }
      getOpportunityList(val, investItem);
    };
    const add = () => {
      // eslint-disable-next-line vue/no-mutating-props
      props.configState.costProductList.push({
        orgId: undefined,
        opportunityIds: [],
        id: Date.now(),
      });
    };

    const del = (investItem: IInvestItem) => {
      if (props.configState.costProductList.length === 1) {
        message.error('至少保留一个');
        return;
      }
      let index = props.configState.costProductList.indexOf(investItem);
      if (index !== -1) {
        // eslint-disable-next-line vue/no-mutating-props
        props.configState.costProductList.splice(index, 1);
      }
    };

    const filterOption = (input: string, option: any) => {
      return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
    };

    init();

    return {
      ...toRefs(state),
      ...toRefs(props),
      add,
      del,
      changeOrg,
      filterOption,
    };
  },
});
</script>
