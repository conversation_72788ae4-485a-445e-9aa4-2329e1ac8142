import { reactive, toRaw } from 'vue';
import { message } from 'ant-design-vue';
import dayjs from 'dayjs';

import ajax from '@/server/http';

import { getInitConfigState, EHandleType, EDeductionype } from './constants';

import { IConfigState, TOnConfirmConfig } from './types';

export const onConfigHook = (fetchTable) => {
  const onConfigState = reactive<IConfigState>(getInitConfigState());
  const onConfig = ({ flag, record }) => {
    if (record) {
      if (flag === EHandleType.CostInvestAudit) {
        ajax('getApprovalInner', { id: record.id }).then((res) => {
          const content = res.content || {};
          const history = res.history || [];
          Object.keys(content).forEach((key) => {
            if (['startDate', 'endDate'].includes(key)) {
              onConfigState[key] = content[key] ? dayjs(content[key]) : '';
            } else {
              onConfigState[key] = content[key];
            }
          });
          onConfigState.history = history;
          onConfigState.id = record.id;
          onConfigState.applyRoleName = record.applyRoleName;
          onConfigState.applyTypeName = record.applyTypeName;
          onConfigState.canApproval = record.canApproval;
          onConfigState.reason = '';
          onConfigState.disabled = true;
        });
      } else {
        ajax('getOpportunityCostDetails', { id: record.id }).then((res) => {
          Object.keys(res).forEach((key) => {
            if (['startDate', 'endDate'].includes(key)) {
              onConfigState[key] = res[key] ? dayjs(res[key]) : '';
            } else {
              onConfigState[key] = res[key];
            }
          });
          onConfigState.id = record.id;
          onConfigState.disabled = false;
        });
      }
    } else {
      Object.keys(onConfigState).forEach((key) => {
        onConfigState[key] = getInitConfigState()[key];
      });
      onConfigState.disabled = false;
    }
    onConfigState.visible = true;
  };
  const onConfirmConfig: TOnConfirmConfig = (configState) => {
    const postQuery = toRaw(configState.value);
    if (!postQuery.fileName) {
      message.error('请上传文件，文件格式只支持图片');
      return;
    }
    if (postQuery.type !== EDeductionype.Gainsharing) {
      postQuery.commissionRatioInfoList = [];
    }
    postQuery.costProductList = (postQuery.costProductList || []).reduce(
      (pre, cur) => {
        let opportunityIds: number[] = [];
        if (cur.opportunityIds.includes(-1)) {
          opportunityIds = [-1];
        } else {
          opportunityIds = cur.opportunityIds;
        }
        return pre.concat({
          opportunityIds,
          orgId: cur.orgId,
        });
      },
      []
    );
    postQuery.commissionRatioInfoList = (
      postQuery.commissionRatioInfoList || []
    ).reduce((pre, cur) => {
      return pre.concat({
        customerRatio: cur.customerRatio,
        fundCode: cur.fundCode,
        saleRatio: cur.saleRatio,
      });
    }, []);
    postQuery.startDate = postQuery.startDate
      ? dayjs(postQuery.startDate).format('YYYYMMDD')
      : '';
    postQuery.endDate = postQuery.endDate
      ? dayjs(postQuery.endDate).format('YYYYMMDD')
      : '';
    if (postQuery.id) {
      ajax('updateOpportunityCostInfo', postQuery).then((res) => {
        setTimeout(() => {
          message.success('操作成功');
        }, 0);
        onConfigState.visible = false;
        fetchTable();
      });
    } else {
      ajax('addOpportunityCostInfo', postQuery).then((res) => {
        setTimeout(() => {
          message.success('操作成功');
        }, 0);
        onConfigState.visible = false;
        fetchTable();
      });
    }
  };

  return {
    onConfigState,
    onConfig,
    onConfirmConfig,
  };
};
