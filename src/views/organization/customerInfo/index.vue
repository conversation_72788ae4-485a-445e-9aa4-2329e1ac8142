<template>
  <div id="root"></div>
</template>

<script lang="ts">
import amisEnv from '@/utils/amisEnv';
import { amisRemoteEmbed } from '@king-fisher/crm-amis-next';
import { DEFAULT_SYSTEM_ID, AMIS_ENV } from '@/utils/request';

export default {
  mounted() {
    const amisLib = window.amisRequire('amis');
    amisLib.registerFilter('filterFile', function (val) {
      if (Object.prototype.toString.call(val) === '[object File]') {
        return val;
      } else {
        return '';
      }
    });

    const CURRENT_PAGE_ID = 1138;
    amisRemoteEmbed(
      '#root',
      {
        id: CURRENT_PAGE_ID,
        systemId: DEFAULT_SYSTEM_ID,
        env: AMIS_ENV,
      },
      {
        // 开启本地缓存
        useLocalCache: true,
      },
      {},
      amisEnv()
    ).then((scoped) => {
      this.amisScoped = scoped;
    });
  },
};
</script>
