<template>
  <a-modal
    v-model:visible="configState.visible"
    ok-text="提交审核"
    :closable="false"
    width="1100px"
    title="审核详情"
    :footer="null"
  >
    <div class="u-audit-title">
      <div>
        <span>申请类型：</span>
        <p>{{ onConfigState.applyTypeName }}</p>
      </div>
      <div>
        <span>申请人：</span>
        <p>{{ onConfigState.applyRoleName }}</p>
      </div>
    </div>
    <h3 style="margin: 20px 0">申请内容：</h3>
    <config-info
      v-if="
        [
          EApplyType.AddCost,
          EApplyType.UpdateCost,
          EApplyType.DeleteCost,
        ].includes(onConfigState.applyType)
      "
      ref="configInfoRef"
      :on-config-state="onConfigState"
    ></config-info>
    <invest-config
      v-if="onConfigState.applyType === EApplyType.ChangeSaleRelation"
      ref="configInfoRef"
      :config-state="onConfigState"
      disabled
    ></invest-config>
    <config-info-opportunity
      v-if="
        [
          EApplyType.AddCostOpportunity,
          EApplyType.UpdateCostOpportunity,
          EApplyType.DeleteCostOpportunity,
        ].includes(onConfigState.applyType)
      "
      ref="configInfoRef"
      :on-config-state="onConfigState"
    ></config-info-opportunity>
    <h3 style="margin: 20px 0">申请记录：</h3>
    <div class="cost-register-list-table">
      <custom-table
        :table-data="configState.history"
        :columns="auditColumns"
        :row-class-name="rowClassName()"
        :sticky="false"
      ></custom-table>
    </div>
    <div>
      <div v-if="configState.canApproval" style="margin: 20px 0">
        <a-textarea
          v-model:value="configState.reason"
          placeholder="请输入不通过原因"
        />
        <a-popconfirm
          :title="`确认操作？`"
          ok-text="是"
          cancel-text="否"
          @confirm="onAudit(EHandleAudit.NoPass)"
        >
          <a-button type="primary" style="margin: 10px 0 0">不通过</a-button>
        </a-popconfirm>
      </div>
      <div style="margin: 20px 0">
        <a-popconfirm
          v-if="configState.canApproval"
          :title="`确认操作？`"
          ok-text="是"
          cancel-text="否"
          @confirm="onAudit(EHandleAudit.Pass)"
        >
          <a-button type="primary" style="margin-right: 10px">通过</a-button>
        </a-popconfirm>
        <a-button @click="() => (configState.visible = false)">返回</a-button>
      </div>
    </div>
  </a-modal>
</template>

<script lang="ts">
import { defineComponent, PropType, computed, ref, toRefs } from 'vue';
import { message } from 'ant-design-vue';

import CustomTable, {
  useCustomTable,
} from '@/components/common/CustomTable.vue';
import ConfigInfo from '../../costRegister/components/configInfo.vue';
import ConfigInfoOpportunity from '../../costRegisterNew/components/configInfo.vue';
import InvestConfig from '../../investAccount/components/investConfig.vue';

import { tableRelate, EHandleAudit, EApplyType } from '../constants';
import { initConfigState } from '../../costRegister/constants';

import ajax from '@/server/http';

import { IConfigState } from '../../costRegister/types';

export default defineComponent({
  name: 'ConfigModal',
  components: {
    CustomTable,
    ConfigInfo,
    ConfigInfoOpportunity,
    InvestConfig,
  },
  props: {
    onConfigState: {
      type: Object as PropType<IConfigState>,
      default: () => initConfigState,
    },
    handleSearch: {
      type: Function,
      default: () => {},
    },
  },
  emits: {
    'update:onConfigState'(payload) {
      return payload;
    },
  },
  setup(props, context) {
    const { table, rowClassName } = useCustomTable(20);

    const configInfoRef = ref();
    const configState = computed({
      get: () => props.onConfigState,
      set: val => {
        context.emit('update:onConfigState', val);
      },
    });

    const onAudit = (flag: EHandleAudit) => {
      if (flag === EHandleAudit.NoPass && !props.onConfigState.reason) {
        message.error('请输入审核不通过原因');
        return;
      }
      ajax('operateApprovalInner', {
        id: props.onConfigState.id,
        operType: flag,
        notes: props.onConfigState.reason,
      }).then(() => {
        message.success('操作成功');
        // eslint-disable-next-line vue/no-mutating-props
        props.onConfigState.visible = false;
        props.handleSearch();
      });
    };

    return {
      ...toRefs(table),
      rowClassName,
      ...tableRelate,
      configState,
      ...toRefs(props),
      configInfoRef,
      EHandleAudit,
      onAudit,
      EApplyType,
    };
  },
});
</script>
<style lang="less" scoped>
.u-audit-title {
  & > div {
    display: flex;
  }
}
</style>
