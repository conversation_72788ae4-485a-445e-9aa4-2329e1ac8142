import { WritableComputedRef } from "vue";

import { EAuditStatus } from "./constants";

export type ReconListTableData = {
  index: number; // 序号
  key: string;
  id: string;
  operate_asset: string; // 操作
  name: string; // 成本项名称
  type: string; // 成本类型
  costAmount: string; // 金额（元）/折扣（扣减部分比例）
  startDate: string; // 开始日期
  endDate: string; // 结束日期
  describe: string; // 说明
  fileName: string; // 证明材料
  remark: string; // 备注
  saleName: string; // 关联销售人员
  mtime: string; // 关联投管人
  mentionProfit: string; // 关联投资帐户
  order_infos: string[]; // 投资经历订单信息
};

export interface IInvestItem {
  orgId: string;
  fofIds: string[];
  id: number;
}

export interface IDistriFundCode {
  fundCode: string;
  saleRatio: string;
  customerRatio: string;
  id: number;
}

export interface IConfigState {
  // 是否显示弹框
  visible: boolean;
  id: string;
  // 申请日期
  applyDate: string;
  // 申请内容
  applyDesc: string;
  // 申请类型
  applyType: string;
  applyTypeName: string;
  // 申请人
  applyRoleName: string,
  // 审核状态
  status: string;
  // 备注
  notes: string;
  canApproval: boolean;
}

export type TOnConfirmConfig = (configState: WritableComputedRef<IConfigState>) => void;
