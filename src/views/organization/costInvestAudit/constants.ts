const defaultPageSize = 20;
const pageSizeList = ['10', '20', '50', '100'];
const columns = [
  {
    dataIndex: 'applyDate',
    title: '申请日期',
    width: 200,
  },
  {
    dataIndex: 'applyDesc',
    title: '申请内容',
    width: 200,
  },
  {
    dataIndex: 'applyTypeName',
    title: '申请类型',
    width: 160,
  },
  {
    dataIndex: 'applyRoleName',
    title: '申请人',
    width: 100,
  },
  {
    dataIndex: 'status',
    title: '审核状态',
    width: 100,
  },
  {
    dataIndex: 'notes',
    title: '备注',
    width: 250,
  },
  {
    dataIndex: 'operate',
    title: '操作',
    fixed: 'right',
    width: 150,
    slotName: 'detail',
  },
];
const auditColumns = [
  {
    dataIndex: 'date',
    title: '审核日期',
  },
  {
    dataIndex: 'name',
    title: '审核人',
  },
  {
    dataIndex: 'role',
    title: '审核人角色',
  },
  {
    dataIndex: 'result',
    title: '审核结果',
  },
  {
    dataIndex: 'notes',
    title: '备注',
  },
];
export const tableRelate = {
  defaultPageSize,
  pageSizeList,
  columns,
  auditColumns
};

// 操作类型
export enum EOperType {
  // 通过
  Success = "1",
  // 不通过
  OperateAudit = "2",
}

// 审核类型
export enum EAuditStatus {
  // 审核通过
  Success = "1",
  // 业务运营审核中
  OperateAudit = "2",
  // 业务主管审核中
  OperateChargeAudit = "3",
  // 财务审核中
  FinanceAudit = "4",
  // 销售总监审核中
  SaleChiefAudit = "5",
  // 审核不通过
  Fail = "9",
}
export const typeOptions = [
  {
    label: '审核通过',
    value: EAuditStatus.Success,
  },
  {
    label: '业务运营审核中',
    value: EAuditStatus.OperateAudit,
  },
  {
    label: '业务主管审核中',
    value: EAuditStatus.OperateChargeAudit,
  },
  {
    label: '财务审核中',
    value: EAuditStatus.FinanceAudit,
  },
  {
    label: '销售总监审核中',
    value: EAuditStatus.SaleChiefAudit,
  },
  {
    label: '审核不通过',
    value: EAuditStatus.Fail,
  }
];

export enum EHandleAudit {
  Pass = '1',
  NoPass = '2'
}

export enum EApplyType {
  // 成本项添加
  AddCost = '01',
  // 成本项修改
  UpdateCost = '02',
  // 成本项删除
  DeleteCost = '03',
  // 变更销售关系
  ChangeSaleRelation = '04',
  // 商机成本项添加
  AddCostOpportunity = '05',
  // 商机成本项修改
  UpdateCostOpportunity = '06',
  // 商机成本项删除
  DeleteCostOpportunity = '07',
}