<template>
  <div class="cost-register-list">
    <div class="cost-register-list-search filter-container">
      <div class="filter-item">
        <span>日期范围</span>
        <a-range-picker v-model:value="dateRange" />
      </div>
      <div class="filter-item">
        <span>申请人员</span>
        <a-select
          v-model:value="saleIds"
          show-search
          mode="multiple"
          :options="saleIdsOptions"
          :filter-option="filterOption"
          allow-clear
          style="width: 220px"
        ></a-select>
      </div>
      <div class="filter-item">
        <span>审核状态</span>
        <a-select
          v-model:value="status"
          show-search
          mode="multiple"
          :options="typeOptions"
          :filter-option="filterOption"
          allow-clear
          style="width: 220px"
        ></a-select>
      </div>

      <div class="m-filter-btns">
        <a-button type="primary" @click="handleSearch">查询</a-button>
      </div>
    </div>
    <div class="cost-register-list-table">
      <custom-table
        v-model:page-num="pageNum"
        v-model:page-size="pageSize"
        :table-total="total"
        :table-data="tableData"
        :columns="columns"
        :default-page-size="defaultPageSize"
        :page-size-list="pageSizeList"
        :row-class-name="rowClassName()"
        :scroll="{ y: 660 }"
        :sticky="false"
        :on-submit="onTableSubmit"
      >
        <!-- 详情 -->
        <template #detail="{ record }">
          <a-button type="link" @click="onConfig({flag: '1', record})">{{ record.canApproval ? '审核' : '查看' }}</a-button>
        </template>
      </custom-table>
    </div>
    <config-modal
      :on-config-state="onConfigState"
      :handle-search="handleSearch"
    />
  </div>
</template>

<script lang="ts">
import { defineComponent, onMounted, reactive, toRefs } from 'vue';
import dayjs from 'dayjs';
import type { Dayjs } from 'dayjs';

import CustomTable, {
  useCustomTable,
} from '@/components/common/CustomTable.vue';
import ConfigModal from './components/configModal.vue';

import { onConfigHook } from '../costRegister/hook';

import ajax from '@/server/http';
import { tableRelate, typeOptions } from './constants';
import { ReconListTableData } from './types';

type RangeValue = [Dayjs, Dayjs];
type iState = {
  total: number; // 表格记录总数
  tableData: ReconListTableData[]; // 表格数据
  saleIds: string[]; // 销售
  status: string[]; // 审核状态
  dateRange: RangeValue; // 生效期
  saleIdsOptions: { value: string; label: string }[]; // 投管人下拉
};

export default defineComponent({
  name: 'CostRegister',
  components: {
    CustomTable,
    ConfigModal,
  },
  setup() {
    const { table, onTableSubmit, onTableChange, rowClassName } =
      useCustomTable(tableRelate.defaultPageSize);
    const { onConfigState, onConfig, onConfirmConfig } =
      onConfigHook(fetchTable);
    const state = reactive<iState>({
      total: 0,
      tableData: [],
      saleIds: [],
      status: [],
      dateRange: [dayjs(), dayjs()],
      saleIdsOptions: [],
    });

    const init = () => {
      getDivideSaleList();
      fetchTable();
    };
    // 获取销售人员
    const getDivideSaleList = () => {
      ajax('getDivideSaleList', { isNeedSelf: true}).then(res => {
        if (res) {
          state.saleIdsOptions = (res || []).reduce((pre, cur) => {
            return pre.concat({
              label: cur.saleName,
              value: cur.saleId
            })
          }, [])
        }
      })
    }
    const filterOption = (input: string, option: any) => {
      return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
    };
    // 查询表格
    function fetchTable() {
      ajax('getApprovalList', {
        startDate: state.dateRange[0]
          ? dayjs(state.dateRange[0]).format('YYYYMMDD')
          : '',
        endDate: state.dateRange[1]
          ? dayjs(state.dateRange[1]).format('YYYYMMDD')
          : '',
        saleIds: state.saleIds,
        status: state.status,
        pageNo: table.pageNum,
        pageSize: table.pageSize,
      })
        .then((res) => {
          if (res) {
            state.total = res.total;
            state.tableData = res.list || [];
          } else {
            state.total = 0;
            state.tableData = [];
          }
        })
        .catch(() => {
          state.total = 0;
          state.tableData = [];
        });
    }
    // 搜索
    const handleSearch = () => {
      onTableChange(fetchTable);
    };

    const onDel = (id: string) => {};

    onMounted(init);
    return {
      ...toRefs(table),
      ...toRefs(state),
      ...tableRelate,
      typeOptions,
      onTableSubmit: onTableSubmit(fetchTable),
      rowClassName,
      filterOption,
      handleSearch,
      onConfig,
      onDel,
      onConfigState,
      onConfirmConfig,
    };
  },
});
</script>

<style lang="less" scoped>
@import './index.less';
</style>
