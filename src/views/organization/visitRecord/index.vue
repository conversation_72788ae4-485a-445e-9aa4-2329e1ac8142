<template>
  <div id="root"></div>
</template>

<script lang="ts">
import monent from 'moment';
import amisEnv from '@/utils/amisEnv';
import { amisRemoteEmbed } from '@king-fisher/crm-amis-next';
import { DEFAULT_SYSTEM_ID, AMIS_ENV } from '@/utils/request';

export default {
  data() {
    return {
      amisScoped: null,
    };
  },
  mounted() {
    const amisLib = window.amisRequire('amis');
    amisLib.registerFilter('filterFile', function (val) {
      if (Object.prototype.toString.call(val) === '[object File]') {
        return val;
      } else {
        return '';
      }
    });
    /**
     * 添加校验规则
     */
    amisLib.addRule(
      'isAfterNowMonday',
      (values, value) => {
        if (!value) {
          return false;
        }
        const mondayTimeStamp = monent().startOf('isoWeek').valueOf();
        const nowTimeStamp = monent().valueOf();
        const selectTimeStamp = monent(value).valueOf();
        if (
          selectTimeStamp >= mondayTimeStamp &&
          selectTimeStamp <= nowTimeStamp
        ) {
          return true;
        }
        return false;
      },
      '请选择本周一到今天的时间'
    );
    const CURRENT_PAGE_ID = 1133;
    amisRemoteEmbed(
      '#root',
      {
        id: CURRENT_PAGE_ID,
        systemId: DEFAULT_SYSTEM_ID,
        env: AMIS_ENV,
      },
      {
        // 开启本地缓存
        useLocalCache: true,
      },
      {},
      amisEnv()
    ).then((scoped) => {
      this.amisScoped = scoped;
    });
  },
  unmounted() {
    this.amisScoped.unmount();
  },
};
</script>
