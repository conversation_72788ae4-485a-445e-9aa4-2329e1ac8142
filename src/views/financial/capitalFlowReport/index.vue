<template>
  <div class="capital-flow-list">
    <section class="filter-container">
      <div class="filter-item">
        <span class="label">统计时间</span>
        <a-range-picker
          v-model:value="dataPickerValue"
          value-format="YYYYMMDD"
          :style="{ width: '280px' }"
        />
      </div>
      <a-button type="primary" style="margin-right: 20px" @click="onSearch">
        查询
      </a-button>
      <a-button type="primary" @click="handleExports">导出表格</a-button>
    </section>
    <div class="capital-flow-list-table">
      <custom-table
        v-model:page-num="pageNum"
        v-model:page-size="pageSize"
        :table-total="total"
        :table-data="tableData"
        :columns="COLUMNS"
        :default-page-size="PAGE_SIZE"
        :page-size-list="PAGE_SIZE_LIST"
        :row-class-name="rowClassName()"
        :on-submit="onTableSubmit"
      >
        <template #workday="{ text }">
          <div>
            {{ text?.replace(/(\d{4})(\d{2})(\d{2})/g, '$1-$2-$3') || '--' }}
          </div>
        </template>
        <!-- 详情 -->
        <template #detail="{ record }">
          <a-button type="link" @click="handleToDetail(record)">
            查看详情
          </a-button>
        </template>
      </custom-table>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, onMounted, reactive, toRefs, ref } from 'vue';
import { message } from 'ant-design-vue';
import ajax from '@/server/http';
import PARAMS from './const';
import { CapitalFlowListTableData } from './type';
import CustomTable, {
  useCustomTable,
} from '@/components/common/CustomTable.vue';
import { getMonthRange } from '@/utils/fn';
import dayjs, { Dayjs } from 'dayjs';
dayjs.locale('zh-CN');

type iState = {
  total: number; // 表格记录总数
  tableData: CapitalFlowListTableData[]; // 表格数据
};

export default defineComponent({
  name: 'CapitalFlowList',
  components: {
    CustomTable,
  },
  setup() {
    const { table, onTableSubmit, onTableChange, rowClassName } =
      useCustomTable(PARAMS.PAGE_SIZE);
    const state = reactive<iState>({
      total: 0,
      tableData: [],
    });
    // 统计时间数据
    const dataPickerValue = ref<string[]>(
      getMonthRange('YYYYMMDD') as string[]
    );
    //初始化
    const init = () => {
      fetchTable();
    };
    // 查询
    const onSearch = () => {
      onTableChange(fetchTable);
    };
    // 查询表格
    const fetchTable = () => {
      let [startDate, endDate] = dataPickerValue.value
        ? [...dataPickerValue.value]
        : [];
      if (!startDate || !endDate) {
        message.error('请选择时间');
        return;
      }
      ajax('getCapitalFlowReport', {
        startDate,
        endDate,
        page: table.pageNum,
        pageSize: table.pageSize,
      })
        .then((res: any) => {
          if (res) {
            state.tableData = res.crmRecord;
            state.total = res.total;
          } else {
            state.tableData = [];
            state.total = 0;
          }
        })
        .catch((err) => {
          console.log(err);
        });
    };
    // 跳转详情页
    const handleToDetail = (record: CapitalFlowListTableData) => {
      const query = `?startDate=${record.workday}&endDate=${record.workday}`;
      const url = location.href.replace(
        location.hash,
        `#/financial/capitalFlowReport/detail${query}`
      );
      window.open(url);
    };
    // 导出表格
    const handleExports = () => {
      let [startDate, endDate] = dataPickerValue.value
        ? [...dataPickerValue.value]
        : [];
      if (!startDate || !endDate) {
        message.error('请选择时间');
        return;
      }
      ajax('exportCapitalFlowReport', {
        startDate,
        endDate,
      });
    };
    onMounted(init);
    return {
      dataPickerValue,
      onSearch,
      ...toRefs(table),
      ...toRefs(state),
      ...PARAMS,
      onTableSubmit: onTableSubmit(fetchTable),
      rowClassName,
      handleToDetail,
      handleExports,
    };
  },
});
</script>

<style lang="less" scoped>
@import './index.less';
</style>
