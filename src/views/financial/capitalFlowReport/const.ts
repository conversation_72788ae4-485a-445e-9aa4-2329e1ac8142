const PAGE_SIZE = 10;
const PAGE_SIZE_LIST = ['10', '20', '50', '100'];
const COLUMNS = [
  {
    dataIndex: 'workday',
    title: '工作日',
    key: 'workday',
    slotName: 'workday',
  },
  {
    title: '收入',
    children: [
      {
        dataIndex: 'taAmount',
        key: 'taAmount',
        title: '赎回/分红',
      },
      {
        dataIndex: 'dezAmount',
        key: 'dezAmount',
        title: '大额转账',
      },
      {
        dataIndex: 'zfAmount',
        key: 'zfAmount',
        title: '支付机构代扣款',
      },
      {
        dataIndex: 'dzhAmount',
        key: 'dzhAmount',
        title: '垫资',
      },
      {
        dataIndex: 'peAmount',
        key: 'peAmount',
        title: '私募赎回',
      },
      {
        dataIndex: 'qtAmount',
        key: 'qtAmount',
        title: '其他',
      },
    ],
  },
  {
    title: '支出',
    children: [
      {
        dataIndex: 'dtaAmount',
        key: 'dtaAmount',
        title: '基金申购',
      },
      {
        dataIndex: 'dshAmount',
        key: 'dshAmount',
        title: '赎回/分红',
      },
      {
        dataIndex: 'ddzAmount',
        key: 'ddzAmount',
        title: '垫资还款',
      },
      {
        dataIndex: 'dpeAmount',
        key: 'dpeAmount',
        title: '私募申购',
      },
      {
        dataIndex: 'dtkAmount',
        key: 'dtkAmount',
        title: '退款',
      },
      {
        dataIndex: 'dqtAmount',
        key: 'dqtAmount',
        title: '其他',
      },
    ],
  },
  {
    dataIndex: 'detail',
    key: 'detail',
    title: '操作',
    width: 60,
    slotName: 'detail',
    fixed: 'right',
  },
];

const PARAMS = {
  PAGE_SIZE,
  PAGE_SIZE_LIST,
  COLUMNS,
};

export default PARAMS;
