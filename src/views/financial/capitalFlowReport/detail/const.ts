import { DebitOrCredit } from './type';

const PAGE_SIZE = 10;
const PAGE_SIZE_LIST = ['10', '20', '50', '100'];
const COLUMNS = [
  {
    dataIndex: 'tradeDate',
    title: '交易日期',
    key: 'tradeDate',
    slotName: 'date',
  },
  {
    dataIndex: 'svrId',
    title: '交易流水',
    key: 'svrId',
  },
  {
    dataIndex: 'otherAcctName',
    title: '对方账号名称',
    key: 'otherAcctName',
  },
  {
    dataIndex: 'otherAcctNo',
    title: '对方账号',
    key: 'otherAcctNo',
  },
  {
    dataIndex: 'creditAmount',
    title: '入账金额',
    key: 'creditAmount',
  },
  {
    dataIndex: 'debitAmount',
    title: '出账金额',
    key: 'debitAmount',
  },
  {
    dataIndex: 'explainSummary',
    title: '交易摘要',
    key: 'explainSummary',
  },
  {
    dataIndex: 'type',
    title: '流水分类',
    key: 'type',
  },
  {
    dataIndex: 'timestamp',
    title: '交易时间',
    key: 'timestamp',
  },
  {
    dataIndex: 'workday',
    title: '所属工作日',
    key: 'workday',
    slotName: 'date',
  },
];

const DATA_TYPE_OPTIONS = [
  {
    value: 'TA',
    name: '赎回分红款',
    debitOrCredit: DebitOrCredit.in,
  },
  {
    value: 'DEZ,JG',
    name: '大额转账款',
    debitOrCredit: DebitOrCredit.in,
  },
  {
    value: 'DZH',
    name: '垫资借款',
    debitOrCredit: DebitOrCredit.in,
  },
  {
    value: 'ZF',
    name: '支付机构代扣款',
    debitOrCredit: DebitOrCredit.in,
  },
  {
    value: 'PE',
    name: '私募转公募',
    debitOrCredit: DebitOrCredit.in,
  },
  {
    value: 'QT,TK',
    name: '其他',
    debitOrCredit: DebitOrCredit.in,
  },
  {
    value: 'DTA',
    name: '基金公司申购款',
    debitOrCredit: DebitOrCredit.out,
  },
  {
    value: 'DSH',
    name: '客户赎回分红款',
    debitOrCredit: DebitOrCredit.out,
  },
  {
    value: 'DDZ',
    name: '垫资还款',
    debitOrCredit: DebitOrCredit.out,
  },
  {
    value: 'DTK',
    name: '资金清退',
    debitOrCredit: DebitOrCredit.out,
  },
  {
    value: 'DPE',
    name: '公募转私募款',
    debitOrCredit: DebitOrCredit.out,
  },
  {
    value: 'DQT',
    name: '其他',
    debitOrCredit: DebitOrCredit.out,
  },
];
const PARAMS = {
  PAGE_SIZE,
  PAGE_SIZE_LIST,
  COLUMNS,
  DATA_TYPE_OPTIONS,
};

export default PARAMS;
