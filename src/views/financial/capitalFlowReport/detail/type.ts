export type CapitalFlowDetailTableData = {
  seq: number;
  thsBankAcco: string; // 同花顺银行账号
  tradeDate: string; // 交易发生日期
  svrId: string; // 交易流水号
  debitAmount: string; // 借方发生额/出账
  creditAmount: string; // 贷方发生额/入账
  balance: string; // 账户余额
  explainSummary: string; // 用途/摘要
  tradeAddress: string; // 交易地点
  otherAcctNo: string; // 对方账号
  otherAcctName: string; // 对方账号名称
  opBankName: string; // 对方开户行
  timestamp: string; // 时间戳
  remark: string;
  type: string; // 流水类型
  workday: string; // 所属工作日
};

export enum DebitOrCredit {
  'out' = '0', // 出账
  'in' = '1', // 入账
}
