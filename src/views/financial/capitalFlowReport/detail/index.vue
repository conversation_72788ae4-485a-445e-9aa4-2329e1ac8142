<template>
  <div class="capital-flow-detail">
    <section class="filter-container">
      <div class="filter-item">
        <span class="label">统计时间</span>
        <a-range-picker
          v-model:value="dataPickerValue"
          value-format="YYYYMMDD"
          :style="{ width: '280px' }"
        />
      </div>
      <div class="filter-item">
        <span class="label">对方账号</span>
        <a-input
          v-model:value="otherAcctNo"
          placeholder="请输入对方账号"
          :style="{ width: '200px' }"
        />
      </div>
      <div class="filter-item">
        <span class="label">借贷类型</span>
        <a-select
          v-model:value="debitOrCredit"
          allow-clear
          placeholder="请选择借代类型"
        >
          <a-select-option :value="DebitOrCredit.out">出帐</a-select-option>
          <a-select-option :value="DebitOrCredit.in">入账</a-select-option>
        </a-select>
      </div>
      <div class="filter-item">
        <span class="label">数据分类</span>
        <a-select
          v-model:value="dataCate"
          allow-clear
          placeholder="请选择数据分类"
        >
          <a-select-option
            v-for="(item, index) in dataCateOptions"
            :key="index"
            :value="item.value"
          >
            {{ item.name }}
          </a-select-option>
        </a-select>
      </div>
      <a-button type="primary" style="margin-right: 20px" @click="onSearch">
        查询
      </a-button>
      <a-button type="primary" @click="handleExports">导出表格</a-button>
    </section>
    <div class="capital-flow-detail-table">
      <custom-table
        v-model:page-num="pageNum"
        v-model:page-size="pageSize"
        :table-total="total"
        :table-data="tableData"
        :columns="COLUMNS"
        :default-page-size="PAGE_SIZE"
        :page-size-list="PAGE_SIZE_LIST"
        :row-class-name="rowClassName()"
        :on-submit="onTableSubmit"
      >
        <template #date="{ text }">
          <div>
            {{ text.replace(/(\d{4})(\d{2})(\d{2})/g, '$1-$2-$3') || '--' }}
          </div>
        </template>
      </custom-table>
    </div>
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  onMounted,
  reactive,
  toRefs,
  ref,
  computed,
  watch,
} from 'vue';
import { message } from 'ant-design-vue';
import ajax from '@/server/http';
import PARAMS from './const';
import { DebitOrCredit, CapitalFlowDetailTableData } from './type';
import CustomTable, {
  useCustomTable,
} from '@/components/common/CustomTable.vue';
import { getMonthRange, getUrlParam } from '@/utils/fn';
import dayjs, { Dayjs } from 'dayjs';
dayjs.locale('zh-CN');

type iState = {
  total: number; // 表格记录总数
  tableData: CapitalFlowDetailTableData[]; // 表格数据
};

export default defineComponent({
  name: 'CapitalFlowList',
  components: {
    CustomTable,
  },
  setup() {
    const { table, onTableSubmit, onTableChange, rowClassName } =
      useCustomTable(PARAMS.PAGE_SIZE);
    const state = reactive<iState>({
      total: 0,
      tableData: [],
    });

    const startDate = getUrlParam('startDate', 'hash') || '';
    const endDate = getUrlParam('endDate', 'hash') || '';
    // 统计时间数据
    const dataPickerValue = ref<string[]>(
      startDate && endDate
        ? [startDate, endDate]
        : (getMonthRange('YYYYMMDD') as string[])
    );

    // 查询过滤参数
    const filterParams = reactive({
      otherAcctNo: null,
      debitOrCredit: null,
      dataCate: null,
    });
    const dataCateOptions = computed(() => {
      return PARAMS.DATA_TYPE_OPTIONS.filter((item) => {
        return item.debitOrCredit === filterParams.debitOrCredit;
      });
    });
    watch(
      () => filterParams.debitOrCredit,
      () => {
        filterParams.dataCate = null;
      }
    );
    // 查询
    const onSearch = () => {
      onTableChange(fetchTable);
    };
    // 初始化
    const init = () => {
      fetchTable();
    };
    // 查询表格
    const fetchTable = () => {
      let [startDate, endDate] = dataPickerValue.value
        ? [...dataPickerValue.value]
        : [];
      if (!startDate || !endDate) {
        message.error('请选择时间');
        return;
      }
      const params = {
        ...filterParams,
        startDate,
        endDate,
        page: table.pageNum,
        pageSize: table.pageSize,
      };
      ajax('getCapitalFlowDetail', params)
        .then((res: any) => {
          if (res) {
            console.log('res');
            state.tableData = res.crmRecord;
            state.total = res.total;
          } else {
            state.tableData = [];
            state.total = 0;
          }
        })
        .catch((err) => {
          console.log(err);
        });
    };
    // 导出表格
    const handleExports = () => {
      let [startDate, endDate] = dataPickerValue.value
        ? [...dataPickerValue.value]
        : [];
      if (!startDate || !endDate) {
        message.error('请选择时间');
        return;
      }
      const params = {
        ...filterParams,
        startDate,
        endDate,
      };
      for (let key in params) {
        if (
          params[key] === null ||
          params[key] === '' ||
          params[key] === undefined
        ) {
          delete params[key];
        }
      }
      ajax('exportCapitalFlowDetail', params);
    };
    onMounted(init);
    return {
      dataPickerValue,
      onSearch,
      ...toRefs(table),
      ...toRefs(state),
      ...toRefs(filterParams),
      ...PARAMS,
      onTableSubmit: onTableSubmit(fetchTable),
      rowClassName,
      DebitOrCredit,
      dataCateOptions,
      handleExports,
    };
  },
});
</script>

<style lang="less" scoped>
@import './index.less';
</style>
