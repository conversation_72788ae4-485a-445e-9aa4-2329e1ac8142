// 登录用户信息
type UserInfo<T> = {
  userId: T; // 人员ID,未取到值为0
  roleId: T; // 角色ID,未取到值为0
  userName: string; // 姓名,未取到值为""
  groupId: T; // 小组ID,未取到值为0
  type: T; // 管理标志,1为管理(销售组长为1),未取到值为0，
  resourceSet: T[]; // 权限数组
};
// 获取当前用户
interface RoleInfo {
  siId: number;
  siName: string;
  groupId: number;
  siGrade: number;
  gradeName: string;
  roleIdSet: number[]; // 销售角色数组 53总监 56组长 57组员
}
// 分页表格参数
type Params = {
  pageNo?: number;
  pageNum?: number;
  pageSize?: number;
};
type TableState = {
  pageNum?: number;
  pageSize?: number;
  total?: number;
  loading: boolean;
};
type Pagination = {
  pageNum: number;
  pageSize: number;
};
export { Params, TableState, Pagination, UserInfo, RoleInfo };
