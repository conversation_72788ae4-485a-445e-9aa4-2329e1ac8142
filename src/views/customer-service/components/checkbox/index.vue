<template>
  <a-checkbox
    v-show="options.length > 0"
    v-model:checked="checkAll"
    :indeterminate="indeterminate"
    @change="onCheckAllChange"
    :class="`${className}-all-checked`"
  >
    全选
  </a-checkbox>
  <a-checkbox-group
    :class="className"
    v-model:value="checkedList"
    :options="options"
  ></a-checkbox-group>
</template>
<script lang="ts">
import { defineComponent, watch, reactive, toRefs } from 'vue';
import type { PropType } from 'vue';

type Options = {
  value: string;
  label: string;
}[];
export default defineComponent({
  props: {
    className: {
      type: String,
      required: false,
    },
    options: {
      type: Array as PropType<Options>,
      required: true,
    },
  },
  emits: ['changeCheckedList'],
  setup(props, ctx) {
    const state = reactive<{
      indeterminate: boolean;
      checkAll: boolean;
      checkedList: string[];
    }>({
      indeterminate: false,
      checkAll: false,
      checkedList: [],
    });
    watch(
      () => state.checkedList,
      (val) => {
        state.indeterminate = !!val.length && val.length < props.options.length;
        state.checkAll = val.length === props.options.length;
        ctx.emit('changeCheckedList', state.checkedList);
      }
    );
    const onCheckAllChange = (e) => {
      const checkedList = props.options.reduce((pre, cur) => {
        return pre.concat(cur.value);
      }, []);
      state.checkedList = e.target.checked ? checkedList : [];
    };
    return {
      ...toRefs(props),
      ...toRefs(state),
      onCheckAllChange,
    };
  },
});
</script>
<style lang="less" scoped>

</style>
