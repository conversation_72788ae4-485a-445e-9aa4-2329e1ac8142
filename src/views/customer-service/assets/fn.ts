import { SimuTableData } from '../type';
import { FILES } from './const';

/** 私募回访 - 生成相关文件链接 */
export function createFileUrl(
  file: any,
  row: SimuTableData,
  showWarn: boolean
): {
  url: string,
  name: string,
  fileIndex: number
} {
  const { fileType } = file;
  let { url } = FILES[fileType];
  const { suffix, name, fileIndex } = FILES[fileType];
  const suffixParams = {
    user: row.investorName,
    date: file.sysDate,
    id: row.certificateNo,
    protocolNo: file.protocolNo,
    reserveDomain1: file.reserveDomain1,
    riskLevel: file.riskLevel,
    fundCode: row.fundCode,
    showWarn: showWarn ? '1' : '0',
  };
  if (url) {
    suffix.map((item: string, index: number) => {
      url = url.replace(`{suffix${index + 1}}`, suffixParams[item]);
    });
    // console.log(`相关文件 ${name}`, url)
    return { url, name, fileIndex };
  } else {
    return { url: '', name: '', fileIndex: 999 };
  }
}
