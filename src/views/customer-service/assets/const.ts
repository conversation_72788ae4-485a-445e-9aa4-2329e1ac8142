import { FeedbackFilter } from '../type';
import { env } from '@/utils/request';

/** 意见反馈 - 回复状态 */
export enum FEEDBACK_STATUS {
  '待回复',
  '已回复',
}
/** 意见反馈 - 筛选项配置 */
export const FEEDBACK_FILTERS: FeedbackFilter[] = [
  {
    key: 'feedStatus',
    type: 'select',
    placeholder: '请选择反馈状态',
    mode: null,
    selections: [
      {
        value: 0,
        label: '待回复',
      },
      {
        value: 1,
        label: '已回复',
      },
    ],
  },
  {
    key: 'custId',
    type: 'input',
    placeholder: '请输入客户号',
  },
  {
    key: 'feedType',
    type: 'input',
    placeholder: '请输入反馈类型',
  },
  {
    key: 'feedDetail',
    type: 'input',
    placeholder: '请输入意见详情',
  },
  {
    key: 'source',
    type: 'select',
    placeholder: '请选择来源',
    mode: 'multiple',
    selections: [],
  },
  {
    key: 'channel',
    type: 'select',
    placeholder: '请选择渠道',
    mode: 'multiple',
    selections: [],
  },
  {
    key: 'version',
    type: 'select',
    placeholder: '请选择版本',
    mode: 'multiple',
    selections: [],
  },
];
/** 意见反馈 - 表格列配置 */
export const FEEDBACK_TABLE_COLUMNS = [
  {
    dataIndex: 'id',
    key: 'id',
    title: '反馈id',
  },
  {
    dataIndex: 'status',
    key: 'status',
    title: '反馈状态',
    slotName: 'status',
  },
  {
    dataIndex: 'feedback_time',
    key: 'feedback_time',
    title: '反馈时间',
    width: 180,
  },
  {
    dataIndex: 'cust_id',
    key: 'cust_id',
    title: '反馈用户custid',
    slotName: 'userid',
  },
  {
    dataIndex: 'user_id',
    key: 'user_id',
    title: '反馈用户userid',
    slotName: 'userid',
  },
  {
    dataIndex: 'type',
    key: 'type',
    title: '反馈类型',
  },
  {
    dataIndex: 'feedback_detail',
    key: 'feedback_detail',
    title: '详细描述',
    slotName: 'longText',
    width: '300px',
  },
  {
    dataIndex: 'picture_url_list',
    key: 'picture_url_list',
    title: '图片',
    slotName: 'picture_url_list',
    width: '170px',
  },
  {
    dataIndex: 'source',
    key: 'source',
    title: '来源',
    slotName: 'source',
  },
  {
    dataIndex: 'telephone',
    key: 'telephone',
    title: '电话号码',
  },
  // TODO 2022.4.8 新增列
  {
    dataIndex: 'contact_way',
    key: 'contact_way',
    title: '联系方式（选填）',
    width: '180px',
  },
  {
    dataIndex: 'responder',
    key: 'responder',
    title: '回复者',
  },
  {
    dataIndex: 'respond_time',
    key: 'respond_time',
    title: '回复时间',
    width: 180,
  },
  {
    dataIndex: 'respond',
    key: 'respond',
    title: '回复内容',
    slotName: 'longText',
    width: '300px',
  },
  {
    dataIndex: 'system_info',
    key: 'system_info',
    title: '系统信息',
    slotName: 'longText',
    width: '200px',
  },
];

/** 私募回访 - 表格列配置 */
export const SIMU_TABLE_COLUMNS: GridColumn[] = [
  {
    headerName: '',
    field: 'select',
    checkboxSelection: true,
    headerCheckboxSelection: true,
    pinned: 'left',
    width: 80,
  },
  {
    headerName: '序号',
    field: 'index',
  },
  {
    headerName: '时间',
    field: 'timeStr',
  },
  {
    headerName: '产品',
    field: 'fundName',
    wrapText: true,
  },
  {
    headerName: '购买金额',
    field: 'applicationAmount',
  },
  {
    headerName: '客户号',
    field: 'custId',
  },
  {
    headerName: '客户名称',
    field: 'investorName',
  },
  {
    headerName: '证件号码',
    field: 'certificateNo',
  },
  {
    headerName: '地址',
    field: 'address',
    wrapText: true,
  },
  {
    headerName: '手机号码（伪号）',
    headerComponent: 'WrapHeader',
    field: 'forgeMobile',
  },
  {
    headerName: '电子邮件',
    field: 'emailAddress',
  },
  {
    headerName: '回访原因',
    field: 'returnVisitType',
    valueFormatter: (row: any): string => {
      return TYPES[row.value];
    },
  },
  {
    headerName: '相关文件',
    field: 'peFundFileDtos',
    cellRenderer: 'File',
    resizable: true,
    pinned: 'right',
    width: 160
  },
  {
    headerName: '回访结果',
    field: 'returnVisitResult',
    cellRenderer: 'Result',
    resizable: true,
    pinned: 'right',
    width: 146
  },
  {
    headerName: '操作',
    field: 'options',
    cellRenderer: 'Option',
    resizable: true,
    pinned: 'right',
    width: 90,
  },
];
/** 私募回访 - 查询表单 */
export const QUERYKEYS = {
  '0': { key: 'fundName' },
  '1': { key: 'custId' },
  '2': { key: 'investorName' },
  '3': { key: 'returnVisitTypeList' },
  '4': { key: 'returnVisitResultList' },
};
/** 私募回访 - 默认输入框 */
export const PLACEHOLDERS = [
  '请输入产品名称',
  '请输入客户号',
  '请输入客户名称',
];
/** 私募回访 - 回访原因 */
export const TYPES: any = ['购买产品', '撤销购买'];
/** 私募回访 - 回访结果 */
export const RESULTS: any = ['未回访', '购买成功', '购买不成功'];
/** 私募回访 - 相关文件 */
const URL_ENV = ~['dev', 'test'].indexOf(env) ? 'test' : '';
const URL_PREFIX = `//${URL_ENV}fund.10jqka.com.cn/ifundapp_app/public/m/privateFundFile/dist/index.html?`
export const FILES: any = {
  // 新增相关关系说明书
  'INFO.CONFIRM': {},
  'RISK.WARN': {},
  'SIGN.AGREE': {
    url: `${URL_PREFIX}type=agree&user={suffix1}&date={suffix2}&protocolNo={suffix3}&fundCode={suffix4}`,
    suffix: ['user', 'date', 'protocolNo', 'fundCode'],
    name: '电子签名约定书',
    fileIndex: 0,
  },
  'RISK.NOTIFY': {
    url: `${URL_PREFIX}type=notify&user={suffix1}&date={suffix2}&showWarn={suffix3}&id={suffix6}&riskLevel={suffix4}&reserveDomain1={suffix5}&protocolNo={suffix7}&fundCode={suffix8}`,
    suffix: [
      'user',
      'date',
      'showWarn',
      'riskLevel',
      'reserveDomain1',
      'id',
      'protocolNo',
      'fundCode',
    ],
    name: '风险测评结果确认书',
    fileIndex: 1,
  },
  'RISK.REVEAL': {
    url: `${URL_PREFIX}type=reveal&user={suffix1}&date={suffix2}&protocolNo={suffix3}&fundCode={suffix4}`,
    suffix: ['user', 'date', 'protocolNo', 'fundCode'],
    name: '风险揭示书',
    fileIndex: 2,
  },
  'FUND.CONTRACT': {
    url: `${URL_PREFIX}type=contract&user={suffix1}&date={suffix2}&id={suffix3}&protocolNo={suffix4}&reserveDomain1={suffix5}&fundCode={suffix6}`,
    suffix: ['user', 'date', 'id', 'protocolNo', 'reserveDomain1', 'fundCode'],
    name: '基金合同',
    fileIndex: 3,
  },
  'TRADE.REQUEST': {
    url: `${URL_PREFIX}type=apply&user={suffix1}&date={suffix2}&id={suffix3}&protocolNo={suffix4}&reserveDomain1={suffix5}&fundCode={suffix6}`,
    suffix: ['user', 'date', 'id', 'protocolNo', 'reserveDomain1', 'fundCode'],
    name: '交易确认书',
    fileIndex: 4,
  },
  'RELATION.INSTR': {
    url: `${URL_PREFIX}type=relation&user={suffix1}&date={suffix2}&protocolNo={suffix3}&fundCode={suffix4}`,
    suffix: ['user', 'date', 'protocolNo', 'fundCode'],
    name: '关联关系说明书',
    fileIndex: 5,
  },
  'HIGH.RISK.NOTIFY': {
    url: `${URL_PREFIX}type=addition&user={suffix1}&date={suffix2}&protocolNo={suffix3}&fundCode={suffix4}`,
    suffix: ['user', 'date', 'protocolNo', 'fundCode'],
    name: '高风险提示说明书',
    fileIndex: 6,
  },
  'RISK.CONFIRM': {},
  'ASSET.CONTRACT': {},
  'PLAN.SPEC': {},
};
/** 私募回访 - 通知人颜色 */
export const NOTICE_COLORS = ['red', 'orange', 'blue', 'cyan', 'green', 'purple']