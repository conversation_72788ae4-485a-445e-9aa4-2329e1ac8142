<template>
  <div class="m-custom-service-simu">
    <p class="m-page-title">私募回访后台</p>
    <div class="m-simu-table table-wrapper">
      <div class="filter-wrapper">
        <div class="m-filter-inputs">
          <a-input
            v-for="(input, index) in PLACEHOLDERS"
            :key="QUERYKEYS[index].key"
            v-model:value="queryList[index]"
            :placeholder="input"
            type="text"
          />
        </div>
        <div class="m-filter-selects">
          <a-select
            v-model:value="queryList[3]"
            mode="multiple"
            placeholder="请输入回访原因"
          >
            <a-select-option
              v-for="(item, index) in TYPES"
              :key="index"
              :value="index.toString()"
            >
              {{ item }}
            </a-select-option>
          </a-select>
          <a-select
            v-model:value="queryList[4]"
            mode="multiple"
            placeholder="请输入回访结果"
          >
            <a-select-option
              v-for="(item, index) in RESULTS"
              :key="index"
              :value="index.toString()"
            >
              {{ item }}
            </a-select-option>
          </a-select>
        </div>
        <div class="m-filter-btns">
          <a-button type="primary" @click="handleSearch">查询</a-button>
          <a-button :disabled="selectIndex.length === 0" @click="handleExport">
            导出
          </a-button>
        </div>
        <div v-show="noticeEdit" class="m-notice">
          <div class="m-notice-edit">
            <a-input-search
              v-model:value="notice"
              placeholder="新订单vanish通知人"
              @search="handleAddNotice"
            >
              <template #enterButton>
                <a-button><user-add-outlined /></a-button>
              </template>
            </a-input-search>
          </div> 
          <div class="m-notice-list">
            <a-popconfirm
              v-for="(member, index) in noticeList"
              :key="member.id"
              :visible="noticeDel === member.id"
              placement="topRight"
              title="确认删除该联系人吗？"
              cancel-text="取消"
              ok-text="确认"
              @confirm="handleConfirmNotice('confirm', member)"
              @cancel="handleConfirmNotice('cancel')"
              @visible-change="handleConfirmNotice('cancel')"
            >
              <a-tag
                :visible="true"
                closable
                :color="NOTICE_COLORS[index % NOTICE_COLORS.length]"
                @close.stop="handleDelNotice(member)"
              >
                {{ member.value }}
              </a-tag>
            </a-popconfirm>
          </div>
        </div>
      </div>
      <div class="m-table-content">
        <aggrid-creater
          v-model:prop-page-num="pageNum"
          v-model:prop-page-size="pageSize"
          :header-height="40"
          :row-height="186"
          :prop-total-size="total"
          :custom-col="true"
          :prop-column-defs="SIMU_TABLE_COLUMNS"
          :prop-row-data="tableData"
          prop-id="simu-feedback"
          @on-grid-page-change="handleTableSubmit"
          @on-grid-ready="onTableReady"
          @on-grid-selection-changed="handleTableRowSelect"
        ></aggrid-creater>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import {
  computed,
  defineComponent,
  onMounted,
  reactive,
  ref,
  toRefs,
} from 'vue';
// component
import useTable from '@/components/hooks/useTable';
import AggridCreater from '@/components/basic/agGrid-creater.vue';
import { UserAddOutlined } from '@ant-design/icons-vue';
// const
import {
  SIMU_TABLE_COLUMNS,
  PLACEHOLDERS,
  TYPES,
  RESULTS,
  QUERYKEYS,
  NOTICE_COLORS,
} from '../assets/const';
// fn
import ajax from '@/server/http';
import { createFileUrl } from '../assets/fn';
// type
import { SimuTableData, NoticeData } from '../type';
// renders
import cellRenderers from './cellRenderers';
import { fetchSaleInfo } from '@/server/api';
import { message } from 'ant-design-vue';

type iState = {
  /** 筛选条件 */
  queryList: (string | string[])[];
  /** 回访结果值列表 */
  resultList: ('0' | '1' | '2')[];
  /** 回访结果操作列表 */
  optionIndex: number[];
  /** 行选择数组 */
  selectIndex: number[];
  /** 编辑vanish通知人 */
  notice: string;
  /** 通知人列表 */
  noticeList: NoticeData[];
  /** 删除通知人索引 */
  noticeDel: string;
  /** 编辑通知人权限 */
  noticeEdit: boolean;
};

export default defineComponent({
  name: 'SimuFeedback',
  components: {
    AggridCreater,
    UserAddOutlined,
    ...cellRenderers,
  },
  setup(props) {
    let gridApi = null;
    let columnApi = null;
    const { table, onTableChange, onTableSubmit, initTable } =
      useTable<SimuTableData>();
    const state = reactive<iState>({
      queryList: ['', '', '', [], []],
      resultList: [],
      optionIndex: [],
      selectIndex: [],
      notice: '',
      noticeList: [],
      noticeDel: '',
      noticeEdit: false,
    });

    // computed
    const _optionIndex = computed(() => state.optionIndex);
    const _resultList = computed(() => state.resultList);

    // methods
    /** 初始化 */
    const init = () => {
      // fetchAdmin();
      fetchTable();
      fetchNoticeList();
    };
    /** TODO 2022.4.18 获取用户权限 */
    const fetchAdmin = () => {
      fetchSaleInfo(null).then((res: any) => {
        if (res.role === 53 || res.role === 56) {
          state.noticeEdit = true;
        }
      });
    };
    /** TODO 2022.4.18 获取通知人列表 */
    const fetchNoticeList = () => {};
    /** TODO 2022.4.18 新增通知人 */
    const addNotice = () => {};
    /** TODO 2022.4.18 删除通知人 */
    const deleteNotice = (member: NoticeData) => {};
    /** 查询表格 */
    const fetchTable = () => {
      // 清空操作列表
      state.optionIndex = [];
      // 整理发送数据
      let dataToSend: {
        [x: string]: string | string[];
      } = {};
      state.queryList.forEach((query: string | string[], index) => {
        switch (index) {
          case 0:
          case 1:
          case 2: {
            dataToSend[QUERYKEYS[index].key] = query as string;
            break;
          }
          case 3:
          case 4: {
            if (!query || query.length == 0) break;
            dataToSend[QUERYKEYS[index].key] = query;
            break;
          }
        }
      });
      // 请求表格
      ajax('getSimuTable', dataToSend, {
        pageNum: table.pageNum,
        pageSize: table.pageSize,
      }).then((res: any) => {
        if (res) {
          table.total = res.total;
          // table.tableData = mockTable;
          table.tableData = createDataSource(res.list as SimuTableData[]);
          state.resultList = table.tableData.map(
            (row) => row.returnVisitResult
          );
          if (gridApi) {
            setTimeout(calcRowHeight);
          }
          setTimeout(() => {
            columnApi.autoSizeAllColumns();
          });
        } else {
          initTable();
        }
      });
    };
    /** 更新回访结果 */
    const postTableUpdate = (seq: number, result: '0' | '1' | '2') => {
      const data = {
        seq,
        returnVisitResult: result,
      };
      ajax('updateSimuTable', data).then(() => {
        message.success('更新成功！');
        onTableChange(fetchTable);
      });
    };
    /** 导出表格 */
    const exportTable = () => {
      const data = {
        seq: state.selectIndex,
        prodType: '1',
      };
      ajax('getSimuFile', data);
    };
    /** 生成 tableData */
    const createDataSource = (tableData: SimuTableData[]) => {
      return tableData.map((item, index: number) => {
        let _item: SimuTableData = { ...item };
        _item.key = item.seq.toString();
        _item.index = index + 1;

        // 添加相关文件链接、名称
        let files = _item?.peFundFileDtos || [];
        let showWarn = false;
        for (let i = 0, len = files?.length; i < len; i++) {
          if (files[i].fileType === 'RISK.WARN') {
            showWarn = true;
            break;
          }
        }
        files = files
          .map((file: any) => {
            let fileData = createFileUrl(file, item, showWarn);
            file.fileUrl = fileData.url;
            file.fileName = fileData.name;
            file.fileIndex = fileData.fileIndex;
            return file;
          })
          .sort((a, b) => a.fileIndex - b.fileIndex);
        _item.peFundFileDtos = files;
        
        return _item;
      });
    };
    /** 表格挂载完成回调 */
    const onTableReady = (_gridApi: any, _columnApi: any) => {
      gridApi = _gridApi;
      columnApi = _columnApi;
      // window['gridApi'] = gridApi;
      // window['columnApi'] = columnApi;
      setTimeout(() => {
        columnApi.autoSizeAllColumns();
      })
      calcRowHeight();
    };
    /** 计算各行高度 */
    const calcRowHeight = () => {
      table.tableData.forEach((row, index) => {
        const rowNode = gridApi.getRowNode(index);
        const files = row.peFundFileDtos.filter(file => file.fileUrl);
        rowNode.setRowHeight(32 + (files.length || 1) * 31);
        if (index === table.tableData.length - 1) {
          gridApi.onRowHeightChanged();
        }
      });
    };

    // controllers
    /** 点击新增通知人 */
    const handleAddNotice = () => {
      console.log('新增通知人', state.notice);
      addNotice();
    };
    /** 点击删除通知人 */
    const handleDelNotice = (member: NoticeData) => {
      console.log('删除通知人', member);
      state.noticeDel = member.id.toString();
    };
    /** 确认/取消删除通知人 */
    const handleConfirmNotice = (
      type: 'confirm' | 'cancel',
      member?: NoticeData
    ) => {
      if (type === 'confirm') {
        // 确认删除
        console.log('确认删除通知人', member);
        deleteNotice(member);
      }
      state.noticeDel = '';
    };
    /** 表格查询控制器 */
    const handleSearch = () => {
      console.log('search filter', state.queryList);
      onTableChange(fetchTable);
    };
    /** 修改回访结果 */
    const handleResultSelect = (value: '0' | '1' | '2', rowIndex: number) => {
      console.log('修改回访结果', value, rowIndex);
      const rowData = table.tableData[rowIndex];
      // 修改表格数据
      // rowData.returnVisitResult = value;
      state.resultList[rowIndex] = value;
      // 将该记录seq加入待确认操作列表
      if (!~state.optionIndex.indexOf(rowData.seq)) {
        state.optionIndex = [...state.optionIndex, rowData.seq];
      }
    };
    /** 确认修改回访结果 */
    const handleConfirm = (index: number) => {
      let row: SimuTableData = { ...table.tableData[index] };
      // 删除修改选项的索引
      const _optionIndex = [...state.optionIndex];
      _optionIndex.splice(_optionIndex.indexOf(row.seq), 1);
      state.optionIndex = _optionIndex;
      // 更新数据
      postTableUpdate(row.seq, state.resultList[index]);
      console.log('确认修改回访结果', index, row.seq, state.resultList[index]);
    };
    /** 选中表格行 */
    const handleTableRowSelect = ({ data }: { data: SimuTableData[] }) => {
      state.selectIndex = data.map((row) => row.seq);
    };
    /** 导出选中表格 */
    const handleExport = () => {
      console.log('导出');
      exportTable();
    };

    // 传入参数
    SIMU_TABLE_COLUMNS.forEach((col) => {
      col.colId = col.field;
      col.cellRendererParams = {
        optionIndex: _optionIndex,
        resultList: _resultList,
        handleResultSelect,
        handleConfirm,
      };
    });

    onMounted(init);
    return {
      ...toRefs(table),
      ...toRefs(state),
      SIMU_TABLE_COLUMNS,
      PLACEHOLDERS,
      TYPES,
      RESULTS,
      QUERYKEYS,
      NOTICE_COLORS,
      onTableReady,
      handleAddNotice,
      handleDelNotice,
      handleConfirmNotice,
      handleSearch,
      handleTableSubmit: onTableSubmit(fetchTable),
      handleTableRowSelect,
      handleExport,
    };
  },
});
</script>

<style lang="less">
@import './index.less';
</style>
