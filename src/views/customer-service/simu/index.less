.m-custom-service-simu {
  .m-simu-table {
    .filter-wrapper {
      flex-wrap: wrap;
      .m-filter- {
        &inputs,
        &selects,
        &btns {
          // margin-bottom: 20px;
          > * {
            margin-right: 20px;
          }
        }
      }
      .m-filter-inputs {
        input {
          width: 160px;
        }
      }
      .m-filter-selects {
        .ant-select {
          width: 200px;
        }
      }
      .m-notice {
        width: 100%;
        display: flex;
        align-items: flex-start;
        input {
          width: 160px;
        }
        .m-notice-edit {
          margin-right: 20px;
          display: flex;
          align-items: center;
          > * {
            margin-right: 12px;
          }
        }
        .m-notice-list {
          max-height: 120px;
          display: flex;
          flex-flow: column wrap;
          > * {
            margin-bottom: 8px;
          }
        }
      }
    }
    .m-table-content #simu-feedback {
      .ag-unselectable {
        user-select: unset;
      }
      .ag-cell-normal-height {
        display: flex;
        align-items: center;
      }
      .m-wrap-header {
        display: flex;
        flex-flow: column;
      }
      .m-option {
        padding: 12px 0;
      }
      .m-files {
        padding: 16px 0;
        display: flex;
        flex-flow: column nowrap;
        > a {
          margin: 2px 0;
        }
      }
    }
  }
}