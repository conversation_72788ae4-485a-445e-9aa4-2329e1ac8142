import { createRenderers } from '@/utils/agGrid-service';
import { RESULTS } from '../assets/const';
import { sliceString } from '@/utils/fn';

const fns = {
  Option: ({ params }) => {
    const { optionIndex, rowIndex, handleConfirm } = params;
    return (
      <div class="m-option">
        <a-popconfirm
          placement="topRight"
          disabled={!~optionIndex.indexOf(params.data.seq)}
          title="确认修改该条记录吗？"
          cancelText="取消"
          okText="确认"
          onConfirm={() => handleConfirm(rowIndex)}
        >
          <a-button
            danger
            type="primary"
            disabled={!~optionIndex.indexOf(params.data.seq)}
          >
            确认
          </a-button>
        </a-popconfirm>
      </div>
    );
  },
  Result: ({ params }) => {
    const { resultList, rowIndex, handleResultSelect, data } = params;
    return (
      <a-select
        value={resultList[rowIndex]}
        onChange={(select: string) => handleResultSelect(select, rowIndex)}
      >
        {RESULTS.map((item: string, index: number) => {
          return (
            <a-select-option key={index} value={index.toString()}>
              {item}
            </a-select-option>
          );
        })}
      </a-select>
    );
  },
  File: ({ params }) => {
    const { data } = params;
    const _data = JSON.parse(JSON.stringify(data));
    return (
      <div class="m-files">
        {_data.peFundFileDtos.map((file: any, index: number) => {
          return file.fileUrl ? (
            <a key={index} href={file.fileUrl} target="_blank">
              {file.fileName}
            </a>
          ) : null;
        })}
      </div>
    );
  },
  WrapHeader: ({params}) => {
    const arr = sliceString(params.displayName, '（');
    return <div class="m-wrap-header">
      <span>{arr[0]}</span>
      <span>{arr[1]}</span>
    </div>
  }
};

export default createRenderers(fns);
