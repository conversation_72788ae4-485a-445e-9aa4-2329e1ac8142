<template>
  <article class="m-service-feedback">
    <p class="m-page-title">意见反馈列表</p>
    <div class="m-feedback-table table-wrapper">
      <div class="filter-wrapper">
        <div class="m-feedback-filters">
          <div>
            <a-range-picker v-model:value="date" show-time />
          </div>
          <template v-for="item in filters" :key="item.key">
            <div>
              <a-select
                v-if="item.type === 'select'"
                v-model:value="selects[item.key]"
                :mode="item.mode"
                :placeholder="item.placeholder"
                allow-clear
              >
                <a-select-option
                  v-for="option in item.selections"
                  :key="option.value"
                  :value="option.value"
                >
                  {{ option.label }}
                </a-select-option>
              </a-select>
              <a-input
                v-else
                v-model:value="inputs[item.key]"
                :placeholder="item.placeholder"
              />
            </div>
          </template>
          <div>
            <a-button type="primary" @click="handleSearch">搜索</a-button>
          </div>
        </div>
      </div>
      <div class="m-table-content">
        <custom-table
          v-model:page-num="pageNum"
          v-model:page-size="pageSize"
          :table-total="total"
          :table-data="tableData"
          :scroll="{ x: 2600, y: 600 }"
          :columns="FEEDBACK_TABLE_COLUMNS"
          :page-size-list="['5', '10', '20', '50']"
          :row-class-name="rowClassName()"
          :on-submit="onTableSubmit"
        >
          <!-- 反馈用户id -->
          <template #userid="{ text }">
            {{ text || '未登录用户' }}
          </template>
          <!-- 反馈类型 -->
          <template #status="{ text, record }">
            <a-button
              type="primary"
              size="small"
              :disabled="text != 0"
              @click="handleReplyShow(record)"
            >
              {{ FEEDBACK_STATUS[text] }}
            </a-button>
          </template>
          <!-- 图片 -->
          <template #picture_url_list="{ text }">
            <div v-show="!!text">
              <img-view
                v-for="(img, index) in text"
                :key="index"
                :src="'data:image/png;base64,' + img"
              />
            </div>
          </template>
          <!-- 来源 -->
          <template #source="{ record }">
            <span>{{ record.source }}（{{ record.source_name }}）</span>
          </template>
          <!-- 长文本 -->
          <template #longText="{ text }">
            <a-textarea
              :value="text"
              :disabled="true"
              :auto-size="{ minRows: 2, maxRows: 4 }"
              @scroll.stop="() => {}"
            />
          </template>
        </custom-table>
      </div>
    </div>
    <div class="m-feedback-modal">
      <a-modal
        v-model:visible="replyModalVisible"
        title="回复用户"
        ok-text="确认回复"
        :confirm-loading="loading"
        @ok="handleConfirmReply"
      >
        <div class="m-feedback-reply">
          <a-textarea
            v-model:value="reply"
            :auto-size="{ minRows: 8 }"
            :maxlength="200"
            show-count
          ></a-textarea>
        </div>
      </a-modal>
    </div>
  </article>
</template>

<script lang="ts">
import { defineComponent, onMounted, reactive, toRefs, watch } from 'vue';
import dayjs, { Dayjs } from 'dayjs';
dayjs.locale('zh-cn');
import { getMonthRange } from '@/utils/fn';
import ajax from '@/server/http';
// param
import {
  FEEDBACK_STATUS,
  FEEDBACK_FILTERS,
  FEEDBACK_TABLE_COLUMNS,
} from '../assets/const';
// type
import { FeedbackFilter, FeedbackTableData } from '../type';
// components
import CustomTable, {
  useCustomTable,
} from '@/components/common/CustomTable.vue';
import ImgView from '@/components/common/ImgView.vue';
import { message } from 'ant-design-vue';

type iState = {
  // 筛选项
  date: Dayjs[];
  filters: FeedbackFilter[];
  selects: {
    feedStatus: number;
    source: string[] | number[];
    channel: string[] | number[];
    version: string[] | number[];
  };
  inputs: {
    custId: string;
    feedType: string;
    feedDetail: string;
  };
  // table
  tableData: FeedbackTableData[];
  total: number;
  // 回复弹窗
  replyModalVisible: boolean;
  replyModalId: number;
  reply: string;
  loading: boolean;
};

export default defineComponent({
  name: 'Feedback',
  components: {
    CustomTable,
    ImgView,
  },
  setup(props) {
    const { table, onTableChange, onTableSubmit, rowClassName } =
      useCustomTable();
    const state = reactive<iState>({
      date: getMonthRange() as Dayjs[],
      filters: FEEDBACK_FILTERS,
      selects: {
        feedStatus: null,
        source: [],
        channel: [],
        version: [],
      },
      inputs: {
        custId: '',
        feedType: '',
        feedDetail: '',
      },
      // table
      tableData: [],
      total: 0,
      // 回复弹窗
      replyModalVisible: false,
      replyModalId: -1,
      reply: '',
      loading: false,
    });
    // watch
    watch(
      () => state.replyModalVisible,
      (visible) => {
        if (!visible) {
          state.reply = '';
          state.replyModalId = -1;
        }
      }
    );
    // methods
    /** 初始化 */
    const init = () => {
      fetchTags();
      fetchTable();
    };
    /** 查询筛选项列表 */
    const fetchTags = () => {
      ajax('getFeedbackTags').then((res: any) => {
        console.log('fetchTags', res);
        const tagKeys = ['source', 'channel', 'version'];
        state.filters = state.filters.map((item) => {
          if (~tagKeys.indexOf(item.key)) {
            item.selections = res[`${item.key}_list`].map((value) => ({
              value,
              label: value,
            }));
            return item;
          } else return item;
        });
      });
    };
    /** 查询表格 */
    const fetchTable = () => {
      const data = {
        begin_time: state.date[0].format('YYYY-MM-DD HH:mm:ss'),
        end_time: state.date[1].format('YYYY-MM-DD HH:mm:ss'),
        status: state.selects.feedStatus,
        cust_id: state.inputs.custId,
        feedback_type: state.inputs.feedType,
        feedback_detail: state.inputs.feedDetail,
        source_list: state.selects.source,
        channel_list: state.selects.channel,
        version_list: state.selects.version,
      };
      ajax('getFeedbackTableData', data, {
        pageNum: table.pageNum,
        pageSize: table.pageSize,
      }).then((res: any) => {
        console.log('fetchTable', res);
        state.total = res.total;
        state.tableData = res.crmRecord;
      });
    };
    /** 回复用户 */
    const postReply = () => {
      return ajax('replyFeedback', {
        id: state.replyModalId,
        reply: state.reply,
      })
        .then((res: any) => {
          if (res) {
            message.success('回复成功');
            onTableChange(fetchTable);
            return true;
          }
        })
        .catch((e) => {
          message.error('回复失败');
          console.error(e);
          return false;
        });
    };
    // controller
    /**
     * 打开回复弹窗
     * @param record
     */
    const handleReplyShow = (record: FeedbackTableData) => {
      console.log('回复弹窗', record);
      state.replyModalVisible = true;
      state.replyModalId = record.id;
    };
    /** 确认回复 */
    const handleConfirmReply = () => {
      console.log('确认回复');
      if (!state.reply) {
        return message.error('回复不能为空！');
      }
      state.loading = true;
      postReply().then(() => {
        state.replyModalVisible = false;
        state.loading = false;
      });
    };
    /** 搜索 */
    const handleSearch = () => {
      onTableChange(fetchTable);
    };

    onMounted(init);
    return {
      ...toRefs(table),
      ...toRefs(state),
      FEEDBACK_STATUS,
      FEEDBACK_TABLE_COLUMNS,
      handleReplyShow,
      handleConfirmReply,
      handleSearch,
      onTableSubmit: onTableSubmit(fetchTable),
      rowClassName,
    };
  },
});
</script>

<style lang="less">
.m-service-feedback {
  padding: 10px;
  .table-wrapper {
    padding: 0;
    .filter-wrapper {
      .m-feedback-filters {
        width: 1400px;
        display: flex;
        align-items: center;
        flex-flow: row wrap;
        > div {
          margin-right: 16px;
        }
        > div:nth-child(n + 6) {
          margin-top: 12px;
        }
        .ant-picker {
          width: 360px;
          input {
            font-size: 14px;
          }
        }
        .ant-select {
          min-width: 240px;
        }
      }
    }
    .m-table-content {
      .cell-picture_url_list {
        > div {
          display: flex;
          flex-flow: row wrap;
        }
        .img-view {
          margin: 4px;
        }
        img {
          width: 60px;
        }
      }
    }
  }
}
</style>
