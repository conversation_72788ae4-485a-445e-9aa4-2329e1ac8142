// 客服 - 意见反馈
/** 意见反馈 - 筛选项配置 */
export type FeedbackFilter = {
  key: string;
  type: 'select' | 'input';
  placeholder: string; // 默认文字
  mode?: string;
  selections?: {
    value: string | number;
    label: string;
  }[];
};
/** 意见反馈 - 表格数据 */
export type FeedbackTableData = {
  id: number; // 反馈id
  status: number; // 反馈状态
  feedback_time: string; // 反馈时间
  cust_id: string; // 反馈用户custid
  user_id: string; // 反馈用户userid
  type: string; // 反馈类型
  feedback_detail: string; // 详细描述
  picture_url_list: string[]; // 图片
  source: string; // 来源
  source_name: string; // 来源名字
  telephone: string; // 电话号码
  responder: string; // 回复者
  respond_time: string; // 回复时间
  respond: string; // 回复内容
  system_info: string; // 系统信息
};

/** 私募回访 - 表格数据 */
export type SimuTableData = {
  key?: any; // key值
  index?: number; // 序号
  seq?: any;
  timeStr?: string; // 时间
  fundCode?: string; // 基金代码
  fundName?: string; // 产品名称
  applicationAmount?: number; // 购买金额
  custId?: string; // 客户号
  investorName?: string; // 客户名称
  certificateNo?: string; // 证件号码
  protocolNo?: string; // 协议号
  address?: string; // 地址
  forgeMobile?: string; // 手机伪号
  emailAddress?: string; // 电子邮件
  returnVisitTypeStr?: string; // 回访原因（中文）
  returnVisitResultStr?: string; // 回访结果（中文）
  returnVisitType?: '0' | '1'; // 回访原因（字符）0 购买产品; 1 撤销购买
  returnVisitResult?: '0' | '1' | '2'; // 回访结果（字符）0 未回访; 1 购买成功; 2 购买不成功
  peFundFileDtos?: any[]; // 文件类型
};
/** 私募回访 - post数据 */
export interface PostParams {
  pageSize: number;
  pageNum: number;
  forgeMobile?: string; // 手机号
  custId?: string; // 客户号
  investorName?: string; // 客户名称
  returnVisitType?: string; // 回访原因
  returnVisitResult?: string; // 回访结果
}
/** 私募回访 - 通知人数据 */
export type NoticeData = {
  id: string;
  value: string;
}