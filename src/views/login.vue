<template>
  <div class="login-container">
    <a-form
      ref="formRef"
      :model="loginForm"
      class="login-form"
      auto-complete="on"
      label-position="left"
    >
      <div class="title-container">
        <img src="@/assets/images/logo.png" alt="" class="logo" />
        <h3 class="title">同花顺-CRM系统</h3>
      </div>
      <div class="welcome">
        <span class="fs24">欢迎登陆</span>
        <span style="margin: 0 7px">|</span>
        <span class="fs18 grey-login">welcome to Log in</span>
      </div>
      <a-form-item class="form-item" name="password">
        <a-input
          ref="password"
          v-model:value="loginForm.password"
          placeholder="请输入密码"
          name="password"
          type="password"
          size="large"
          tabindex="2"
          auto-complete="on"
        >
          <template #prefix>
            <img class="svg-container" src="@/assets/images/code.png" />
          </template>
        </a-input>
      </a-form-item>
      <a-button
        class="login_btn"
        size="large"
        type="primary"
        style="width: 100%; margin-bottom: 30px"
        @click="handleLogin"
      >
        登 录
      </a-button>
    </a-form>
  </div>
</template>

<script setup lang="ts">
import md5 from 'blueimp-md5';
import { ref, reactive } from 'vue';
import { message } from 'ant-design-vue';
import { useRouter } from 'vue-router';
import ajax from '@/server/http';

const formRef = ref();
const loginForm = reactive({
  password: '888888',
});
const router = useRouter();
const handleLogin = () => {
  formRef.value.validate().then(async (res) => {
    message.success('登陆成功');
    ajax('login', {}, { password: loginForm.password });
    // router.push('/index');
  });
};
</script>

<style lang="scss" scoped>
$bg: #2d3a4b;
$dark_gray: #889aa4;
$light_gray: #eee;

.login-container {
  min-height: 100%;
  width: 100%;
  background: url('~@/assets/images/home_bg01.png');
  background-size: 100% 100%;
  background-color: $bg;
  overflow: hidden;
  .login_btn {
    margin-top: 100px;
  }

  .login-form {
    transform: scale(0.9);
    position: absolute;
    background: #fff;
    width: 400px;
    height: 600px;
    border-radius: 10px;
    padding: 0 50px;
    left: 60%;
    top: 0;
    bottom: 0;
    // padding: 160px 35px 0;
    margin: auto 0;
    overflow: hidden;
  }

  .tips {
    font-size: 14px;
    color: #fff;
    margin-bottom: 10px;

    span {
      &:first-of-type {
        margin-right: 16px;
      }
    }
  }

  .svg-container {
    width: 23px;
    height: 23px;
  }
  .welcome {
    display: flex;
    align-items: center;
    font-size: 16px;
    margin-top: 30px;
  }
  .form-item {
    margin-top: 20px;
  }
  .title-container {
    margin-top: 60px;
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;

    .logo {
      width: 240px;
      height: 80px;
    }
    .title {
      font-size: 32px;
      color: #242733;
      margin-top: 20px;
      text-align: center;
      font-weight: bold;
    }
  }

  .show-pwd {
    position: absolute;
    right: 10px;
    top: 7px;
    font-size: 16px;
    color: $dark_gray;
    cursor: pointer;
    user-select: none;
  }
}
</style>
