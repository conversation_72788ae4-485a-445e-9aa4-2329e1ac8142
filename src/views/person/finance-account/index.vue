<template>
  <div class="m-finance-account">
    <p class="m-page-title">财务核算</p>
    <div class="m-account-options">
      <a-date-picker v-model:value="date" />
      <a-button type="primary" @click="exportExcel">导出excel</a-button>
    </div>
    <div class="m-account-table table-wrapper">
      <custom-table
        v-model:page-num="pageNum"
        v-model:page-size="pageSize"
        :table-total="total"
        :table-data="tableData"
        :columns="COLUMNS_ACCOUNT"
        :scroll="{ x: 3200, y: 600 }"
        :page-size-list="['5', '10', '20', '50']"
        :row-class-name="rowClassName()"
        :on-submit="onTableSubmit"
      >
        <!-- 头部插槽 -->
        <!-- 说明头 -->
        <template #head_desc="{ column }">
          <div class="m-double-line desc">
            <span>{{ sliceString(column.title, '（')[0] }}</span>
            <span>{{ sliceString(column.title, '（')[1] }}</span>
          </div>
        </template>
        <!-- 总毛利说明头 -->
        <template #head_reward="{ column }">
          <div class="m-double-line">
            <span>{{ sliceString(column.title, '/')[0] }}</span>
            <span>{{ sliceString(column.title, '/')[1] }}</span>
          </div>
        </template>
        <!-- 单元格插槽 -->
        <!-- 产品名称 -->
        <template #fundName="{ record }">
          <div class="m-double-line">
            <span>{{ record.fundCode_test7 }}</span>
            <span>{{ record.fundName_test7 }}</span>
          </div>
        </template>
        <!-- 费率 -->
        <template #rate="{ text }">
          <span>{{Number(text || '').toFixed(2)}}%</span>
        </template>
      </custom-table>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, watch } from 'vue';
import dayjs, { Dayjs } from 'dayjs';
dayjs.locale('zh-cn');
// const
import PARAMS from '../assets/const';
// components
import CustomTable, {
  useCustomTable,
} from '@/components/common/CustomTable.vue';
// type
import { AccountTableData } from '../type';
import { getMonthRange } from '@/utils/fn';
// fn
import { sliceString } from '@/utils/fn';

const mockPositionTableData: AccountTableData[] = [
  {
    id_test7: '1',
    // 客户&销售信息
    sale_test7: '黎雾森',
    custName_test7: '李必龙',
    custId_test7: '************',
    buyDate_test7: '2022-01-04',
    // 产品销售详情
    fundCode_test7: 'C06063',
    fundName_test7: '中航证券鑫航睿享10号',
    minPeriod_test7: '6个月',
    buyAmount_test7: '400000',
    haveAmount_test7: '400000',
    currencyTransform_test7: '400000',
    newBankIn_test7: '0',
    // 收入
    buyFee_test7: '0',
    manageFee_test7: '0',
    operatFee_test7: '0',
    serviceFee_test7: '0.3',
    companyIncome_test7: '600',
    // 支出
    chargeFee_test7: '40',
    currencyManageFeeRate_test7: '0.01',
    currencyManageFee_test7: '40',
    payCost_test7: '0',
    // 收入支出汇总
    totalIncome_test7: '80',
    pureProfit_test7: '520',
    saleTake_test7: '0.05',
    reward_test7: '200',
    saleReward_test7: '38.46',
    buyWay_test7: '银行大额转账',
  },
];
for (let i = 0; i < 10; i++)
  mockPositionTableData.push(mockPositionTableData[0]);

type iState = {
  // filter
  date: Dayjs;
  // table
  total: number;
  tableData: AccountTableData[];
};

export default defineComponent({
  name: 'PositionInfo',
  components: {
    CustomTable,
  },
  setup() {
    const { PAGE_SIZE, PAGE_SIZE_LIST, COLUMNS_ACCOUNT } = PARAMS;
    const { table, onTableChange, onTableSubmit, rowClassName } =
      useCustomTable(PAGE_SIZE);
    const state = reactive<iState>({
      // filter
      date: getMonthRange()[0] as Dayjs,
      // table
      total: 0,
      tableData: mockPositionTableData,
    });
    // watch
    // TODO 2022.3.8 监听日期变化
    watch(
      () => state.date,
      () => {
        console.log('日期改变', state.date.format('YYYY-MM-DD'));
        onTableChange(fetchTable);
      }
    )
    // methods
    /** 查询表格 */
    const fetchTable = () => {};

    // controller
    /** TODO 2022.3.8 导出excel */
    const exportExcel = () => {

    }

    return {
      PAGE_SIZE_LIST,
      COLUMNS_ACCOUNT,
      ...toRefs(table),
      ...toRefs(state),
      sliceString,
      rowClassName,
      onTableSubmit: onTableSubmit(fetchTable),
      exportExcel,
    };
  },
});
</script>

<style lang="less">
.m-finance-account {
  padding: 10px;
  .m-account-options {
    > * {
      margin-right: 20px;
    }
  }
  .m-account-table {
    padding: 0;
    margin-top: 20px;
    .m-double-line {
      display: flex;
      flex-flow: column wrap;
      align-items: center;
      text-align: center;
      &.desc {
        > span:nth-child(2) {
          color: #999;
        }
      }
    }
  }
}
</style>
