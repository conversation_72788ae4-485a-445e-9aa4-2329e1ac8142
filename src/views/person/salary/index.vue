<template>
  <div class="person-salary">
    <div class="salary-options filter-container">
      <a-date-picker v-model:value="date" picker="month" />
      <a-button type="primary" @click="handleExportExcel">导出excel</a-button>
    </div>
    <div class="salary-table">
      <custom-table
        v-model:page-num="pageNum"
        v-model:page-size="pageSize"
        :table-total="total"
        :table-data="tableData"
        :columns="COLUMNS_SALARY"
        :scroll="{ x: 2400 }"
        :default-page-size="PAGE_SIZE"
        :page-size-list="PAGE_SIZE_LIST"
        :row-class-name="rowClassName()"
        :on-submit="onTableSubmit"
      >
        <!-- 百分数 -->
        <template #rate="{ text }">
          <div>{{ text || '--' }}%</div>
        </template>
        <!-- 销售详情 -->
        <template #detail="{ record }">
          <a-button type="link" @click="handleToDetail(record)">
            查看详情
          </a-button>
        </template>
      </custom-table>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, onMounted, reactive, toRefs, watch } from 'vue';
import ajax from '@/server/http';
import { useRouter } from 'vue-router';
import CustomTable, {
  useCustomTable,
} from '@/components/common/CustomTable.vue';
import PARAMS from '../assets/const';
import { SalaryTableData } from '../type';
import dayjs from 'dayjs';
import { jumpToSaleDetail } from '@/utils/common';
dayjs.locale('zh-cn');
window['dayjs'] = dayjs;

type iState = {
  date: any; // 日期
  total: number; // 表格记录总数
  tableData: SalaryTableData[]; // 表格数据
};

export default defineComponent({
  name: 'PersonSalary',
  components: {
    CustomTable,
  },
  setup() {
    const router = useRouter();
    const state = reactive<iState>({
      date: dayjs(new Date().toJSON(), 'YYYY-MM'),
      total: 0,
      tableData: [],
    });
    const { table, onTableChange, onTableSubmit, rowClassName } =
      useCustomTable(PARAMS.PAGE_SIZE);
    /** watch */
    watch(
      () => state.date,
      () => {
        onTableChange(fetchTable);
      }
    );
    /** methods */
    // 初始化
    const init = () => {
      fetchTable();
    };
    // 查询表格
    const fetchTable = () => {
      ajax(
        'person_salary_table',
        {
          month: state.date.format('YYYY-MM'),
        },
        {
          pageNum: table.pageNum,
          pageSize: table.pageSize,
        }
      )
        .then((res) => {
          if (res) {
            console.log('fetchTable', res);
            state.total = res.total;
            state.tableData = res.crmRecord;
          } else {
            state.total = 0;
            state.tableData = [];
          }
        })
        .catch(() => {
          state.total = 0;
          state.tableData = [];
        });
    };
    // 获取excel表格
    const fetchExcel = () => {
      ajax('person_salary_excel', { month: state.date.format('YYYY-MM') });
    };
    // controller
    // 导出excel
    const handleExportExcel = () => {
      console.log('导出excel');
      fetchExcel();
    };
    // 跳转销售详情
    const handleToDetail = (record: SalaryTableData) => {
      jumpToSaleDetail(record.siId);
    };

    onMounted(init);
    return {
      ...toRefs(table),
      ...toRefs(state),
      ...PARAMS,
      rowClassName,
      onTableSubmit: onTableSubmit(fetchTable),
      handleExportExcel,
      handleToDetail,
    };
  },
});
</script>

<style lang="less">
@import './index.less';
</style>
