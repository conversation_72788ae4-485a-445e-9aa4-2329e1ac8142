import { Columns } from 'types/custom';
const PAGE_SIZE = 20;
const PAGE_SIZE_LIST = ['10', '20', '50', '100'];
// 人事相关 - 工资核算
const COLUMNS_SALARY = [
  {
    dataIndex: 'orderNo',
    key: 'orderNo',
    title: '序号',
    width: 80,
  },
  {
    dataIndex: 'giName',
    key: 'giName',
    title: '组别',
    width: 100,
  },
  {
    dataIndex: 'siName',
    key: 'siName',
    title: '姓名',
    width: 120,
  },
  {
    dataIndex: 'siGh',
    key: 'siGh',
    title: '工号',
    width: 80,
  },
  {
    dataIndex: 'grade',
    key: 'grade',
    title: '员工级别',
    width: 120,
  },
  {
    dataIndex: 'jobStartTime',
    key: 'jobStartTime',
    title: '入职时间',
    width: 180,
  },
  {
    dataIndex: 'fullSale',
    key: 'fullSale',
    title: '是否转正',
    width: 90,
  },
  {
    dataIndex: 'jobEndTime',
    key: 'jobEndTime',
    title: '离职时间',
    width: 180,
  },
  {
    dataIndex: 'year_salary',
    key: 'year_salary',
    title: '年限',
    width: 80,
  },
  {
    dataIndex: 'min_teat_salary',
    key: 'min_teat_salary',
    title: '底薪（元）',
    width: 120,
  },
  {
    dataIndex: 'year_salary_salary',
    key: 'year_salary_salary',
    title: '年限工资（元）',
    width: 150,
  },
  {
    dataIndex: 'subsidy_salary',
    key: 'subsidy_salary',
    title: '岗位补贴（元）',
    width: 150,
  },
  // 2022.5.6 万元户
  {
    dataIndex: 'tenThousandNum',
    key: 'tenThousandNum',
    title: '本月达成万元户门槛的用户数',
    width: 240,
  },
  {
    dataIndex: 'tenThousandBonus',
    key: 'tenThousandBonus',
    title: '万元户用户奖金（元）',
    width: 200,
  },
  {
    dataIndex: 'rule_ratio_salary',
    key: 'rule_ratio_salary',
    title: '合规系数',
    width: 120,
  },
  {
    dataIndex: 'rule_cut_salary',
    key: 'rule_cut_salary',
    title: '合规扣款（元）',
    width: 150,
  },
  {
    dataIndex: 'salesAmount',
    key: 'salesAmount',
    title: '基金销售额（元）',
    width: 150,
  },
  {
    dataIndex: 'validManagement',
    key: 'validManagement',
    title: '有效管户数',
    width: 120,
  },
  {
    dataIndex: 'teamCompletionRate',
    key: 'teamCompletionRate',
    title: '组完成率',
    width: 100,
    slotName: 'rate',
  },
  {
    dataIndex: 'bonusCoefficient',
    key: 'bonusCoefficient',
    title: '奖金系数',
    width: 100,
  },
  {
    dataIndex: 'bonus',
    key: 'bonus',
    title: '奖金（元）',
    width: 120,
  },
  {
    dataIndex: 'none_day_salary',
    key: 'none_day_salary',
    title: '无薪天数',
    width: 100,
  },
  {
    dataIndex: 'sick_day_salary',
    key: 'sick_day_salary',
    title: '病假天数',
    width: 100,
  },
  {
    dataIndex: 'absence_day_salary',
    key: 'absence_day_salary',
    title: '缺勤天数',
    width: 100,
  },
  {
    dataIndex: 'absence_cut_salary',
    key: 'absence_cut_salary',
    title: '缺勤扣款',
    width: 120,
  },
  {
    dataIndex: 'total_salary',
    key: 'total_salary',
    title: '合计',
    width: 150,
  },
  {
    dataIndex: 'detail',
    key: 'detail',
    title: '销售详情',
    width: 90,
    slotName: 'detail',
    fixed: 'right',
  },
];
// 人事相关 - 岗位信息设置
/** 表格项 */
const COLUMNS_POSITION = [
  {
    dataIndex: 'id_test6',
    key: 'id_test6',
    title: '序号',
  },
  {
    dataIndex: 'positionLevel_test6',
    key: 'positionLevel_test6',
    title: '岗位职级',
  },
  {
    dataIndex: 'minSalary_test6',
    key: 'minSalary_test6',
    title: '底薪',
  },
  {
    dataIndex: 'yearSalary_test6',
    key: 'yearSalary_test6',
    title: '年限工资',
  },
  {
    dataIndex: 'subsidy_test6',
    key: 'subsidy_test6',
    title: '岗位补贴',
  },
  {
    dataIndex: 'saleCount_test6',
    key: 'saleCount_test6',
    title: '考核指标1-销量',
  },
  {
    dataIndex: 'managerCount_test6',
    key: 'managerCount_test6',
    title: '考核指标2-管户',
  },
];
/** 新增表单项 */
const FORM_POSITION = [
  {
    key: 'positionLevel_test6',
    label: '岗位职级',
  },
  {
    key: 'minSalary_test6',
    label: '底薪',
  },
  {
    key: 'yearSalary_test6',
    label: '年限工资',
  },
  {
    key: 'subsidy_test6',
    label: '岗位补贴',
  },
  {
    key: 'saleCount_test6',
    label: '考核指标1-销量',
  },
  {
    key: 'managerCount_test6',
    label: '考核指标2-有效管户',
  },
];
/** 默认表单 */
const DEFAULT_POSITION_FORM = {
  positionLevel_test6: '',
  minSalary_test6: '',
  yearSalary_test6: '',
  subsidy_test6: '',
  saleCount_test6: '',
  managerCount_test6: '',
};
// 人事相关 - 财务核算
const COLUMNS_ACCOUNT: Columns[] = [
  {
    dataIndex: 'id_test7',
    key: 'id_test7',
    title: '序号',
    width: '80px',
  },
  {
    title: '客户&销售信息',
    children: [
      {
        dataIndex: 'sale_test7',
        key: 'sale_test7',
        title: '归属销售',
      },
      {
        dataIndex: 'custName_test7',
        key: 'custName_test7',
        title: '客户姓名',
      },
      {
        dataIndex: 'custId_test7',
        key: 'custId_test7',
        title: '客户号',
        width: '130px',
      },
      {
        dataIndex: 'buyDate_test7',
        key: 'buyDate_test7',
        title: '购买时间',
        width: '120px',
      },
    ],
  },
  {
    title: '产品销售详情',
    children: [
      {
        dataIndex: 'fundName_test7',
        key: 'fundName_test7',
        title: '产品名称',
        slotName: 'fundName',
        width: '180px',
      },
      {
        dataIndex: 'minPeriod_test7',
        key: 'minPeriod_test7',
        title: '产品最低期限',
        width: '120px',
      },
      {
        dataIndex: 'buyAmount_test7',
        key: 'buyAmount_test7',
        title: '购买金额',
      },
      {
        dataIndex: 'haveAmount_test7',
        key: 'haveAmount_test7',
        title: '保有量（购买金额扣除认购费）',
        headerSlotName: 'head_desc',
        width: '200px',
      },
      {
        dataIndex: 'currencyTransform_test7',
        key: 'currencyTransform_test7',
        title: '原货币转化',
        width: '110px',
      },
      {
        dataIndex: 'newBankIn_test7',
        key: 'newBankIn_test7',
        title: '新增银行汇入',
        width: '120px',
      },
    ],
  },
  {
    title: '收入',
    children: [
      {
        dataIndex: 'buyFee_test7',
        key: 'buyFee_test7',
        title: '认购/申购费率',
        slotName: 'rate',
        width: '130px',
      },
      {
        dataIndex: 'manageFee_test7',
        key: 'manageFee_test7',
        title: '产品管理费率（年）',
        headerSlotName: 'head_desc',
        slotName: 'rate',
        width: '120px',
      },
      {
        dataIndex: 'operatFee_test7',
        key: 'operatFee_test7',
        title: '客户维护费率（年）',
        headerSlotName: 'head_desc',
        slotName: 'rate',
        width: '120px',
      },
      {
        dataIndex: 'serviceFee_test7',
        key: 'serviceFee_test7',
        title: '销售服务费率（年）',
        headerSlotName: 'head_desc',
        slotName: 'rate',
        width: '120px',
      },
      {
        dataIndex: 'companyIncome_test7',
        key: 'companyIncome_test7',
        title: '公司总收入',
        width: '110px',
      },
    ],
  },
  {
    title: '支出',
    children: [
      {
        dataIndex: 'chargeFee_test7',
        key: 'chargeFee_test7',
        title: '监管费（扣除）',
        headerSlotName: 'head_desc',
        width: '90px',
      },
      {
        dataIndex: 'currencyManageFeeRate_test7',
        key: 'currencyManageFeeRate_test7',
        title: '原货币管理费率（年）',
        headerSlotName: 'head_desc',
        slotName: 'rate',
        width: '140px',
      },
      {
        dataIndex: 'currencyManageFee_test7',
        key: 'currencyManageFee_test7',
        title: '原货币管理费（扣除）',
        headerSlotName: 'head_desc',
        width: '120px',
      },
      {
        dataIndex: 'payCost_test7',
        key: 'payCost_test7',
        title: '支付成本（扣除）',
        headerSlotName: 'head_desc',
        width: '100px',
      },
    ],
  },
  {
    title: '收入支出汇总',
    children: [
      {
        dataIndex: 'totalIncome_test7',
        key: 'totalIncome_test7',
        title: '总支出',
      },
      {
        dataIndex: 'pureProfit_test7',
        key: 'pureProfit_test7',
        title: '毛总利润',
      },
      {
        dataIndex: 'saleTake_test7',
        key: 'saleTake_test7',
        title: '销售提成',
        slotName: 'rate',
      },
      {
        dataIndex: 'reward_test7',
        key: 'reward_test7',
        title: '奖金',
      },
      {
        dataIndex: 'saleReward_test7',
        key: 'saleReward_test7',
        title: '销售奖金/毛利润',
        headerSlotName: 'head_reward',
        slotName: 'rate',
      },
      {
        dataIndex: 'buyWay_test7',
        key: 'buyWay_test7',
        title: '购买方式',
      },
    ],
  },
];

const PARAMS = {
  PAGE_SIZE,
  PAGE_SIZE_LIST,
  COLUMNS_SALARY,
  COLUMNS_POSITION,
  FORM_POSITION,
  DEFAULT_POSITION_FORM,
  COLUMNS_ACCOUNT,
};

export default PARAMS;
