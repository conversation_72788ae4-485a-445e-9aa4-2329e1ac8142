// 工资核算页
/** 工资核算表格数据 */
export type SalaryTableData = {
  orderNo?: string; // 序号
  giName?: string; // 组别
  siName?: string; // 姓名
  siGh?: string; // 工号
  siId?: number; // 销售id
  grade?: string; // 员工级别
  jobStartTime?: string; // 入职时间
  jobEndTime?: string; // 离职时间
  year_salary?: string; // 年限
  min_teat_salary?: string; // 底薪
  year_salary_salary?: string; // 年限工资
  subsidy_salary?: string; // 岗位补贴
  rule_ratio_salary?: string; // 合规系数
  rule_cut_salary?: string; // 合规扣款
  saleAmount?: string; // 基金销售额
  validManagement?: string; // 有效管户数
  teamCompletionRate?: string; // 组完成率
  bonusCoefficent?: string; // 奖金系数
  bonus?: string; // 奖金
  none_day_salary?: string; // 无薪天数
  sick_day_salary?: string; // 病假天数
  absence_day_salary?: string; // 缺勤天数
  absence_cut_salary?: string; // 缺勤扣款
  total_salary?: string; // 合计
  detail: any; // 销售详情
}

// 岗位信息页
/** 岗位信息表格数据 */
export type PositionTableData = {
  id_test6?: string; // 序号
  positionLevel_test6: string; // 岗位职级
  minSalary_test6: string; // 底薪
  yearSalary_test6: string; // 年限工资
  subsidy_test6: string; // 岗位补贴
  saleCount_test6: string; // 考核指标1-销量
  managerCount_test6: string; // 考核指标2-管户
}

// 财务核算页
/** 财务核算表格数据 */
export type AccountTableData = {
  id_test7?: string; // 序号
  // 客户&销售信息
  sale_test7: string; // 归属销售
  custName_test7: string; // 客户姓名
  custId_test7: string; // 客户号
  buyDate_test7: string; // 购买时间
  // 产品销售详情
  fundCode_test7: string; // 产品code
  fundName_test7: string; // 产品名称
  minPeriod_test7: string; // 产品最低期限
  buyAmount_test7: string; // 购买金额
  haveAmount_test7: string; // 保有量（购买金额扣除认购费）
  currencyTransform_test7: string; // 原货币转化
  newBankIn_test7: string; // 新增银行汇入
  // 收入
  buyFee_test7: string; // 认购/申购费率
  manageFee_test7: string; // 产品管理费率（年）
  operatFee_test7: string; // 客户维护费率（年）
  serviceFee_test7: string; // 销售服务费率（年）
  companyIncome_test7: string; // 公司总收入
  // 支出
  chargeFee_test7: string; // 扣：监管费
  currencyManageFeeRate_test7: string; // 原货币管理费率（年）
  currencyManageFee_test7: string; // 扣：原货币管理费
  payCost_test7: string; // 扣：支付成本
  // 收入支出汇总
  totalIncome_test7: string; // 总支出
  pureProfit_test7: string; // 毛总利润
  saleTake_test7: string; // 销售提成
  reward_test7: string; // 奖金
  saleReward_test7: string; // 销售奖金/毛利润
  buyWay_test7: string; // 购买方式
}

// 架构管理页
export type UserInfo = {
  level: string; // 角色等级
}
type NodeLevel = ''|'root'|'department'|'group';
export type orgTreeData = {
  id?: number; // id
  label: string; // 节点名称
  level: NodeLevel; // 节点等级（crm、部门、小组）
  children: orgTreeData[]; // 子节点
  // onClick: (...data: any[]) => any; // 节点点击事件
  // onCollapse: (...data: any[]) => any; // 折叠子节点
  // onExpand: (...data: any[]) => any; // 展开子节点
}