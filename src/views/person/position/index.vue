<template>
  <div class="m-position-info">
    <p class="m-page-title">销售岗位考核设置</p>
    <div class="m-position-options">
      <a-button type="primary" @click="handleModalShow()">
        新增岗位设置
      </a-button>
    </div>
    <div class="m-position-table table-wrapper">
      <custom-table
        v-model:page-num="pageNum"
        v-model:page-size="pageSize"
        :table-total="total"
        :table-data="tableData"
        :columns="COLUMNS_POSITION"
        :page-size-list="['5', '10', '20', '50']"
        :row-class-name="rowClassName()"
        :on-submit="onTableSubmit"
      ></custom-table>
    </div>
    <a-modal
      v-model:visible="visible"
      title="新增岗位设置"
      class="m-position-modal"
      :confirm-loading="loading"
      @ok="handleConfirm"
    >
      <a-descriptions :column="2">
        <a-descriptions-item
          v-for="item in FORM_POSITION"
          :key="item.key"
          :label="item.label"
        >
          <a-input v-model:value="formData[item.key]" :bordered="false" />
        </a-descriptions-item>
      </a-descriptions>
    </a-modal>
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs } from 'vue';
// const
import PARAMS from '../assets/const';
// components
import CustomTable, {
  useCustomTable,
} from '@/components/common/CustomTable.vue';
// type
import { PositionTableData } from '../type';

const mockPositionTableData: PositionTableData[] = [
  {
    id_test6: '1',
    positionLevel_test6: '一级销售经理',
    minSalary_test6: '5000',
    yearSalary_test6: '12000',
    subsidy_test6: '500',
    saleCount_test6: '6000',
    managerCount_test6: '20',
  }
]
for(let i = 0; i < 10; i++) mockPositionTableData.push(mockPositionTableData[0])

type iState = {
  // table
  total: number;
  tableData: PositionTableData[];
  // modal
  visible: boolean;
  loading: boolean;
  formData: PositionTableData;
};

export default defineComponent({
  name: 'PositionInfo',
  components: {
    CustomTable,
  },
  setup() {
    const {
      PAGE_SIZE,
      PAGE_SIZE_LIST,
      COLUMNS_POSITION,
      FORM_POSITION,
      DEFAULT_POSITION_FORM,
    } = PARAMS;
    const { table, onTableChange, onTableSubmit, rowClassName } =
      useCustomTable(PAGE_SIZE);
    const state = reactive<iState>({
      // table
      total: 0,
      tableData: mockPositionTableData,
      // modal
      visible: false,
      loading: false,
      formData: { ...DEFAULT_POSITION_FORM },
    });

    // methods
    /** 查询表格 */
    const fetchTable = () => {};
    /** TODO 2022.3.8 新增岗位 */
    const postPosition = async () => {
      return '';
    };

    // controller
    /** 展示新增岗位设置弹窗 */
    const handleModalShow = () => {
      state.visible = true;
    };
    /** TODO 2022.3.8 确认新增岗位 */
    const handleConfirm = () => {
      state.loading = true;
      postPosition().then(() => {
        console.log('formData', state.formData);
        state.loading = false;
        state.visible = false;
        state.formData = { ...DEFAULT_POSITION_FORM };
      });
    };

    return {
      PAGE_SIZE_LIST,
      COLUMNS_POSITION,
      FORM_POSITION,
      ...toRefs(table),
      ...toRefs(state),
      rowClassName,
      onTableSubmit: onTableSubmit(fetchTable),
      handleModalShow,
      handleConfirm,
    };
  },
});
</script>

<style lang="less">
.m-position-info {
  padding: 10px;
  .m-position-table {
    padding: 0;
    margin-top: 20px;
  }
}
.m-position-modal {
  .ant-descriptions-item-container {
    align-items: center;
    margin-right: 16px;
    .ant-descriptions-item-label {
      width: 90px;
    }
    input,
    input:hover {
      border-bottom: solid 1px #999;
      padding: 0 10px;
    }
  }
}
</style>
