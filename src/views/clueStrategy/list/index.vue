<template>
  <section id="root"></section>
</template>

<script lang="ts">
import { fetchUserInfo, fetchSaleInfo } from '@/server/api';
import { amisRemoteEmbed } from '@king-fisher/crm-amis-next';
import amisEnv from '@/utils/amisEnv';
import { AMIS_ENV, DEFAULT_SYSTEM_ID } from '@/utils/request';
import { RoleAuth } from '@/utils/const';
import { StrategyStatus, strategyStatusMap } from '../types';
export default {
  data() {
    return {
      //用户名
      userName: '',
      //是否是运营
      isOperator: false,
      //amis节点对象
      amisScoped: null,
    };
  },
  mounted() {
    fetchUserInfo()
      .then((res) => {
        this.userName = res.userName;
        return fetchSaleInfo(res.userId, [106]);
      })
      .then((info) => {
        // 总监和组长由新增常用语的权限
        if (info.role === RoleAuth.operation) {
          this.isOperator = true;
        }
        const amisLib = window.amisRequire('amis');
        const CURRENT_PAGE_ID = 1027;
        //自定义filter，处理状态码展示
        amisLib.registerFilter('strategyStatus', function (input) {
          const statusNumber = Number(input);
          const status = StrategyStatus[statusNumber];
          return status ? strategyStatusMap[status] : '未知状态';
        });
        amisRemoteEmbed(
          '#root',
          {
            id: CURRENT_PAGE_ID,
            systemId: DEFAULT_SYSTEM_ID,
            env: AMIS_ENV,
          },
          {
            // 开启本地缓存
            useLocalCache: true,
          },
          {
            data: {
              isOperator: this.isOperator,
              userName: this.userName,
            },
          },
          amisEnv()
        ).then((scoped) => {
          this.amisScoped = scoped;
        });
      });
  },
  unmounted() {
    this.amisScoped.unmount();
  },
  methods: {},
};
</script>
