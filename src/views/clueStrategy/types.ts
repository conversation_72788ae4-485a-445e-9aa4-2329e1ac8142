/*
1-未开始，2-进行中，3-已结束，4-已中止，7-待运营审核，8-待测试审核，9-审核不通过
*/
export enum StrategyStatus {
  NotStarted = 1,
  InProgress = 2,
  Completed = 3,
  Aborted = 4,
  PendingOperationsReview = 7,
  PendingTestingReview = 8,
  ReviewFailed = 9,
}

type StrategyStatusMap = {
  [key in keyof typeof StrategyStatus]: string;
};

export const strategyStatusMap: StrategyStatusMap = {
  NotStarted: '未开始',
  InProgress: '进行中',
  Completed: '已结束',
  Aborted: '已中止',
  PendingOperationsReview: '待运营审核',
  PendingTestingReview: '待测试审核',
  ReviewFailed: '审核不通过',
};
