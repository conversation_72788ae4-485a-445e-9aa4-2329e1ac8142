<template>
  <section>
      <div :style="{marginBottom:'12px'}">
          <a-range-picker v-model:value="timeRange" :style="{marginRight:'12px'}" />
          <a-select v-model:value="currentMethod" :style="{marginRight:'12px',width:'200px'}" :placeholder="'请选择渠道号'">
              <a-select-option :key="item.capitalMethod" v-for="(item) in methodList" :value="item.capitalMethod">{{item.name}}</a-select-option>
          </a-select>
          <a-button  :style="{marginRight:'12px'}" @click="fetchRecord">查询</a-button>
      </div>
      <a-table :pagination="false" :columns="columns" :dataSource="dataSource" >

          <template #bodyCell="{column,record,text}">
              <template v-if="column.key==='state'">
                  <a-tag  v-if="record.state==='1'">待通知财务</a-tag>
                  <a-tag v-else-if="record.state==='2'"  color='blue'>已通知财务</a-tag>
                  <a-tag  v-else-if="record.state==='3'" color='green'>财务已确认</a-tag>
                  <a-tag v-else>{{record.state}}</a-tag>
              </template>
              <template v-else-if="column.key==='ctime'">
                  <span>{{dayjs(text).format('YYYY-MM-DD HH:mm:ss')}}</span>
              </template>
              <template v-else-if="column.key==='mtime'">
                  <span>{{dayjs(text).format('YYYY-MM-DD HH:mm:ss')}}</span>
              </template>
              <template v-else-if="column.key==='opt'">
                  <a-button :style="{marginRight:'12px'}" @click="downloadRecord(record)">下载明细</a-button>
                  <a-button v-if="record.state==='2'" type='primary' @click="updateRecord(record)">确认打款</a-button>
              </template>
           
              
          </template>
      </a-table>
  </section>
</template>

<script lang="ts">
import dayjs, { Dayjs } from "dayjs"
import {defineComponent,onMounted,ref} from "vue"
import ajax from '@/server/http';
import { message } from 'ant-design-vue';

export default defineComponent({
    setup(){
        const timeRange=ref<[Dayjs,Dayjs]>([dayjs(),dayjs()])
        const currentMethod=ref<string>();
        const methodList=ref<{capitalMethod:string,name:string}[]>([]);
        const dataSource=ref([]);
        const checkParams=()=>{
           
            if(!timeRange.value){
                message.error('请选择时间')
                return false;
            }
             const [startDateDayjs,endDateDayjs] = timeRange.value;
            if(!startDateDayjs||!endDateDayjs){
               
                message.error('请选择时间')
                return false;
            }
            if(!currentMethod.value){
                message.error('请选择渠道号')
                return false;
            }
            return true;
        }
        const fetchMethodList=async()=>{
           
            const res= await ajax('get_method_list')
            console.log("test",res);
            methodList.value=res
        }
        const fetchRecord=async()=>{
            
             if(!checkParams()){
                 return;
                }
            const [startDateDayjs,endDateDayjs] = timeRange.value;
            const res=await ajax('get_banktransfer_record',{
                startDate:startDateDayjs?.format('YYYYMMDD'),
                endDate:endDateDayjs?.format('YYYYMMDD'),
                capitalMethod:currentMethod.value
            })
            console.log(res);
            dataSource.value=res;
        }
        const updateRecord=async(record)=>{
            const {operatorDate,capitalMethod} = record;
            await ajax('update_banktransfer_record',{operatorDate,capitalMethod})
            fetchRecord();
        }
        const downloadRecord=(record)=>{
            const {operatorDate,capitalMethod} = record;
            ajax('download_banktransfer_record',{operatorDate,capitalMethod})
        }
        onMounted(()=>{
            fetchMethodList();
        })
        return {
            timeRange,
            currentMethod,
            methodList,
            dataSource,
            columns:[
              {
                title: '总金额（单位元）',
                key: 'totalAmount',
                dataIndex: 'totalAmount',
                },
                {
                title: '对账日期',
                key: 'operatorDate',
                dataIndex: 'operatorDate',
                },
                {
                title: '渠道编号',
                key: 'capitalMethod',
                dataIndex: 'capitalMethod',
                },
                {
                title: '状态',
                key: 'state',
                dataIndex: 'state',
                },
                {
                title: '操作人员',
                key: 'operator',
                dataIndex: 'operator',
                },
                {
                title: '创建时间',
                key: 'ctime',
                dataIndex: 'ctime',
                },
                {
                title: '更新时间',
                key: 'mtime',
                dataIndex: 'mtime',
                }, 
                {
                title: '操作',
                key: 'opt',
               
                }, 
            ],
            fetchRecord,
            updateRecord,
            downloadRecord,
            dayjs
        }
    }
})
</script>

<style>

</style>