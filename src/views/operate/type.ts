import { Columns } from '../../../types/custom';
type TabMap = {
  'gdlc': ProductionTableData,
  'sm': ProductionTableData,
  'gm': ProductionTableData,
}
type ProductionTableData = {
  name: string; // 列表名称
  columns: Columns[]; // 表格列格式
  dataSource: TableRow[]; // 数据列表
  detailModal: {
    key: string;
    label: string;
    editKey?: string;
    value: ''|'text'|'textarea'|'table'|'radio';
    columns?: {
      key: string|number;
      title: string|number;
      map?: any;
      [key: string]: any;
    }[],
    map?: any;
  }[],
  sortMap?: any; // 排序值表
}

type gdlc_row = {
  id?: number|string; // 唯一标识
  code?: string; // 产品代码
  product_name?: string; // 产品名称
  sale_commission?: number; // 销售佣金
  inter_recommend_level?: number; // 内部推荐等级
  risk_level?: number; // 风险等级
  if_new?: number; // 是否新发
  net?: number; // 最新净值
  end_date?: string; // 净值日期
  year?: number; // 近一年收益
  buy_status?: number; // 交易状态
  open_start_date?: string; // 开放开始时间
  open_end_date?: string; // 开放结束时间
  min_buy_amount?: number; // 起购金额（元）
  options?: string[]; // 操作
  delFlag?: boolean; // 是否可删除
  relatedInfo?: {
    taa_id: number;
    activity_name: string;
  }[]; // 是否参加万元户活动
  c_time?: string; // 上架时间
  m_time?: string; // 修改时间

  // 风险等级相关新增字段
  custRiskList?: number[]; // 风险等级数组
  productRisk?: string; // 风险等级描述
}
type sm_row = {
  id?: number|string; // 唯一标识
  code?: string; // 产品代码
  product_name?: string; // 产品名称
  sale_commission?: number; // 销售佣金
  inter_recommend_level?: number; // 内部推荐等级
  risk_level?: number; // 风险等级
  is_valid?: number; // 是否有效
  net?: number; // 最新净值
  year?: number; // 近一年收益
  buy_status?: number; // 交易状态
  subsce_start?: string; // 认购开始时间
  subsce_end?: string; // 认购结束时间
  subsce_origin?: number; // 认购起点（元）
  manage_rate?: number; // 管理费率
  options?: string[]; // 操作
  delFlag?: boolean; // 是否可删除
  relatedInfo?: {
    taa_id: number;
    activity_name: string;
  }[]; // 是否参加万元户活动
  c_time?: string; // 上架时间
  m_time?: string; // 修改时间

  // 风险等级相关新增字段
  custRiskList?: number[]; // 风险等级数组
  productRisk?: string; // 风险等级描述  
}
type gm_row = {
  id?: number|string; // 唯一标识
  code?: string; // 产品代码
  product_name?: string; // 产品名称
  inter_recommend_level?: number; // 内部推荐等级
  if_new?: number; // 是否新发
  fund_invest_type?: number; // 基金类型
  raise_period?: string; // 募集期
  perfm_compare_benchmark?: string; // 业绩比较基准
  sale_commission?: number; // 销售佣金
  manage_fee?: number; // 管理费
  custody_fee?: number; // 托管费
  sales_serv_fee?: number; // 销售服务费
  fund_manager_list?: string; // 基金经理
  options?: string[]; // 操作
  delFlag?: boolean; // 是否可删除
  relatedInfo?: {
    taa_id: number;
    activity_name: string;
  }[]; // 是否参加万元户活动
  c_time?: string; // 上架时间
  m_time?: string; // 修改时间

  // 风险等级相关新增字段
  custRiskList?: number[]; // 风险等级数组
  productRisk?: string; // 风险等级描述
}
type TableRow = gdlc_row | sm_row | gm_row;

export {
  TabMap,
  ProductionTableData,
  TableRow,
}