.operate-production-list {
  padding: 20px;
  .options-group {
    display: flex;
    margin-bottom: 20px;
    .list-tab-group {
      display: flex;
      button {
        min-width: 80px;
        margin-right: 16px;
        border-radius: 8px;
      }
    }
    .list-add-btn {
      margin-left: 100px;
    }
    .m-standard-upload {
      margin-left: 16px;
    }
  }
  .production-search {
    margin-bottom: 20px;
    > * {
      margin-right: 20px;
    }
    .ant-input-search {
      width: 200px;
    }
    .production-join {
      width: 200px;
    }
  }
  .production-table {
    .ant-table-cell {
      padding: 8px;
      .table-title-sort,
      .table-title-pick > .pick-title {
        justify-content: center;
      }
      .cell-relatedInfo {
        max-height: 80px;
        padding: 0 4px;
        > p {
          margin: 4px 0;
        }
      }
      .cell-m_time > span {
        display: block;
      }
      .cell-risk_level,
      .cell-investor_risk_level,
      .cell-productRisk {
        > p {
          display: flex;
          flex-flow: column;
          justify-content: center;
          align-items: center;
          cursor: pointer;
        }
      }
      .cell-options {
        min-width: 180px;
        .table-cell-options {
          display: flex;
          > button {
            padding: 0 8px;
          }
        }
      }
    }
  }
  .production-detail {
    .modal-options {
      margin-left: auto;
      > * {
        margin-left: 16px;
      }
    }
  }
  .m-image {
    background-position: 0 0;
    background-repeat: no-repeat;
    background-size: 100% 100%;
  }
  .link-button {
    color: #1890ff;
  }
}