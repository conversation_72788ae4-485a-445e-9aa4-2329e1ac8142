<template>
  <section class="operate-production-list">
    <div class="options-group">
      <div class="list-tab-group">
        <a-button
          v-for="(_tab, index) in tabs"
          :key="_tab"
          :type="index === tab ? 'primary' : 'default'"
          @click="handleTabClick(index)"
        >
          {{ tab_map[_tab].name }}
        </a-button>
      </div>
      <a-button
        ghost
        class="list-add-btn"
        type="primary"
        :disabled="!addAuth"
        @click="handleAddClick"
      >
        新增产品
      </a-button>
    </div>
    <div class="table-wrapper">
      <div class="production-search filter-wrapper">
        <a-input-search
          v-model:value="search.name"
          placeholder="请输入代码/产品名称"
          @search="handleSearch"
        />
        <!-- 2022.5.6 筛选项-是否参加万元户活动 -->
        <a-select
          v-model:value="search.join"
          class="production-join"
          placeholder="请选择参与活动"
          allow-clear
        >
          <a-select-option
            v-for="option in saleActivityList"
            :key="option.taa_id"
            :value="Number(option.taa_id)"
          >
            {{ option.activity_name }}
          </a-select-option>
        </a-select>
        <a-button type="primary" @click="handleSearch">查询</a-button>
      </div>
      <div class="production-table">
        <custom-table
          v-model:page-num="pageNum"
          v-model:page-size="pageSize"
          :table-total="total"
          :table-data="tableData.dataSource"
          :columns="tableData.columns"
          :default-page-size="PAGE_SIZE"
          :page-size-list="PAGE_SIZE_LIST"
          :scroll="{ x: 2600, y: 600 }"
          :row-class-name="rowClassName()"
          :on-submit="onTableSubmit"
        >
          <!-- 百分数 -->
          <template #rate="{ text }">{{ text || '--' }}%</template>
          <!-- 内部推荐等级 -->
          <template #inter_recommend_level="{ text }">
            <div>{{ recommandLevel[text] || '--' }}</div>
          </template>
          <!-- 最低持有期 -->
          <template #lowest_hold_day="{ text }">
            {{ text ? `${text}天` : '--' }}
          </template>
          <!-- 2022.5.6 参与xx元户活动 -->
          <template #relatedInfo="{ record }">
            <template
              v-if="record.relatedInfo.filter(item => item.is_valid).length > 0"
            >
              <p
                v-for="item in record.relatedInfo"
                v-show="item.is_valid"
                :key="item.taa_id"
              >
                {{ item.activity_name }}
              </p>
            </template>
            <span v-else>未参加活动</span>
          </template>
          <!-- 2022.5.6 上架&修改时间 -->
          <template #update_time="{ record }">
            <span>
              {{ record.c_time.toString().slice(0, 10) || '--' }} 上架
            </span>
            <span>
              {{ record.m_time.toString().slice(0, 10) || '--' }} 修改
            </span>
          </template>
          <!-- 风险等级 - 高端理财 -->
          <template #risk_level="{ record }">
            <p class="link-button" @click="handleToRank(record)">
              <span v-for="(str, index) in riskLevelText(record)" :key="index">
                {{ str }}
              </span>
            </p>
          </template>
          <!-- 是否新发 -->
          <template #if_new="{ text }">
            <div>{{ text === 1 ? '是' : '否' }}</div>
          </template>
          <!-- 是否有效 -->
          <template #is_valid="{ text }">
            <div>{{ text === 1 ? '有效' : '无效' }}</div>
          </template>
          <!-- 交易状态 -->
          <template #buy_status="{ text }">
            <div>{{ TRADE_STATUS[text] || '--' }}</div>
          </template>
          <!-- 基金类型 -->
          <template #fund_invest_type="{ text }">
            <div>{{ FUND_TYPE[text] || '--' }}</div>
          </template>
          <!-- 募集期 -->
          <template #raise_period="{ record }">
            <div>
              {{
                record.start_time || record.end_timer
                  ? `${record.start_time || ''}~${record.end_time || ''}`
                  : '--'
              }}
            </div>
          </template>
          <!-- options -->
          <template #options="{ index, record }">
            <div class="table-cell-options">
              <a-button type="link" @click="handleToDetail(index)">
                查看详情
              </a-button>
              <a-button
                type="link"
                :disabled="!addAuth"
                @click="handleEdit(index)"
              >
                编辑
              </a-button>
              <a-popconfirm
                title="确认删除吗？"
                :disabled="!delAuth(record)"
                @confirm="handleDelete(index)"
              >
                <a-button
                  class="cell-options-del"
                  type="link"
                  danger
                  :disabled="!delAuth(record)"
                >
                  删除
                </a-button>
              </a-popconfirm>
            </div>
          </template>
        </custom-table>
      </div>
    </div>
    <div class="production-detail">
      <custom-modal
        :visible="modalVisible"
        :title="modalOption === 'add' ? '新增产品' : nowRecord.product_name"
        :custom-footer="true"
      >
        <!-- 窗体 -->
        <template #content>
          <product-detail
            v-model:code="modalCode"
            v-model:checked="modalCheck"
            v-model:submit="modalSubmit"
            :visible="modalVisible"
            :detail-modal="detailModal"
            :detail-data="modalData"
            :option="modalOption"
            :recommand-level="recommandLevel"
            :type="tabs[tab]"
            :sale-activity-list="saleActivityList"
            :on-submit="onDetailSubmit"
          />
        </template>
        <!-- 底部按钮 -->
        <template #footer>
          <div v-if="modalOption === 'scan'" class="modal-options">
            <a-button type="primary" ghost @click="handleModalCancel">
              关闭
            </a-button>
          </div>
          <div v-else-if="modalOption === 'edit'" class="modal-options">
            <a-button
              type="primary"
              :disabled="!modalCheck"
              @click="handleModalConfirm"
            >
              完成修改
            </a-button>
            <a-button type="primary" ghost @click="handleModalCancel">
              取消
            </a-button>
          </div>
          <div v-else-if="modalOption === 'add'" class="modal-options">
            <a-button type="primary" @click="handleModalPull">
              根据代码拉取数据
            </a-button>
            <a-button
              type="primary"
              :disabled="Object.keys(modalData).length === 0 || !modalCheck"
              @click="handleModalConfirm"
            >
              确认新增
            </a-button>
            <a-button type="primary" ghost @click="handleModalCancel">
              取消
            </a-button>
          </div>
        </template>
      </custom-modal>
    </div>
  </section>
</template>

<script lang="ts">
import {
  computed,
  defineComponent,
  onMounted,
  reactive,
  toRefs,
  watch,
  ref,
} from 'vue';
import PARAMS from './assets/const';
import CustomTable, {
  useCustomTable,
} from '@/components/common/CustomTable.vue';
import CustomModal from '@/components/common/CustomModal.vue';
import ProductDetail from '@/components/operate/ProductDetail.vue';
import { ProductionTableData, TabMap, TableRow } from './type';
import ajax from '@/server/http';
import RESOURCE from '@/router/resource';
import { fetchUserAdmin, localDev } from '@/server/api';
import { useRouter } from 'vue-router';
import { getUrlParam } from '@/utils/fn';
import { UploadOutlined } from '@ant-design/icons-vue';
import { fetchSaleActivityList } from '@/utils/common';

type iState = {
  tab: number; // 当前tab
  tab_map: TabMap; // 页面tab数据总项
  tabs: string[]; // tab数组
  // total: number; // 总数
  search: {
    name: string; // 代码或名称
    join: string; // 是否参加万元户活动
  }; // 搜索条件
  recordIndex: number; // 操作行索引
  recommandLevel: {
    [key: number]: string;
  }; // 内部推荐等级表

  modalVisible: boolean; // 展示产品弹窗
  modalData: {
    [key: string]: any; // 产品信息
  };
  modalOption: '' | 'scan' | 'edit' | 'add'; // 弹窗操作类型（scan 查看；edit 编辑；add 新增）
  modalSubmit: boolean; // 是否提交修改表格
  modalCode: string; // 弹窗产品代码
  modalCheck: boolean; // 弹窗内容校验
  delAuthFromApi: boolean; // 删除权限
  editAuthFromApi: boolean; // 编辑权限
};
const _tabs: ['gdlc', 'sm', 'gm'] = ['gdlc', 'sm', 'gm'];
const default_submitOptions = {
  sortKey: '',
  sortOrder: 'none',
  pickMap: {},
};

export default defineComponent({
  name: 'ProductionList',
  components: {
    CustomTable,
    CustomModal,
    ProductDetail,
    UploadOutlined,
  },
  setup() {
    const router = useRouter();
    // 2022.5.6 万元户筛选项
    const saleActivityList = ref<{ taa_id: number; activity_name: string }[]>(
      []
    );
    const { table, onTableChange, onTableSubmit, rowClassName } =
      useCustomTable(PARAMS.PAGE_SIZE);
    const state = reactive<iState>({
      tab: 0,
      tabs: _tabs,
      tab_map: PARAMS.TAB_MAP,
      // total: 0,
      search: {
        name: '',
        join: null,
      },

      recommandLevel: {},

      recordIndex: -1,
      modalVisible: false,
      modalData: {},
      modalOption: '',
      modalCode: '',
      modalSubmit: false,
      modalCheck: false,
      // 获取用户权限
      delAuthFromApi: false,
      editAuthFromApi: false,
    });
    // computed
    /** 当前产品类型 */
    const tabType = computed(() => {
      return _tabs[state.tab];
    });
    /** 当前表格数据 */
    const tableData = computed(() => {
      let _data: ProductionTableData = state.tab_map[tabType.value];
      return _data;
    });
    /** 当前操作记录 */
    const nowRecord = computed(() => {
      if (state.recordIndex < 0) return {};
      let record = tableData.value.dataSource[state.recordIndex] || {};
      return record;
    });
    /** 详情弹窗数据 */
    const detailModal = computed(() => {
      return tableData.value.detailModal;
    });
    /** 新增/编辑权限 */
    const addAuth = computed(() => {
      if (localDev && getUrlParam('edit') === 'true') return true;
      return state.editAuthFromApi;
    });
    // watch
    /** 监听tab变化 */
    watch(
      () => state.tab,
      (tab, prevTab) => {
        console.log(tab, prevTab);
        if (tab !== prevTab) {
          table.submitOptions = default_submitOptions;
          state.search = {
            name: '',
            join: null,
          };
          state.modalData = {};
          onTableChange(fetchTable);
        }
      }
    );
    /** 打开产品详情弹窗 */
    watch(
      () => [state.tab, state.modalOption, state.recordIndex],
      ([tab, option, index], [oldTab, oldOption, oldIndex]) => {
        // 如果是同tab下的非首次查看同一个记录详情，无需额外请求
        if (
          !option ||
          option === 'add' ||
          (tab === oldTab &&
            Object.keys(state.modalData).length > 0 &&
            index === oldIndex)
        )
          return;
        fetchDetail('id');
      }
    );
    /** 关闭弹窗 */
    watch(
      () => state.modalVisible,
      (visible, prevVisible) => {
        if (!visible && prevVisible) {
          state.modalOption = '';
          state.modalCode = '';
        }
      }
    );
    // methods
    /** 初始化 */
    const init = async () => {
      saleActivityList.value = await fetchSaleActivityList();
      await fetchRecommand();
      fetchTable();
      // 获取用户权限
      fetchUserAdmin([RESOURCE.production_del, RESOURCE.production_edit]).then(
        ([delAuthFromApi, editAuthFromApi]) => {
          state.delAuthFromApi = delAuthFromApi;
          state.editAuthFromApi = editAuthFromApi;
        }
      );
    };
    /** 查询内部推荐等级 */
    const fetchRecommand = () => {
      ajax('get_recommend_level').then((res: any) => {
        console.log('res', res);
        if (res) {
          res = res.recommend_level_list;
          res.forEach(recommend => {
            state.recommandLevel[recommend.id] = recommend.name;
          });
          // 将表格中的筛选项替换
          for (let type in state.tab_map) {
            state.tab_map[type].columns.forEach(col => {
              if (col.dataIndex === 'inter_recommend_level' && col.pickList) {
                col.pickList = res.map(recommend => ({
                  value: recommend.id,
                  label: recommend.name,
                }));
              }
            });
          }
          console.log(state.tab_map);
        }
      });
    };
    /** 获取表格 */
    const fetchTable = async () => {
      state.recordIndex = -1;
      const type = _tabs[state.tab];
      // 转换筛选数据项
      let options: any = {
        codeOrName: state.search.name,
        taa_id: state.search.join,
      };
      // 添加排序项
      if (table.submitOptions.sortOrder !== 'none') {
        console.log('添加排序项');
        const sortKey = table.submitOptions.sortKey;
        options.order = [
          {
            item: state.tab_map[type].sortMap[sortKey],
            asc: table.submitOptions.sortOrder === 'up',
          },
        ];
      }
      // 遍历筛选项
      for (let key in table.submitOptions.pickMap) {
        options[key] = table.submitOptions.pickMap[key];
      }

      await ajax(`operate_get_${type}`, options, {
        pageNum: table.pageNum,
        pageSize: table.pageSize,
      })
        .then((res: any) => {
          if (res) {
            table.total = res.total;
            state.tab_map[type].dataSource = res.crmRecord;
          } else {
            table.total = 0;
            state.tab_map[type].dataSource = [];
          }
        })
        .catch(() => {
          table.total = 0;
          state.tab_map[type].dataSource = [];
        });
      await fetchActivityInfo();
    };
    /** 2022.5.6 万元户 获取产品对应活动信息 */
    const fetchActivityInfo = () => {
      const type = _tabs[state.tab];
      const fundType = [3, 1, 2];
      const fundCodeList = tableData.value.dataSource.map(fund => fund.code);
      ajax('fetchProductionActivity', {
        fund_type: fundType[state.tab],
        fund_code_list: fundCodeList,
      }).then((res: any) => {
        try {
          const fundActivityInfo: {
            [x: string]: any;
          } = {};
          res.forEach(fund => {
            const map: any = {};
            fund.activity_info_list.forEach(activity => {
              map[activity.taa_id] = activity;
            });
            fundActivityInfo[fund.fund_code] = saleActivityList.value.map(
              _activity => {
                const act = map[_activity.taa_id];
                if (act) {
                  return { ...act, is_valid: 1 };
                } else {
                  return { ..._activity, is_valid: 0 };
                }
              }
            );
          });
          state.tab_map[type].dataSource = state.tab_map[type].dataSource.map(
            row => {
              row.relatedInfo = fundActivityInfo[row.code];
              return row;
            }
          );
        } catch (e) {
          console.error(e);
        }
      });
    };
    /**
     * 根据code/id获取详情表格
     * @param type 'code' | 'id'
     */
    const fetchDetail = (type: 'code' | 'id') => {
      let data = {};
      if (type === 'id') {
        data[type] = nowRecord.value[type];
      } else {
        data[type] = state.modalCode;
      }
      ajax(`operate_get_detail_${type}_${tabType.value}`, data).then(
        (res: any) => {
          if (res) {
            console.log('fetchDetail', res);
            state.modalData = { ...res };
            if (state.recordIndex > -1) {
              state.modalData['relatedInfo'] =
                tableData.value.dataSource[state.recordIndex].relatedInfo;
            }
          }
        }
      );
    };
    /**
     * 发送详情表格
     * @param data 详情数据
     */
    const postDetail = async (data: any) => {
      return ajax(`operate_add_${tabType.value}`, data)
        .then((res: any) => {
          console.log('postDetail', data);
          state.modalVisible = false;
          state.modalOption = '';
          state.modalCode = '';
          return true;
        })
        .catch(() => {
          return false;
        });
    };
    /** 发送产品-活动关联信息 */
    const postContract = async (data: TableRow) => {
      const fundType = [3, 1, 2];
      return ajax('editProductionActivity', {
        activity_list: data.relatedInfo,
        fund_code: data.code,
        fund_type: fundType[state.tab],
      });
    };
    /** 删除记录 */
    const postTableDel = () => {
      const data = {
        type: PARAMS.DELETE_MAP[tabType.value],
        id: tableData.value.dataSource[state.recordIndex].id,
      };
      ajax('operate_delete', data).then((res: any) => {
        console.log('postTableDel', res, data);
        fetchTable();
      });
    };
    /**
     * 删除权限
     * @param record 表格行数据
     */
    const delAuth = (record: TableRow) => {
      if (localDev && getUrlParam('delete') === 'true') return true;
      return record.delFlag && state.delAuthFromApi;
    };
    /**
     * 完成详情编辑
     * @param data 详情数据
     */
    const onDetailSubmit = async (data: TableRow) => {
      const data1 = { ...data };
      delete data1['relatedInfo'];
      await postDetail(data1);
      await postContract(data);
      fetchTable();
    };
    /**
     * 风险等级文字显示
     * @param record 表格行数据
     * @returns 风险文字数组
     */
    const riskLevelText = (record: TableRow) => {
      const str = record.productRisk || '';
      return [str.slice(0, str.indexOf('(')), str.slice(str.indexOf('('))];
    };

    /**
     * 点击tab
     * @param tab tab索引
     */
    const handleTabClick = (tab: number) => {
      state.tab = tab;
    };
    /** 新增产品 */
    const handleAddClick = () => {
      console.log('新增产品');
      state.modalVisible = true;
      state.modalOption = 'add';
      state.modalData = {};
    };
    /** 搜索产品 */
    const handleSearch = () => {
      console.log('搜索', state.search);
      onTableChange(fetchTable);
    };
    /**
     * 查看详情
     * @param index 索引
     */
    const handleToDetail = (index: number) => {
      console.log('查看详情', index);
      state.modalVisible = true;
      state.modalOption = 'scan';
      state.recordIndex = index;
    };
    /**
     * 修改
     * @param index 索引
     */
    const handleEdit = (index: number) => {
      console.log('编辑产品', index);
      state.modalVisible = true;
      state.modalOption = 'edit';
      state.recordIndex = index;
    };
    /**
     * 删除
     * @param index 索引
     */
    const handleDelete = (index: number) => {
      console.log('删除产品', index);
      state.recordIndex = index;
      postTableDel();
    };
    /** 产品弹窗 - 取消/关闭 */
    const handleModalCancel = () => {
      console.log('cancel');
      state.modalVisible = false;
    };
    /** 产品弹窗 - 完成修改/确认新增 */
    const handleModalConfirm = () => {
      state.modalSubmit = true;
    };
    /** 产品弹窗 - 拉取数据 */
    const handleModalPull = () => {
      console.log('pull data', state.modalCode);
      fetchDetail('code');
    };
    /**
     * 风险等级跳转页面
     * @param record 表格行数据
     */
    const handleToRank = (record: TableRow) => {
      console.log('跳转风险等级页面', record);
      router.push({
        path: '/customer/list',
        query: {
          grade: record.custRiskList[0] || 1,
        },
      });
    };

    onMounted(init);
    return {
      saleActivityList,
      ...toRefs(table),
      ...toRefs(state),
      tableData,
      nowRecord,
      detailModal,
      addAuth,
      onTableSubmit: onTableSubmit(fetchTable),
      onDetailSubmit,
      postDetail,
      delAuth,
      riskLevelText,
      rowClassName,
      handleTabClick,
      handleAddClick,
      handleSearch,
      handleToDetail,
      handleEdit,
      handleDelete,
      handleModalCancel,
      handleModalConfirm,
      handleModalPull,
      handleToRank,
      ...PARAMS,
    };
  },
});
</script>

<style lang="less">
@import './index.less';
</style>
