import { TabMap } from '../type';

/** 风险等级 */
const RISK_LEVEL = {
  1: '低风险',
  2: '中低风险',
  3: '中风险',
  4: '中高风险',
  5: '高风险',
};
/** 交易状态 */
const TRADE_STATUS = {
  1: '预约',
  2: '在售',
  3: '售罄',
  5: '开售提醒',
  6: '即将开售',
  7: '封闭期',
};
/** 基金类型 */
const FUND_TYPE = {
  0: '股票型',
  1: '债务型',
  2: '混合型',
  3: '货币型',
  4: '其他',
  6: '基金型',
  7: '保本型',
  8: 'REITs',
};
/** 删除记录对应索引 */
const DELETE_MAP = {
  sm: 1,
  gm: 2,
  gdlc: 3,
};
/** tab配置项 */
const TAB_MAP: TabMap = {
  // 高端理财
  gdlc: {
    name: '高端理财',
    columns: [
      {
        dataIndex: 'id',
        key: 'id',
        title: '序号',
        width: 80,
      },
      {
        dataIndex: 'code',
        key: 'code',
        title: '产品代码',
        width: 90,
      },
      {
        dataIndex: 'product_name',
        key: 'product_name',
        title: '产品名称',
        width: 200,
      },
      {
        dataIndex: 'lowest_hold_day',
        key: 'lowest_hold_day',
        title: '最低持有期',
        slotName: 'lowest_hold_day',
        width: 100,
      },
      {
        dataIndex: 'sale_commission',
        key: 'sale_commission',
        title: '销售佣金',
        sort: true,
        slotName: 'rate',
        width: 120,
      },
      {
        dataIndex: 'inter_recommend_level',
        key: 'inter_recommend_level',
        title: '内部推荐等级',
        pickList: [
          {
            value: 1262,
            label: 'S',
          },
          {
            value: 1263,
            label: 'A',
          },
          {
            value: 1264,
            label: 'B',
          },
        ],
        slotName: 'inter_recommend_level',
        width: 150,
      },
      // 2022.5.6 是否参与万元户活动
      {
        dataIndex: 'relatedInfo',
        key: 'relatedInfo',
        title: '参与活动列表',
        slotName: 'relatedInfo',
        width: 200,
      },
      {
        dataIndex: 'risk_level',
        key: 'risk_level',
        title: '风险等级',
        pickList: [
          {
            value: '1',
            label: '低风险',
          },
          {
            value: '2',
            label: '中低风险',
          },
          {
            value: '3',
            label: '中风险',
          },
          {
            value: '4',
            label: '中高风险',
          },
          {
            value: '5',
            label: '高风险',
          },
        ],
        slotName: 'risk_level',
        width: 240,
      },
      // 2022.5.6 新增上架&修改时间
      {
        dataIndex: 'm_time',
        key: 'm_time',
        title: '上架&修改时间',
        slotName: 'update_time',
        width: 120,
      },
      {
        dataIndex: 'if_new',
        key: 'if_new',
        title: '是否新发',
        slotName: 'if_new',
        width: 80,
      },
      {
        dataIndex: 'net',
        key: 'net',
        title: '最新净值',
        sort: true,
        width: 120,
      },
      {
        dataIndex: 'end_date',
        key: 'end_date',
        title: '净值日期',
        width: 120,
      },
      {
        dataIndex: 'year',
        key: 'year',
        title: '近一年收益',
        sort: true,
        slotName: 'rate',
        width: 150,
      },
      {
        dataIndex: 'buy_status',
        key: 'buy_status',
        title: '交易状态',
        slotName: 'buy_status',
        width: 120,
      },
      {
        dataIndex: 'open_start_date',
        key: 'open_start_date',
        title: '开放开始时间',
        width: 120,
      },
      {
        dataIndex: 'open_end_date',
        key: 'open_end_date',
        title: '开放结束时间',
        width: 120,
      },
      {
        dataIndex: 'min_buy_amount',
        key: 'min_buy_amount',
        title: '起购金额（元）',
        sort: true,
        width: 150,
      },
      {
        dataIndex: 'options',
        key: 'options',
        title: '操作',
        slotName: 'options',
        width: 180,
        fixed: 'right',
      },
    ],
    dataSource: [],
    detailModal: [
      {
        key: 'code',
        label: '产品代码',
        editKey: 'code',
        value: 'text',
      },
      {
        key: 'product_name',
        label: '产品名称',
        value: 'text',
      },
      {
        key: 'inter_recommend_level',
        label: '内部推荐等级',
        editKey: 'inter_recommend_level',
        value: 'text',
      },
      // 2022.5.6 万元户
      {
        key: 'relatedInfo',
        label: '参加何种活动',
        editKey: 'relatedInfo',
        value: 'radio',
      },
      {
        key: 'sale_commission',
        label: '销售佣金',
        editKey: 'sale_commission',
        value: 'text',
      },
      {
        key: 'lowest_hold_day',
        label: '最低持有期',
        editKey: 'lowest_hold_day',
        value: 'text',
      },
      {
        key: 'sale_words',
        label: '销售话术',
        editKey: 'sale_words',
        value: 'text',
      },
      {
        key: 'if_new',
        label: '是否新发',
        value: 'text',
        map: {
          1: '新发',
          0: '非新发',
        },
      },
      {
        key: 'open_start_date',
        label: '开放开始日期',
        value: 'text',
      },
      {
        key: 'open_end_date',
        label: '开放结束日期',
        value: 'text',
      },
      {
        key: 'redeem_open_start_date',
        label: '赎回开放开始日期',
        value: 'text',
      },
      {
        key: 'redeem_open_end_date',
        label: '赎回开放结束日期',
        value: 'text',
      },
      {
        key: 'old_open_start_date',
        label: '处理之前的开放开始日期',
        value: 'text',
      },
      {
        key: 'old_open_end_date',
        label: '处理之前的开放结束日期',
        value: 'text',
      },
      {
        key: 'sy_desc',
        label: '收益描述',
        value: 'text',
      },
      {
        key: 'sy_value',
        label: '收益值',
        value: 'text',
      },
      {
        key: 'net',
        label: '净值',
        value: 'text',
      },
      {
        key: 'end_date',
        label: '净值日期',
        value: 'text',
      },
      {
        key: 'profit_info',
        label: '区间收益数据',
        value: 'table',
        columns: [
          {
            key: 'time',
            title: '时间',
            map: {
              now: '成立以来收益',
              month: '近一月收益',
              tmonth: '近三月收益',
              hyear: '近半年收益',
              year: '近一年收益',
              twoyear: '近两年收益',
              tyear: '近三年收益',
              fyear: '近五年收益',
              nowyear: '今年以来收益',
            },
          },
          {
            key: 'syValue',
            title: '收益率',
          },
        ],
      },
      {
        key: 'hs_profit_info',
        label: '沪深300 区间收益',
        value: 'table',
        columns: [
          {
            key: 'time',
            title: '时间',
            map: {
              week: '近一周收益',
              month: '近一月收益',
              tmonth: '近三月收益',
              hyear: '近半年收益',
              year: '近一年收益',
              twoyear: '近两年收益',
              tyear: '近三年收益',
              fyear: '近五年收益',
              nowyear: '今年以来收益',
            },
          },
          {
            key: 'syValue',
            title: '收益率',
          },
        ],
      },
      {
        key: 'min_buy_amount',
        label: '起购金额（元）',
        value: 'text',
      },
      {
        key: 'add_buy_amount',
        label: '追加金额（元）',
        value: 'text',
      },
      {
        key: 'trade_fee_ratio',
        label: '购买手续费',
        value: 'text',
      },
      {
        key: 'redeem_rate',
        label: '赎回手续费',
        value: 'text',
      },
      {
        key: 'share_type',
        label: '分红方式',
        value: 'text',
      },
      {
        key: 'manager_name',
        label: '管理人名称',
        value: 'text',
      },
      {
        key: 'open_period_rule',
        label: '开放期规则',
        value: 'text',
      },
      {
        key: 'is_xy',
        label: '是否续约',
        value: 'text',
        map: {
          1: '能',
          0: '不能',
        },
      },
      {
        key: 'no_sale_cnt',
        label: '未销售人数',
        value: 'text',
      },
      {
        key: 'buy_status',
        label: '交易状态',
        value: 'text',
        map: TRADE_STATUS,
      },
      {
        key: 'product_type',
        label: '产品类型',
        value: 'text',
        map: {
          smallset: '小集合',
          specialBankAccount: '专户',
        },
      },
      {
        key: 'estab_date',
        label: '成立日期',
        value: 'text',
      },
      {
        key: 'arrival_desc',
        label: '到期描述',
        value: 'text',
      },
      {
        key: 'anti_risk_capacity',
        label: '抗风险能力',
        value: 'table',
        columns: [
          {
            key: 'year',
            title: '年份',
          },
          {
            key: 'rate',
            title: '涨跌幅',
          },
          {
            key: 'max_draw_down',
            title: '最大回撤',
          },
        ],
      },
      {
        key: 'risk_level',
        label: '风险等级',
        value: 'text',
        map: RISK_LEVEL,
      },
    ],
    sortMap: {
      sale_commission: 1,
      net: 2,
      year: 3,
      min_buy_amount: 4,
    },
  },
  // 私募
  sm: {
    name: '私募产品',
    columns: [
      {
        dataIndex: 'id',
        key: 'id',
        title: '序号',
        width: 60,
      },
      {
        dataIndex: 'code',
        key: 'code',
        title: '产品代码',
        width: 100,
      },
      {
        dataIndex: 'product_name',
        key: 'product_name',
        title: '产品名称',
        width: 240,
      },
      {
        dataIndex: 'lowest_hold_day',
        key: 'lowest_hold_day',
        title: '最低持有期',
        slotName: 'lowest_hold_day',
        width: 100,
      },
      {
        dataIndex: 'sale_commission',
        key: 'sale_commission',
        title: '销售佣金',
        sort: true,
        slotName: 'rate',
        width: 120,
      },
      {
        dataIndex: 'inter_recommend_level',
        key: 'inter_recommend_level',
        title: '内部推荐等级',
        pickList: [
          {
            value: 1262,
            label: 'S',
          },
          {
            value: 1263,
            label: 'A',
          },
          {
            value: 1264,
            label: 'B',
          },
        ],
        slotName: 'inter_recommend_level',
        width: 150,
      },
      // 2022.5.6 是否参与万元户活动
      {
        dataIndex: 'relatedInfo',
        key: 'relatedInfo',
        title: '参与活动列表',
        slotName: 'relatedInfo',
        width: 200,
      },
      {
        dataIndex: 'investor_risk_level',
        key: 'investor_risk_level',
        title: '风险等级',
        slotName: 'risk_level',
        width: 260,
      },
      // 2022.5.6 新增上架&修改时间
      {
        dataIndex: 'm_time',
        key: 'm_time',
        title: '上架&修改时间',
        slotName: 'update_time',
        width: 120,
      },
      {
        dataIndex: 'is_valid',
        key: 'is_valid',
        title: '是否有效',
        slotName: 'is_valid',
        pickList: [
          {
            value: 1,
            label: '有效',
          },
          {
            value: 0,
            label: '无效',
          },
        ],
        width: 100,
      },
      {
        dataIndex: 'net',
        key: 'net',
        title: '最新净值',
        sort: true,
        width: 120,
      },
      {
        dataIndex: 'year',
        key: 'year',
        title: '近一年收益',
        sort: true,
        slotName: 'rate',
        width: 150,
      },
      {
        dataIndex: 'buy_status',
        key: 'buy_status',
        title: '交易状态',
        slotName: 'buy_status',
        width: 120,
      },
      {
        dataIndex: 'subsce_start',
        key: 'subsce_start',
        title: '认购开始时间',
        width: 120,
      },
      {
        dataIndex: 'subsce_end',
        key: 'subsce_end',
        title: '认购截止时间',
        width: 120,
      },
      {
        dataIndex: 'subsce_origin',
        key: 'subsce_origin',
        title: '认购起点（元）',
        sort: true,
        width: 150,
      },
      {
        dataIndex: 'manage_rate',
        key: 'manage_rate',
        title: '管理费率',
        sort: true,
        slotName: 'rate',
        width: 150,
      },
      {
        dataIndex: 'options',
        key: 'options',
        title: '操作',
        slotName: 'options',
        width: 180,
        fixed: 'right',
      },
    ],
    dataSource: [],
    detailModal: [
      {
        key: 'code',
        label: '产品代码',
        editKey: 'code',
        value: 'text',
      },
      {
        key: 'product_name',
        label: '产品名称',
        value: 'text',
      },
      {
        key: 'inter_recommend_level',
        label: '内部推荐等级',
        editKey: 'inter_recommend_level',
        value: 'text',
      },
      // 2022.5.6 万元户
      {
        key: 'relatedInfo',
        label: '参加何种活动',
        editKey: 'relatedInfo',
        value: 'radio',
      },
      {
        key: 'sale_commission',
        label: '销售佣金',
        editKey: 'sale_commission',
        value: 'text',
      },
      {
        key: 'sale_words',
        label: '销售话术',
        editKey: 'sale_words',
        value: 'text',
      },
      {
        key: 'costody_rate',
        label: '托管费率',
        value: 'text',
      },
      {
        key: 'stop_loss_line',
        label: '止损线',
        value: 'text',
      },
      {
        key: 'invest_strategy',
        label: '投资策略',
        value: 'textarea',
      },
      {
        key: 'close_period',
        label: '封闭期',
        value: 'text',
      },
      {
        key: 'subsce_origin',
        label: '认购起点（元）',
        value: 'text',
      },
      {
        key: 'is_stru',
        label: '是否结构化',
        value: 'text',
      },
      {
        key: 'fund_manager_intro',
        label: '基金经理介绍',
        value: 'textarea',
      },
      {
        key: 'lowest_hold_day',
        label: '最低持有期',
        editKey: 'lowest_hold_day',
        value: 'text',
      },
      {
        key: 'open_freq',
        label: '开放频率',
        value: 'text',
      },
      {
        key: 'sales_rate',
        label: '销售服务费率',
        value: 'text',
      },
      {
        key: 'manage_rate',
        label: '管理费率',
        value: 'text',
      },
      {
        key: 'manager_org',
        label: '管理机构',
        value: 'text',
      },
      {
        key: 'sec_id',
        label: '证券id',
        value: 'text',
      },
      {
        key: 'add_amt',
        label: '追加金额（元）',
        value: 'text',
      },
      {
        key: 'purchase_rate',
        label: '申购费率',
        value: 'text',
      },
      {
        key: 'termination_clause',
        label: '终止条款',
        value: 'text',
      },
      {
        key: 'record_nbr',
        label: '备案编号',
        value: 'text',
      },
      {
        key: 'perfm_accrual_bench_mark',
        label: '业绩计提基准',
        value: 'textarea',
      },
      {
        key: 'valuation_method',
        label: '估值方法',
        value: 'text',
      },
      {
        key: 'product_type',
        label: '产品类型',
        value: 'text',
      },
      {
        key: 'subsce_rate',
        label: '认购费率',
        value: 'text',
      },
      {
        key: 'perfm_accrual_rate',
        label: '业绩报酬计提率',
        value: 'text',
      },
      {
        key: 'redeem_rate_explain',
        label: '赎回费率说明',
        value: 'textarea',
      },
      {
        key: 'investor_risk_level',
        label: '投资风险等级',
        value: 'text',
      },
      {
        key: 'fund_manager_name',
        label: '基金经理名称',
        value: 'text',
      },
      {
        key: 'subsce_end',
        label: '认购截止日期',
        value: 'text',
      },
      {
        key: 'product_estab',
        label: '产品成立日',
        value: 'text',
      },
      {
        key: 'continuation_cluase',
        label: '展期条款',
        value: 'text',
      },
      {
        key: 'purchase_origin',
        label: '申购起点',
        value: 'text',
      },
      {
        key: 'warn_line',
        label: '警告线',
        value: 'text',
      },
      {
        key: 'costody_org',
        label: '托管机构',
        value: 'text',
      },
    ],
    sortMap: {
      sale_commission: 1,
      subsce_origin: 2,
      manage_rate: 3,
    },
  },
  // 公募
  gm: {
    name: '公募产品',
    columns: [
      {
        dataIndex: 'id',
        key: 'id',
        title: '序号',
        width: 50,
      },
      {
        dataIndex: 'code',
        key: 'code',
        title: '基金代码',
        width: 70,
      },
      {
        dataIndex: 'product_name',
        key: 'product_name',
        title: '基金名称',
        width: 240,
      },
      {
        dataIndex: 'lowest_hold_day',
        key: 'lowest_hold_day',
        title: '最低持有期',
        slotName: 'lowest_hold_day',
        width: 100,
      },
      // 2022.5.6 是否参与万元户活动
      {
        dataIndex: 'relatedInfo',
        key: 'relatedInfo',
        title: '参与活动列表',
        slotName: 'relatedInfo',
        width: 200,
      },
      {
        dataIndex: 'inter_recommend_level',
        key: 'inter_recommend_level',
        title: '内部推荐等级',
        slotName: 'inter_recommend_level',
        width: 100,
      },
      {
        dataIndex: 'if_new',
        key: 'if_new',
        title: '是否新发',
        slotName: 'if_new',
        width: 70,
      },
      {
        dataIndex: 'fund_invest_type',
        key: 'fund_invest_type',
        title: '基金类型',
        slotName: 'fund_invest_type',
        width: 80,
      },
      {
        dataIndex: 'productRisk',
        key: 'productRisk',
        title: '风险等级',
        slotName: 'risk_level',
        width: 240,
      },
      // 2022.5.6 新增上架&修改时间
      {
        dataIndex: 'm_time',
        key: 'm_time',
        title: '上架&修改时间',
        slotName: 'update_time',
        width: 120,
      },
      {
        dataIndex: 'raise_period',
        key: 'raise_period',
        title: '募集期',
        slotName: 'raise_period',
        width: 150,
      },
      {
        dataIndex: 'perfm_compare_benchmark',
        key: 'perfm_compare_benchmark',
        title: '业绩比较基准',
        width: 300,
      },
      {
        dataIndex: 'sale_commission',
        key: 'sale_commission',
        title: '销售佣金',
        slotName: 'rate',
        width: 70,
      },
      {
        dataIndex: 'manage_fee',
        key: 'manage_fee',
        title: '管理费',
        slotName: 'rate',
        width: 70,
      },
      {
        dataIndex: 'custody_fee',
        key: 'custody_fee',
        title: '托管费',
        slotName: 'rate',
        width: 70,
      },
      {
        dataIndex: 'sales_serv_fee',
        key: 'sales_serv_fee',
        title: '销售服务费',
        slotName: 'rate',
        width: 120,
      },
      {
        dataIndex: 'fund_manager_list',
        key: 'fund_manager_list',
        title: '基金经理',
        width: 120,
      },
      {
        dataIndex: 'options',
        key: 'options',
        title: '操作',
        slotName: 'options',
        width: 180,
        fixed: 'right',
      },
    ],
    dataSource: [],
    detailModal: [
      {
        key: 'code',
        label: '基金代码',
        editKey: 'code',
        value: 'text',
      },
      {
        key: 'product_name',
        label: '基金名称',
        value: 'textarea',
      },
      {
        key: 'inter_recommend_level',
        label: '内部推荐等级',
        editKey: 'inter_recommend_level',
        value: 'text',
      },
      {
        key: 'sale_commission',
        label: '销售佣金',
        editKey: 'sale_commission',
        value: 'text',
      },
      // 2022.5.6 万元户
      {
        key: 'relatedInfo',
        label: '参加何种活动',
        editKey: 'relatedInfo',
        value: 'radio',
      },
      {
        key: 'if_new',
        label: '是否新发',
        value: 'text',
      },
      {
        key: 'fund_invest_type',
        label: '基金类型',
        value: 'text',
      },
      {
        key: 'issue_org_name',
        label: '所属公司',
        value: 'text',
      },
      {
        key: 'invest_phil',
        label: '投资理念',
        value: 'textarea',
      },
      {
        key: 'risk_profit_feature',
        label: '风险收益特征',
        value: 'text',
      },
      {
        key: '',
        label: '起购金额',
        value: 'text',
      },
      {
        key: 'lowest_hold_day',
        label: '最低持有期',
        editKey: 'lowest_hold_day',
        value: 'text',
      },
      {
        key: 'buy_rule',
        label: '买入规则',
        editKey: 'rule',
        value: 'table',
        columns: [
          {
            key: 'item',
            title: '阶段',
            map: {
              buy_commit: '买入提交',
              buy_confirm_share: '确认份额',
              select_income: '查询收益',
            },
          },
          {
            key: 'time',
            title: '时间',
          },
        ],
      },
      {
        key: 'buy_fee_rate',
        label: '买入费率',
        value: 'table',
        columns: [
          {
            key: 'buy_in_amount',
            title: '买入金额',
          },
          {
            key: 'fee_rate',
            title: '费率',
          },
          {
            key: 'discount_fee_rate',
            title: '优惠费率',
          },
        ],
      },
      {
        key: 'operate_fee_rate',
        label: '运作费用',
        value: 'table',
        columns: [
          {
            key: 'type',
            title: '费用类型',
            map: {
              manage_fee: '管理费',
              custody_fee: '托管费',
              sales_serv_fee: '销售服务费',
            },
          },
          {
            key: 'value',
            title: '费率/年',
          },
        ],
      },
      {
        key: 'sell_rule',
        label: '卖出规则',
        editKey: 'rule',
        value: 'table',
        columns: [
          {
            key: 'item',
            title: '阶段',
            map: {
              sell_commit: '卖出提交',
              sell_confirm_share: '确认份额',
              funds_arrive: '资金到账',
            },
          },
          {
            key: 'time',
            title: '时间',
          },
        ],
      },
      {
        key: 'sell_fee_rate',
        label: '卖出费率',
        value: 'table',
        columns: [
          {
            key: 'hold_time_limit',
            title: '持有期限',
          },
          {
            key: 'fee_rate',
            title: '费率',
          },
        ],
      },
    ],
    sortMap: {},
  },
};
/** 操作按钮 */
const OPTIONS_MAP = {
  scan: '查看详情',
  edit: '编辑',
  delete: '删除',
};
/** 角色权限控制 */
const ROLE_OPTIONS_MAP = {
  operator: ['scan', 'edit', 'delete'],
  saleman: ['scan'],
};
/** 筛选项 - 是否参与万元户活动 */
const JOIN_STANDARD = [
  {
    label: '全部',
    value: '0',
  },
  {
    label: '参加万元户活动',
    value: '1',
  },
  {
    label: '不参加万元户活动',
    value: '2',
  }
]
const PAGE_SIZE = 20;
const PAGE_SIZE_LIST = ['10', '20', '50', '100'];

const PARAMS = {
  TAB_MAP,
  OPTIONS_MAP,
  DELETE_MAP,
  ROLE_OPTIONS_MAP,
  RISK_LEVEL,
  TRADE_STATUS,
  FUND_TYPE,
  JOIN_STANDARD,
  PAGE_SIZE,
  PAGE_SIZE_LIST,
};

export default PARAMS;
