<template>
  <section class="m-packet-activities">
    <p class="m-page-title">
      活动项目{{ pageType === 'edit' ? '列表' : '审核' }}
    </p>
    <section class="m-activities-table table-wrapper">
      <div class="filter-wrapper">
        <a-tree-select
          v-model:value="departmentId"
          allow-clear
          class="m-project-name-long"
          placeholder="请输入部门名称"
          :tree-data="departmentSelectList"
          :show-search="true"
          tree-checkable
          multiple
          :max-tag-count="2"
          :tree-check-strictly="false"
          :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
          :filter-tree-node="filterTreeNode"
          @change="handleSearch"
        ></a-tree-select>
        <a-select
          v-model:value="projectId"
          allow-clear
          class="m-project-name"
          placeholder="请输入项目名称"
          show-search
          :options="projectSelectListForDropdown"
          :filter-option="filterOption"
          @change="handleSearch"
        ></a-select>
        <!-- 权限控制 -->
        <a-button
          v-show="pageType === 'edit'"
          class="m-add-btn"
          type="primary"
          @click="handleProjectModal('add')"
        >
          新增项目
        </a-button>
        <div>
          <span class="m-header-item">
            总申请金额：
            <i>{{ Number(totalAmount).toFixed(2) }}</i>
          </span>
          <span class="m-header-item">
            累计已使用金额：
            <i>{{ Number(usedAmount).toFixed(2) }}</i>
          </span>
          <span class="m-header-item">
            余额：
            <i>{{ Number(balance).toFixed(2) }}</i>
          </span>
          <a-button class="m-header-item" @click="handleAmountDetailModal">
            各业务部门资金明细
          </a-button>
        </div>
      </div>
      <div class="m-table-content">
        <custom-table
          v-model:page-num="pageNum"
          v-model:page-size="pageSize"
          :table-total="total"
          :table-data="tableData"
          :scroll="{ scrollToFirstRowOnChange: true, x: 1600, y: 600 }"
          :columns="PROJECT_COLUMNS"
          :page-size-list="['5', '10', '20', '50']"
          :row-class-name="rowClassName()"
          :on-submit="onTableSubmit"
        >
          <!-- 余额 -->
          <template #surplus_amount="{ text }">
            <span :style="{ color: Number(text) < 10000 ? 'red' : '' }">
              {{ text }}
            </span>
          </template>
          <!-- 其余活动明细 -->
          <template #otherDetail="{ index, column }">
            <a-button
              type="link"
              @click="handleOtherDetailModal(column.key, index)"
            >
              查看明细
            </a-button>
          </template>
          <!-- 明细跳转 -->
          <template #detail="{ record, column }">
            <a-button type="link" @click="handleDetail(record, column)">
              查看明细
            </a-button>
          </template>
          <!-- 空值默认为0 -->
          <template #defaultNum="{ text }">
            {{ text || 0 }}
          </template>
          <!-- 状态 - 待审核资金数 -->
          <template #audit_count="{ text }">
            <span v-if="text > 0">
              有
              <span style="color: red">{{ Number(text || '') }}</span>
              笔资金待审核
            </span>
            <span v-else>暂无审核</span>
          </template>
          <!-- 权限控制 -->
          <template #options="{ index, record }">
            <div>
              <a-button type="link" @click="handleApplyModal(index)">
                {{ pageType === 'edit' ? '资金变更/' : '' }}申请明细
              </a-button>
            </div>
            <div v-show="pageType === 'verify'">
              <a-button
                v-show="record.audit_count > 0"
                type="link"
                @click="handleApplyModal(index)"
              >
                去审核
              </a-button>
            </div>
            <div>
              <!-- 若第一笔资金审核通过不显示 -->
              <a-button
                v-show="pageType === 'edit'"
                type="link"
                @click="handleProjectModal('edit', index)"
              >
                修改
              </a-button>
            </div>
          </template>
        </custom-table>
      </div>
    </section>
    <section class="m-activities-modal">
      <!-- 活动明细列表弹窗 -->
      <a-modal
        v-model:visible="otherDetailVisible"
        :title="OTHER_DETAIL_TITLE[otherDetailType]"
        wrap-class-name="full-modal"
        width="96%"
        :footer="null"
        style="top: 20px"
      >
        <!-- <activity-detail
          v-if="otherDetailType === 'activity'"
          :project-id="tableIndex > -1 ? tableData[tableIndex].item_id : 0"
        /> -->
        <portion-detail
          v-if="otherDetailType === 'portion'"
          :project-id="tableIndex > -1 ? tableData[tableIndex].item_id : 0"
        />
      </a-modal>
      <!-- 申请明细列表弹窗 -->
      <a-modal
        v-model:visible="applyVisible"
        title="申请明细"
        wrap-class-name="full-modal"
        width="96%"
        :footer="null"
        style="top: 20px"
      >
        <div class="m-apply-modal">
          <span class="m-header-item">
            累计申请总金额：
            <i>{{ Number(projectTotalAmount).toFixed(2) }}</i>
          </span>
          <!-- 权限控制 -->
          <a-button
            v-show="pageType === 'edit'"
            type="primary"
            style="margin-right: 20px"
            @click="handleBudgetModal('budget_add')"
          >
            资金变更
          </a-button>
          <a-button
            v-show="pageType === 'edit'"
            type="primary"
            @click="handleBudgetMoveModal('budget_move_add')"
          >
            迁移资金
          </a-button>
          <custom-table
            :table-total="applyTotal"
            :table-data="applyTableData"
            :scroll="{ scrollToFirstRowOnChange: true, x: 1500, y: 600 }"
            :columns="APPLY_COLUMNS"
            :page-size-list="['5', '10', '20', '50']"
            :row-class-name="rowClassName()"
            :on-submit="onApplyTableSubmit"
          >
            <!-- 到账日期 -->
            <template #date="{ text }">
              {{ dealYYYYMMDD(text || '') }}
            </template>
            <!-- 审核状态 -->
            <template #audit_status="{ text }">
              <span>{{ BUDGET_STATUS[text] }}</span>
            </template>
            <!-- 权限控制 -->
            <template #options="{ index, record }">
              <a-button
                v-show="record.audit_status === 0 && pageType === 'verify'"
                type="link"
                @click="handleConfirmVerify(index)"
              >
                审核通过
              </a-button>
              <a-button
                v-show="record.audit_status === 0 && pageType === 'verify'"
                type="link"
                @click="handleConfirmRefuse(index)"
              >
                打回
              </a-button>
              <a-button
                v-show="record.audit_status !== 1 && pageType === 'edit'"
                type="link"
                @click="
                  record.target_id
                    ? handleBudgetMoveModal('budget_move_edit', index)
                    : handleBudgetModal('budget_edit', index)
                "
              >
                修改
              </a-button>
              <a-button
                type="link"
                @click="
                  record.target_id
                    ? handleBudgetMoveModal('budget_move_scan', index)
                    : handleBudgetModal('budget_scan', index)
                "
              >
                查看
              </a-button>
            </template>
          </custom-table>
        </div>
        <!-- 查看项目相关凭证弹窗 -->
        <a-modal
          v-model:visible="fileVisible2"
          title="已上传凭证"
          width="60%"
          mode="full-modal"
          style="top: 20px"
          :footer="null"
        >
          <div class="m-file-modal">
            <img-view
              v-for="(img, index) in [...fileImgs, ...uploadImgs]"
              :key="index"
              :src="img"
              width="56vw"
            />
          </div>
        </a-modal>
      </a-modal>
      <!-- 新增/修改活动项目、新增/修改/查看申请明细弹窗 -->
      <a-modal
        v-for="key in ['addVisible', 'addBudgetVisible']"
        :key="key"
        v-model:visible="addPop[key]"
        :title="ADD_MODAL_TITLE[addType]"
        :confirm-loading="loading"
        @ok="handleConfirmAdd"
      >
        <div class="m-add-modal">
          <!-- 项目名称/项目ID仅新增/修改时有，申请资金无 -->
          <!-- TODO  need add 申请部门申请人员 -->
          <a-descriptions :column="1">
            <a-descriptions-item
              v-for="item in ADD_FORM.slice(ifProject() ? 0 : 2)"
              :key="item.key"
              :label="item.label"
            >
              <a-input
                v-if="item.type === 'input'"
                v-model:value="addFormData[item.key]"
                :placeholder="item.placeholder"
                :disabled="item.disabled || editDisabled(item.key)"
                @change="checkAddFormItem(item.key)"
              />
              <a-date-picker
                v-else-if="item.type === 'date'"
                v-model:value="addFormData[item.key]"
                :disabled="item.disabled || editDisabled(item.key)"
              />
              <a-textarea
                v-else-if="item.type === 'textarea'"
                v-model:value="addFormData[item.key]"
                :placeholder="item.placeholder"
                :disabled="item.disabled || editDisabled(item.key)"
                :rows="4"
                @change="checkAddFormItem(item.key)"
              />
            </a-descriptions-item>
            <a-descriptions-item label="相关凭证">
              <a-upload
                v-model:file-list="addFormData.voucherImg"
                :before-upload="handleFileUpload"
                :disabled="editDisabled('voucherImg')"
              >
                <a-button :disabled="editDisabled('voucherImg')">
                  <upload-outlined></upload-outlined>
                  点击上传
                </a-button>
              </a-upload>
              <a-button
                v-show="uploadImgs.length + fileImgs.length > 0"
                type="link"
                @click="handleFileModal"
              >
                查看已上传的凭证
              </a-button>
            </a-descriptions-item>

            <a-descriptions-item
              v-for="item in ifProject() ? ADD_FORM_WITH_SI : []"
              :key="item.key"
              :label="item.label"
            >
              <template v-if="item.type === 'input'">
                <a-input
                  v-model:value="addFormData[item.key]"
                  :placeholder="item.placeholder"
                  :disabled="item.disabled"
                  @change="checkAddFormItem(item.key)"
                />
              </template>
              <template v-else-if="item.type === 'depId'">
                <a-tree-select
                  v-model:value="addFormData[item.key]"
                  allow-clear
                  style="width: 100%"
                  :tree-data="departmentSelectList"
                  :show-search="true"
                  :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
                  :filter-tree-node="filterTreeNode"
                  :placeholder="item.placeholder || '请选择部门'"
                  :disabled="!ifAdd()"
                  @change="handleDepartmentChange"
                ></a-tree-select>
              </template>
              <template v-else-if="item.type === 'warningMember'">
                <a-select
                  v-model:value="warningMemberTags"
                  mode="tags"
                  style="width: 100%"
                  :placeholder="item.placeholder"
                  @change="handleWarningMemberChange"
                ></a-select>
              </template>
            </a-descriptions-item>
            <!-- <a-descriptions-item  v-if="ifBudgetAdd()" label="申请人员">
              <a-input v-model:value="addFormData.siName" />
            </a-descriptions-item> -->
          </a-descriptions>
        </div>
        <!-- 查看项目相关凭证弹窗 仅操作项目时 -->
        <a-modal
          v-model:visible="fileVisible1"
          title="已上传凭证"
          width="60%"
          mode="full-modal"
          style="top: 20px"
          :footer="null"
        >
          <div class="m-file-modal">
            <img-view
              v-for="(img, index) in [...fileImgs, ...uploadImgs]"
              :key="index"
              :src="img"
              width="56vw"
            />
          </div>
        </a-modal>
      </a-modal>
      <!-- 资金迁移弹窗 -->
      <a-modal
        v-model:visible="moveVisible"
        :title="ADD_MODAL_TITLE['budget_move']"
        :confirm-loading="loading"
        @ok="handleConfirmMove"
      >
        <div class="m-move-modal">
          <a-descriptions :column="1">
            <a-descriptions-item label="原始项目">
              <div>{{ nowProject().item_name }}</div>
            </a-descriptions-item>
            <a-descriptions-item label="目标项目">
              <a-select
                v-model:value="moveFormData.targetId"
                style="width: 100%"
              >
                <template
                  v-for="item in projectSelectListForDropdown"
                  :key="item"
                >
                  <a-select-option :value="item.value">
                    {{ item.label }}
                  </a-select-option>
                </template>
              </a-select>
            </a-descriptions-item>
            <a-descriptions-item label="迁移资金（元）">
              <a-input v-model:value="moveFormData.applyAmount" />
            </a-descriptions-item>
            <a-descriptions-item label="备注">
              <a-input v-model:value="moveFormData.remark" />
            </a-descriptions-item>
            <a-descriptions-item label="相关凭证">
              <a-upload
                v-model:file-list="moveFormData.voucherImg"
                :before-upload="handleMoveFileUpload"
              >
                <a-button>
                  <upload-outlined></upload-outlined>
                  点击上传
                </a-button>
              </a-upload>
              <a-button
                v-show="uploadImgs.length + fileImgs.length > 0"
                type="link"
                @click="handleFileModal3"
              >
                查看已上传的凭证
              </a-button>
            </a-descriptions-item>
            <a-descriptions-item label="申请人员">
              <a-input v-model:value="moveFormData.siName" disabled />
            </a-descriptions-item>
          </a-descriptions>
        </div>
        <!-- 查看项目相关凭证弹窗 仅操作项目时 -->
        <a-modal
          v-model:visible="fileVisible3"
          title="已上传凭证"
          width="60%"
          mode="full-modal"
          style="top: 20px"
          :footer="null"
        >
          <div class="m-file-modal">
            <img-view
              v-for="(img, index) in [...fileImgs, ...uploadImgs]"
              :key="index"
              :src="img"
              width="56vw"
            />
          </div>
        </a-modal>
      </a-modal>
      <!-- 明细页弹窗 -->
      <a-modal
        v-model:visible="detailVisible"
        :title="DETAIL_MODAL_CONFIG[detailType].title"
        wrap-class-name="full-modal"
        width="96%"
        :footer="null"
        style="top: 20px"
      >
        <receive-detail
          v-if="detailType === 'receive'"
          component-type="component"
          :calc-type="1"
          :calc-id="detailId"
        />
        <use-detail
          v-if="detailType === 'use'"
          component-type="component"
          :calc-type="1"
          :calc-id="detailId"
        />
        <red-detail
          v-if="detailType === 'red'"
          component-type="component"
          :calc-type="1"
          :calc-id="detailId"
        />
      </a-modal>
      <!-- 各业务部门资金明细弹窗 -->
      <a-modal
        v-model:visible="amountDetailVisible"
        title="各业务部门资金明细"
        width="800px"
        :footer="null"
      >
        <a-table
          :data-source="amountDetailList"
          :pagination="false"
          :scroll="{ y: 400 }"
        >
          <a-table-column
            key="dep_name"
            title="部门名称"
            data-index="dep_name"
          />
          <a-table-column
            key="apply_amount_total"
            title="总申请金额"
            data-index="apply_amount_total"
          >
            <template #default="{ text }">
              {{ Number(text).toFixed(2) }}
            </template>
          </a-table-column>
          <a-table-column
            key="used_amount_total"
            title="累计已使用金额"
            data-index="used_amount_total"
          >
            <template #default="{ text }">
              {{ Number(text).toFixed(2) }}
            </template>
          </a-table-column>
          <a-table-column
            key="surplus_amount"
            title="余额"
            data-index="surplus_amount"
          >
            <template #default="{ text }">
              <span>
                {{ Number(text).toFixed(2) }}
              </span>
            </template>
          </a-table-column>

          <template #summary>
            <a-table-summary-row>
              <a-table-summary-cell :index="0">总计</a-table-summary-cell>
              <a-table-summary-cell :index="1">
                {{ Number(totalAmount).toFixed(2) }}
              </a-table-summary-cell>
              <a-table-summary-cell :index="2">
                {{ Number(usedAmount).toFixed(2) }}
              </a-table-summary-cell>
              <a-table-summary-cell :index="3">
                <span>
                  {{ Number(balance).toFixed(2) }}
                </span>
              </a-table-summary-cell>
            </a-table-summary-row>
          </template>
        </a-table>
      </a-modal>
    </section>
  </section>
</template>

<script lang="ts">
import {
  defineComponent,
  nextTick,
  onMounted,
  reactive,
  toRefs,
  watch,
  ref,
} from 'vue';
import ajax from '@/server/http';
import dayjs from 'dayjs';
dayjs.locale('zh-cn');
import { useRouter } from 'vue-router';
// components
import CustomTable, {
  useCustomTable,
} from '../../../components/common/CustomTable.vue';
import ImgView from '@/components/common/ImgView.vue';
import { UploadOutlined } from '@ant-design/icons-vue';
import ActivityDetail from '../details/ActivityDetail.vue';
import PortionDetail from '../details/PortionDetail.vue';
import ReceiveDetail from '../details/ReceiveDetail.vue';
import UseDetail from '../details/UseDetail.vue';
import RedDetail from '../details/RedDetail.vue';
// hook
import useDetail from '@/components/packet/detailHook';
// const
import {
  ACTIVITIES_PARAMS,
  DETAIL_MODAL_CONFIG,
  DETAIL_DIMENSION,
} from '../const';
// type
import { iState, projectTableData, budgetTableData } from './type';
import { debounce } from 'lodash';
import { message } from 'ant-design-vue';
window['message'] = message;
// fn
import { dealYYYYMMDD, imgToBase64 } from '@/utils/fn';
import { initTable } from '../fn';
import { computed } from 'vue';
import { useStore } from 'vuex';

let Init = false; // 首次初始化

export default defineComponent({
  name: 'ActivityList',
  components: {
    CustomTable,
    ImgView,
    UploadOutlined,
    ActivityDetail,
    PortionDetail,
    ReceiveDetail,
    UseDetail,
    RedDetail,
  },
  setup() {
    const router = useRouter();
    const store = useStore();
    const { table, onTableSubmit, onTableChange, rowClassName } =
      useCustomTable();
    const userInfo = computed(() => store.state.userInfo);
    const applyTable = useCustomTable();
    const { detail, jumpDetail } = useDetail();
    // 告警人员标签数组
    const warningMemberTags = ref<string[]>([]);
    const state = reactive<iState>({
      // user
      pageType: ~location.hash.indexOf('packet/activities') ? 'edit' : 'verify',
      // header
      totalAmount: 0,
      usedAmount: 0,
      balance: 0,
      projectTotalAmount: 0,
      // filters
      projectId: null, // 项目Id
      departmentId: [] as string[], // 部门Id
      // table
      total: 0,
      tableData: [],
      tableIndex: -1,
      applyTotal: 0,
      applyTableData: [],
      applyTableIndex: -1,
      // modal
      loading: false,
      otherDetailVisible: false,
      otherDetailType: '',
      applyVisible: false,
      addPop: {
        addVisible: false,
        addBudgetVisible: false,
      },
      addType: '',
      addFormData: {
        siName: userInfo.value.userName,
      },
      addFormCheck: {
        itemName: true,
        arrivalDate: true,
        paySerial: true,
        applyAmount: true,
      },
      fileVisible1: false,
      fileVisible2: false,
      fileVisible3: false,
      moveVisible: false,
      moveFormData: {},
      // 图片显示列表，file转base64
      fileImgs: [],
      uploadImgs: [],
      projectSelectListForDropdown: [], // 项目下拉列表
      departmentSelectList: [], // 部门下拉列表
      amountDetailList: [], // 部门资金明细列表
      amountDetailVisible: false, // 新增部门资金明细弹窗显示状态
    });
    // methods
    /** 初始化 */
    const init = () => {
      if (!Init) {
        state.pageType = ~location.hash.indexOf('packet/activities')
          ? 'edit'
          : 'verify';
        Init = true;
      }
      onTableChange(fetchTable);
      fetchTotalAmount();
      fetchProjectListForDropdown();
      fetchDepartmentListForDropdown();
    };
    /** 清空表格 */
    const clear = initTable(state);
    /** 清空申请表格 */
    const clearApplyTable = () => {
      state.applyTotal = 0;
      state.applyTableData = [];
      state.projectTotalAmount = 0;
    };
    /** 查询表格 */
    const fetchTable = () => {
      ajax(
        'getProjectTableData',
        {
          pro_id_list: state.projectId ? [state.projectId] : null,
          dep_id_list:
            state.departmentId && state.departmentId.length > 0
              ? state.departmentId
              : null,
        },
        {
          pageNum: table.pageNum,
          pageSize: table.pageSize,
        }
      )
        .then((res: any) => {
          try {
            if (res) {
              state.total = res.total;
              state.tableData = res.crmRecord;
            }
          } catch (e) {
            clear();
            console.log('查询项目表格 error', e);
          }
        })
        .catch(() => {
          clear();
        });
    };
    /** 查询申请列表 */
    const fetchBudgetTable = () => {
      clearApplyTable();
      ajax('getBudgetTableData', {
        id: nowProject().item_id,
      })
        .then((res: any) => {
          try {
            if (res) {
              state.applyTotal = res.total;
              state.applyTableData = res.crmRecord;
              state.projectTotalAmount = res.expand.apply_amount_count;
            } else {
              clearApplyTable();
            }
          } catch (e) {
            console.log('查询申请资金列表', e);
          }
        })
        .catch(() => {
          clearApplyTable();
        });
    };
    /** 查询项目资金细节 */
    const fetchBudgetDetail = () => {
      const projectId = nowProject().item_id.toString();
      const id = ifProject() ? projectId : nowBudget().item_id;
      return ajax('getBudgetDetail', { id }).then((res: any) => {
        try {
          if (res) {
            state.addFormData = {
              itemName: res.item_name,
              itemId: projectId,
              arrivalDate: dayjs(dealYYYYMMDD(res.arrival_date)),
              paySerial: res.pay_serial,
              applyAmount: res.apply_amount,
              siDepartment: res.si_department,
              siName: res.si_name,
              siDepartmentAdmin: res.si_department_admin,
              remark: res.remark,
              depId: res.dep_id,
              reserveRatio: res.reserve_ratio,
              warningRatio: res.warning_ratio,
              warningMember: res.warning_member,
              toleranceRatio: res.tolerance_ratio,
              voucherImg: [],
            };
            state.fileImgs = res.voucher_img.map(
              img => 'data:image/png;base64,' + img
            );

            // 初始化告警人员标签
            initWarningMemberTags();
          }
        } catch (e) {
          console.log('查询项目资金细节 error');
        }
      });
    };
    /** 查询项目迁移资金细节 */
    const fetchBudgetMoveDetail = () => {
      const projectId = nowProject().item_id.toString();
      const id = ifProject() ? projectId : nowBudget().item_id;
      return ajax('getBudgetDetail', { id }).then((res: any) => {
        try {
          if (res) {
            state.moveFormData = {
              applyAmount: res.apply_amount,
              targetId: res.target_id,
              voucherImg: [],
              remark: res.remark,
              siName: res.si_name,
            };
            state.fileImgs = res.voucher_img.map(
              img => 'data:image/png;base64,' + img
            );
          }
        } catch (e) {
          console.log('查询项目迁移资金细节 error');
        }
      });
    };
    /** 获取所有项目金额 */
    const fetchTotalAmount = () => {
      ajax('getTotalAmount').then((res: any) => {
        try {
          if (res) {
            state.totalAmount = res.total?.apply_amount_total;
            state.usedAmount = res.total?.used_amount_total;
            state.balance = res.total?.surplus_amount;
            state.amountDetailList = res.detail || [];
          }
        } catch (e) {
          console.log('获取项目金额 error', e);
        }
      });
    };
    /** 审核资金 */
    const postVerify = (type: '1' | '2') => {
      const data: any = {
        id: nowBudget().item_id,
        audit_type: type,
      };
      // if (type === '2') {
      //   data.fail_reason = state.refuse;
      // }
      ajax('postBudgetVerify', data).then(() => {
        init();
        applyTable.onTableChange(fetchBudgetTable);
        nextTick(() => message.success('操作成功！'));
      });
    };
    /** 新增/编辑 项目、资金 */
    const postBudget = () => {
      const project: projectTableData = nowProject();
      const budget: budgetTableData = nowBudget();
      const {
        itemName,
        arrivalDate,
        paySerial,
        applyAmount,
        voucherImg,
        siDepartment,
        siDepartmentAdmin,
        siName,
        remark,
        reserveRatio,
        warningRatio,
        warningMember,
        toleranceRatio,
        depId,
      } = state.addFormData;
      const form = new FormData();
      form.append(
        'arrivalDate',
        arrivalDate.format('YYYY-MM-DD').replace(/-/g, '')
      );
      form.append('paySerial', paySerial || '');
      form.append('applyAmount', applyAmount);
      (voucherImg || []).forEach(file => {
        form.append('voucherImg', file.originFileObj);
      });
      // 如果是新增/修改 项目，添加项目名称
      if (ifProject()) {
        form.append('itemName', itemName);
        form.append('siDepartment', siDepartment);
        form.append('siDepartmentAdmin', siDepartmentAdmin);
        form.append('reserveRatio', reserveRatio);
        form.append('warningRatio', warningRatio);
        form.append('warningMember', warningMember);
        form.append('toleranceRatio', toleranceRatio);
        form.append('depId', depId);
      }

      form.append('siName', siName);
      form.append('remark', remark || '');

      // 如果是修改 项目/资金，添加项目/资金id
      if (!ifAdd()) {
        form.append(
          'itemId',
          (state.addType === 'edit'
            ? project.item_id
            : budget.item_id
          ).toString()
        );
      }
      // 如果是新增资金，添加项目id
      if (state.addType === 'budget_add') {
        form.append('parentId', project.item_id.toString());
      }
      ajax(
        'editProject',
        form,
        {},
        {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        }
      ).then((res: any) => {
        if (res) {
          init();
          nextTick(() => message.success('操作成功！'));
          if (!ifProject()) {
            applyTable.onTableChange(fetchBudgetTable);
          }
        } else message.error('操作失败');
      });
    };
    /** 校验表单项 */
    const checkAddFormItem = debounce((type: string) => {
      let reg = /.+/;
      switch (type) {
        case 'applyAmount': {
          reg = /^(-)?(0|[1-9][0-9]*)(\.[0-9]+)?$/;
          break;
        }
        default:
          break;
      }
      if (!reg.test(state.addFormData[type])) {
        message.error('请输入正确的格式！');
        state.addFormCheck[type] = false;
      } else {
        state.addFormCheck[type] = true;
      }
    }, 500);
    /** 当前操作项目 */
    const nowProject = () => {
      return state.tableData[state.tableIndex];
    };
    /** 当前操作资金 */
    const nowBudget = () => {
      return state.applyTableData[state.applyTableIndex];
    };
    /** 是否是项目操作 */
    const ifProject = () => {
      return !~state.addType.indexOf('budget');
    };
    /** 是否是新增 */
    const ifAdd = () => {
      return ~state.addType.indexOf('add');
    };
    const ifBudgetAdd = () => {
      return !~state.addType.indexOf('budget_add');
    };
    /** 校验提交表单 */
    const checkAddForm = () => {
      let check = true;
      const keys = ACTIVITIES_PARAMS.DEFAULT_ADD_KEYS.slice(
        ifProject() ? 0 : 1
      );
      const needValidateKeyArray = [
        'reserveRatio',
        'warningRatio',
        'toleranceRatio',
      ];
      needValidateKeyArray.forEach(nKey => {
        if (
          !isNaN(state.addFormData[nKey]) &&
          parseFloat(state.addFormData[nKey]) >= 0 &&
          parseFloat(state.addFormData[nKey]) <= 1
        ) {
          // donothing
        } else {
          if (ifProject()) {
            check = false;
            message.error('请按正确格式填写！请检查比例是否在0到1之间');
          }
        }
      });
      // 非空校验
      keys.forEach(key => {
        if (
          key === 'paySerial' &&
          !ifProject() &&
          Number(state.addFormData.applyAmount) < 0
        )
          return;
        if (!state.addFormCheck[key] || !state.addFormData[key]) {
          check = false;
          message.error('请按正确格式填写！');
        }
      });
      return check;
    };
    /** 判断修改diabled */
    const editDisabled = (key: string) => {
      // 是否是查看申请明细，均不可编辑
      if (state.addType === 'budget_scan') return true;
      if (key === 'siName') return true;
      // 判断是否是新增，新增均可编辑（除项目id）
      if (ifAdd()) return false;
      // 判断审核状态，若审核通过则只有项目名称可编辑
      const status =
        (state.addType === 'edit' ? nowProject() : nowBudget()).audit_status ===
        1;
      return status && key !== 'itemName';
    };
    /** 读取正在上传图片 */
    const changeUploadImgs = (type: 'add' | 'move') => {
      state.uploadImgs = [];
      if (type === 'add') {
        state.addFormData.voucherImg.forEach(img => {
          imgToBase64(img.originFileObj, base64 => {
            state.uploadImgs.push(base64 as string);
          });
        });
      } else {
        state.moveFormData.voucherImg.forEach(img => {
          imgToBase64(img.originFileObj, base64 => {
            state.uploadImgs.push(base64 as string);
          });
        });
      }
    };
    // controller
    /** 搜索表格 */
    const handleSearch = () => {
      onTableChange(fetchTable);
    };
    /** 项目其他明细 */
    const handleOtherDetailModal = (type: string, index: number) => {
      const _type = type.replace('Detail', '') as 'portion';
      state.tableIndex = index;
      state.otherDetailVisible = true;
      state.otherDetailType = _type;
    };
    /** 各明细页跳转 */
    const handleDetail = (record: projectTableData, col: any) => {
      jumpDetail(col.dataIndex.replace('Detail', ''), 1, record.item_id);
    };
    /** 新增/修改项目 */
    const handleProjectModal = (type: 'add' | 'edit', index?: number) => {
      state.addType = type;
      // 修改需更改列表操作索引
      if (type === 'edit') {
        state.tableIndex = index;
        // 添加表单内容
        fetchBudgetDetail();
      } else {
        // 新增项目时重置告警人员标签
        warningMemberTags.value = [];
      }
      state.addPop.addVisible = true;
    };
    /** 新增/修改/查看资金申请明细 */
    const handleBudgetModal = (
      type: 'budget_add' | 'budget_edit' | 'budget_scan',
      index?: number
    ) => {
      state.addType = type;
      // 修改/查看需更改申请操作索引
      if (type !== 'budget_add') {
        state.applyTableIndex = index;
        // 添加表单内容
        fetchBudgetDetail();
      } else {
        // 新增时初始化表单
        state.addFormData = {
          siName: userInfo.value.userName, // 申请人名称取当前用户名称
        };
      }
      // 查看直接打开凭证弹窗
      if (type === 'budget_scan') {
        state.fileVisible2 = true;
      } else {
        state.addPop.addBudgetVisible = true;
      }
    };
    /** 新增/修改/查看资金迁移 */
    const handleBudgetMoveModal = (
      type: 'budget_move_add' | 'budget_move_edit' | 'budget_move_scan',
      index?: number
    ) => {
      state.addType = type;
      // 修改/查看需更改申请操作索引
      if (type !== 'budget_move_add') {
        state.applyTableIndex = index;
        fetchBudgetMoveDetail();
      } else {
        state.moveFormData = {
          siName: userInfo.value.userName, // 申请人名称取当前用户名称
        };
      }
      // 查看直接打开凭证弹窗
      if (type === 'budget_move_scan') {
        state.fileVisible2 = true;
      } else {
        state.moveVisible = true;
      }
    };
    /** 请求项目列表下拉列表 */
    const fetchProjectListForDropdown = async () => {
      const res = await ajax('getProjectListForDropdown');
      if (res) {
        state.projectSelectListForDropdown = res.map(item => ({
          label: item.select_name,
          value: item.select_id,
        }));
      }
    };
    /** 请求部门下拉列表 */
    const fetchDepartmentListForDropdown = async () => {
      const res = await ajax('getDepartmentListForDropdown');
      if (res) {
        // 处理树形结构数据
        state.departmentSelectList = transformDepartmentData(res);
      }
    };
    /** 将部门数据转换为树形结构 */
    const transformDepartmentData = data => {
      if (!data) return [];

      // 递归处理数据
      const transformNode = node => {
        const result: {
          value: number;
          title: string;
          key: number;
          children?: any[];
        } = {
          value: node.group_id,
          title: node.group_name,
          key: node.group_id,
          children: [],
        };

        // 如果有子部门，递归处理
        if (node.sub_group_list && node.sub_group_list.length > 0) {
          result.children = node.sub_group_list.map(child =>
            transformNode(child)
          );
        }

        return result;
      };

      // 处理数据
      // 如果是数组则map处理，如果是单个对象则直接处理
      if (Array.isArray(data)) {
        return data.map(item => transformNode(item));
      } else {
        // 如果是单个对象，直接处理
        return [transformNode(data)];
      }
    };

    /** 显示申请列表弹窗 */
    const handleApplyModal = (index: number) => {
      state.tableIndex = index;
      state.applyVisible = true;
      fetchBudgetTable();
    };
    /** 迁移资金弹窗表单校验 */
    const checkMoveForm = () => {
      let result = true;
      const project: projectTableData = nowProject();
      const { applyAmount, targetId } = state.moveFormData;
      switch (true) {
        case !targetId:
          message.error('请选择目标项目');
          result = false;
          break;
        case Number(targetId) === project.item_id:
          message.error('目标项目不能与原始项目相同');
          result = false;
          break;
        case Number(applyAmount) > Number(project.surplus_amount):
          message.error('迁移资金总额不能大于原始项余额');
          result = false;
          break;
        case !(Number(applyAmount) > 0):
          message.error('迁移资金总额需填写正数');
          result = false;
          break;
        default:
          break;
      }
      return result;
    };
    /** 确认新增/修改 项目/资金 */
    const handleConfirmAdd = () => {
      if (state.addType === 'budget_scan') {
        // 如果是查看，关闭弹窗
        state.addPop.addBudgetVisible = false;
      } else if (checkAddForm()) {
        postBudget();
        state.addPop[ifProject() ? 'addVisible' : 'addBudgetVisible'] = false;
      }
    };

    /** 确认新增/修改 迁移资金 */
    const handleConfirmMove = () => {
      if (checkMoveForm()) {
        const project: projectTableData = nowProject();
        const budget: budgetTableData = nowBudget();
        const { applyAmount, targetId, voucherImg, remark, siName } =
          state.moveFormData;
        const form = new FormData();
        form.append('itemId', budget?.item_id?.toString() || '');
        form.append('parentId', project?.item_id?.toString() || '');
        form.append('applyAmount', applyAmount);
        form.append('targetId', targetId);
        form.append('remark', remark || '');
        form.append('siName', siName || '');
        (voucherImg || []).forEach(file => {
          form.append('voucherImg', file.originFileObj);
        });
        ajax(
          'editProject',
          form,
          {},
          {
            headers: {
              'Content-Type': 'multipart/form-data',
            },
          }
        ).then((res: any) => {
          if (res) {
            state.moveVisible = false;
            init();
            nextTick(() => message.success('操作成功！'));
            if (!ifProject()) {
              applyTable.onTableChange(fetchBudgetTable);
            }
          } else message.error('操作失败');
        });
      }
    };
    /** 审核通过 */
    const handleConfirmVerify = (index: number) => {
      state.applyTableIndex = index;
      postVerify('1');
    };
    /** 审核打回 */
    const handleConfirmRefuse = (index: number) => {
      state.applyTableIndex = index;
      postVerify('2');
      state.addPop.addBudgetVisible = false;
    };
    /** 上传文件 */
    const handleFileUpload = (file: File, filelist: File[]) => {
      const reg = /^.+\.(png|jpg|jpeg|bmp|gif)$/;
      if (!reg.test(file.name.toLowerCase())) {
        message.error('请上传图片文件！');
        setTimeout(() => {
          state.addFormData.voucherImg = filelist.slice(0, -1);
        });
      }
      return false;
    };
    /** 上传资金迁移凭证文件 */
    const handleMoveFileUpload = (file: File, filelist: File[]) => {
      const reg = /^.+\.(png|jpg|jpeg|bmp|gif)$/;
      if (!reg.test(file.name.toLowerCase())) {
        message.error('请上传图片文件！');
        setTimeout(() => {
          state.moveFormData.voucherImg = filelist.slice(0, -1);
        });
      }
      return false;
    };
    /** 显示已上传凭证弹窗 */
    const handleFileModal = () => {
      state.fileVisible1 = true;
    };
    /** 显示已上传凭证弹窗 */
    const handleFileModal3 = () => {
      state.fileVisible3 = true;
    };
    /** 显示部门资金明细弹窗 */
    const handleAmountDetailModal = () => {
      state.amountDetailVisible = true;
    };
    /** 过滤下拉项 */
    const filterOption = (input, option) => {
      return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
    };

    /** 过滤树节点 */
    const filterTreeNode = (inputValue, treeNode) => {
      return (
        treeNode.title.toLowerCase().indexOf(inputValue.toLowerCase()) >= 0
      );
    };

    /** 处理告警人员标签变更 */
    const handleWarningMemberChange = (value: string[]) => {
      // 将标签数组转换为分号连接的字符串
      state.addFormData.warningMember = value.join(';');
    };

    /** 初始化告警人员标签 */
    const initWarningMemberTags = () => {
      if (state.addFormData.warningMember) {
        warningMemberTags.value = state.addFormData.warningMember
          .split(';')
          .filter(Boolean);
      } else {
        warningMemberTags.value = [];
      }
    };

    /** 处理部门变更 */
    const handleDepartmentChange = (value: number | string) => {
      if (value) {
        // 请求获取部门负责人信息
        fetchDepartmentAdmin(value);
      } else {
        // 清空部门负责人
        state.addFormData.siDepartmentAdmin = '';
      }
    };

    /** 获取部门负责人信息 */
    const fetchDepartmentAdmin = async (depId: number | string) => {
      try {
        const res = await ajax('getDepartmentAdmin', { dep_id: depId });

        if (res) {
          // 设置部门负责人
          state.addFormData.siDepartmentAdmin = res.sale_name || '';
        }
      } catch (error) {
        console.error('获取部门负责人失败', error);
        message.error('获取部门负责人失败');
      }
    };

    // watch
    // 监听路由变化
    watch(
      () => router.currentRoute.value,
      () => {
        Init = false;
        init();
      }
    );
    // 监听addFormData变化，初始化告警人员标签
    watch(
      () => state.addFormData,
      () => {
        initWarningMemberTags();
      },
      { deep: true }
    );
    // 监听新增/修改 项目/资金弹窗的关闭
    watch(
      () => [state.addPop.addVisible, state.addPop.addBudgetVisible],
      ([v1, v2]) => {
        if (!v1 && !v2) {
          if (ifProject()) {
            // 关闭新增/修改项目弹窗，重置项目列表操作索引
            state.tableIndex = -1;
          } else {
            // 关闭新增/修改资金申请弹窗，重置申请明细操作索引
            state.applyTableIndex = -1;
          }
          // 重置表单内容
          state.addFormData = {
            siName: userInfo.value.userName, // 申请人名称取当前用户名称
          };
          state.fileImgs = [];
          state.uploadImgs = [];
          // state.addType = '';
        }
      }
    );
    // 监听迁移资金弹窗关闭
    watch(
      () => [state.moveVisible],
      ([v1]) => {
        if (!v1) {
          // 关闭新增/修改资金申请弹窗，重置申请明细操作索引
          state.applyTableIndex = -1;
          // 重置表单内容
          state.moveFormData = {
            siName: userInfo.value.userName,
          };
          state.fileImgs = [];
          state.uploadImgs = [];
        }
      }
    );
    // 关闭活动明细、项目申请明细时，重置项目列表操作索引
    watch(
      () => [
        state.otherDetailVisible,
        state.applyVisible,
        detail.detailVisible,
      ],
      ([v1, v2, v3]) => {
        if (!v1 && !v2 && !v3) {
          state.tableIndex = -1;
        }
      }
    );
    // 当上传/删除新的图片，重新读取图片
    watch(
      () => state.addFormData.voucherImg,
      () => {
        changeUploadImgs('add');
      }
    );
    // 当上传/删除新的图片，重新读取图片
    watch(
      () => state.moveFormData.voucherImg,
      () => {
        changeUploadImgs('move');
      }
    );

    onMounted(init);
    return {
      ...toRefs(table),
      ...toRefs(detail),
      ...toRefs(state),
      ...ACTIVITIES_PARAMS,
      DETAIL_MODAL_CONFIG,
      DETAIL_DIMENSION,
      rowClassName,
      checkAddFormItem,
      ifAdd,
      editDisabled,
      ifProject,
      dealYYYYMMDD,
      handleSearch,
      handleOtherDetailModal,
      handleDetail,
      handleProjectModal,
      handleBudgetModal,
      handleBudgetMoveModal,
      handleApplyModal,
      handleConfirmAdd,
      handleConfirmMove,
      handleConfirmVerify,
      handleConfirmRefuse,
      handleFileUpload,
      handleMoveFileUpload,
      handleFileModal,
      handleFileModal3,
      handleAmountDetailModal,
      filterOption,
      onTableSubmit: onTableSubmit(fetchTable),
      onApplyTableSubmit: applyTable.onTableSubmit(fetchBudgetTable),
      nowProject,
      warningMemberTags,
      handleWarningMemberChange,
      initWarningMemberTags,
      handleDepartmentChange,
      filterTreeNode,
    };
  },
});
</script>

<style lang="less">
.m-packet-activities {
  .filter-wrapper {
    .m-project-name {
      width: 200px;
    }
    .m-project-name-long {
      width: 300px;

      .ant-select-selection-overflow {
        flex-wrap: nowrap;
        overflow: hidden;
      }

      .ant-select-selection-overflow-item {
        flex-wrap: nowrap;
      }
    }
    .m-add-btn {
      margin-left: 32px;
    }
    .m-header-item {
      margin-left: 20px;
      i {
        color: rgb(255, 100, 100);
      }
    }
  }
  .m-table-content {
    .header {
      &-activityDetail,
      &-redDetail,
      &-receiveDetail,
      &-useDetail {
        text-align: center;
      }
    }
  }
}
.m-apply-modal {
  .m-header-item {
    margin-right: 20px;
    margin-bottom: 20px;
    display: inline-block;
    i {
      color: rgb(255, 100, 100);
    }
  }
}
.m-add-modal,
.m-move-modal {
  .ant-descriptions-item-label {
    width: 100px;
  }
  .ant-upload-list-item-name {
    max-width: 200px;
  }
}
.m-file-modal {
  height: 680px;
  overflow: auto;
}

.ant-modal-content .ant-table-tbody {
  .ant-table-row {
    td {
      padding: 16px;
    }
  }
}
</style>
