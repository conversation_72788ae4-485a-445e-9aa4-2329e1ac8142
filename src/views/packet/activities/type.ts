import { Dayjs } from 'dayjs';

export type iState = {
  // user
  /** 新增/编辑 or 审核 */
  pageType: 'edit' | 'verify';

  // header
  /** 总申请金额 */
  totalAmount: number;
  /** 累计已使用金额 */
  usedAmount: number;
  /** 余额 */
  balance: number;
  /** 单项目总申请金额 */
  projectTotalAmount: number;

  // filters
  /** 项目Id */
  projectId: string;
  /** 部门Id */
  departmentId: (string | number)[];

  // table
  /** 列表总数 */
  total: number;
  /** 列表数据 */
  tableData: projectTableData[];
  /** 列表操作索引 */
  tableIndex: number;
  /** 申请明细列表总数 */
  applyTotal: number;
  /** 申请明细列表数据 */
  applyTableData: budgetTableData[];
  /** 申请操作索引 */
  applyTableIndex: number;

  // modal
  /** 加载 */
  loading: boolean;
  /** 其余明细弹窗显示 */
  otherDetailVisible: boolean;
  /** 其余明细类型 */
  otherDetailType: '' | 'activity' | 'portion';
  /** 申请明细弹窗显示 */
  applyVisible: boolean;
  /** 新增/修改弹窗显示 */
  addPop: {
    /** 新增/修改活动项目显示 */
    addVisible: boolean;
    /** 新增/修改/查看申请明细显示 */
    addBudgetVisible: boolean;
  };
  /** 新增修改/申请资金类型 */
  addType:
    | ''
    | 'add'
    | 'edit'
    | 'budget_add'
    | 'budget_edit'
    | 'budget_scan'
    | 'budget_move_add'
    | 'budget_move_edit'
    | 'budget_move_scan';
  /** 新增修改/申请资金表单数据 */
  addFormData: addForm;
  addFormCheck: {
    [x: string]: boolean;
  };
  /** 凭证弹窗显示 - 项目/资金细节弹窗 */
  fileVisible1: boolean;
  /** 凭证弹窗显示 - 申请明细列表 */
  fileVisible2: boolean;
  /** 凭证弹窗显示 - 资金迁移列表 */
  fileVisible3: boolean;
  /** 迁移资金弹窗显示 */
  moveVisible: boolean;
  /** 资金迁移表单数据 */
  moveFormData: moveForm;
  /** 已上传凭证图片列表 */
  fileImgs: string[];
  /** 本次上传图片列表 */
  uploadImgs: string[];
  /** 项目下拉列表 */
  projectSelectListForDropdown: {
    label: string;
    value: string;
  }[];
  /** 部门下拉列表 */
  departmentSelectList: any[];
  /** 部门资金明细列表 */
  amountDetailList: amountDetail[];
  /** 部门资金明细弹窗显示 */
  amountDetailVisible: boolean;
};

export type amountDetail = {
  dep_id: number; // 部门ID
  dep_name: string; // 部门名称
  apply_amount_total: number; // 总申请金额
  used_amount_total: number; // 累计已使用金额
  surplus_amount: number; // 余额
};
/** 项目列表项 */
export type projectTableData = {
  item_name: string; // 项目名称
  item_id: number; // 项目ID
  apply_amount: number; // 总申请资金
  used_amount: number; // 已使用金额
  surplus_amount: number; // 余额
  audit_count: number; // 状态/待审核资金数
  audit_status: number; // 第一笔资金审核状态
};
/** 活动明细列表项 */
export type actDetailTableData = {
  online_date: string; // 活动开始时间
  offline_date: string; // 活动结束时间
  online_date_str: string; // 活动开始时间 年月日
  offline_date_str: string; // 活动结束时间 年月日
  activity_name: string; // 活动名称
  activity_id: string; // 活动id
  put_in_qty: string; // 投放数
  receive_qty: string; // 领取数
  used_qty: string; // 已使用数
  used_rate: string; // 使用率
  used_amount: string; // 已使用金额
  used_volume: string; // 使用红包带来销量
};
/** 申请资金列表项 */
export type budgetTableData = {
  item_id: number; // 资金id
  arrival_date: string; // 到账日期
  pay_serial: string; // 到账流水号
  apply_amount: string; // 申请金额
  audit_status: number; // 审核状态
};
/** 活动明细列表项 */
/** 新增修改/申请资金项目表单数据 */
export type addForm = {
  itemName?: string; // 项目名称
  itemId?: string; // 项目id
  arrivalDate?: Dayjs; // 到账日期
  paySerial?: string; // 到账流水号
  applyAmount?: string; // 申请金额
  siDepartment?: string;
  siDepartmentAdmin?: string;
  siName?: string;
  remark?: string; // 备注
  reserveRatio?: string;
  warningRatio?: string;
  warningMember?: string;
  toleranceRatio?: string;
  voucherImg?: any[]; // 相关凭证
  depId?: string; // 部门id
};

/** 迁移资金表单数据 */
export type moveForm = {
  applyAmount?: string; // 迁移金额
  targetId?: string; // 迁移目标项目id
  voucherImg?: any[]; // 相关凭证
  remark?: string; // 备注
  siName?: string; // 申请人员
};
