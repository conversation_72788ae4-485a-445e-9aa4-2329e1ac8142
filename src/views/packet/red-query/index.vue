<template>
  <div class="m-red-query">
    <p class="m-page-title">项目核销查询</p>
    <div class="m-red-query-table table-wrapper">
      <div class="filter-wrapper">
        <a-range-picker v-model:value="date" show-time />
        <a-button type="primary" @click="handleSearch">查询</a-button>
      </div>
      <div class="m-table-content">
        <custom-table
          v-model:page-num="pageNum"
          v-model:page-size="pageSize"
          :table-total="total"
          :table-data="tableData"
          :scroll="{ scrollToFirstRowOnChange: true, x: 900, y: 600 }"
          :columns="RED_QUERY_COLUNMS"
          :page-size-list="['5', '10', '20', '50']"
          :row-class-name="rowClassName()"
          :on-submit="onTableSubmit"
        >
          <!-- 优惠券核销明细跳转 -->
          <template #detail="{ record, column }">
            <a-button type="link" @click="handleDetail(record, column)">
              查看明细
            </a-button>
          </template>
          <!-- 份额发放明细跳转 -->
          <template #portionDetail="{ index }">
            <a-button type="link" @click="handlePortionDetail(index)">
              查看明细
            </a-button>
          </template>
        </custom-table>
      </div>
    </div>
    <!-- 核销明细弹窗 -->
    <div class="m-red-query-modal">
      <a-modal
        v-model:visible="detailVisible"
        :title="DETAIL_MODAL_CONFIG[detailType].title"
        wrap-class-name="full-modal"
        width="96%"
        :footer="null"
        style="top: 20px"
      >
        <use-detail
          component-type="component"
          :calc-type="1"
          :calc-id="detailId"
          :show-date="false"
          :download="true"
          :if-used="true"
          :used-date="date"
        />
      </a-modal>
    </div>
    <!-- 份额发放明细弹窗 -->
    <div class="m-portion-detail-modal">
      <a-modal
        v-model:visible="portionModalVisible"
        title="份额发放明细"
        wrap-class-name="full-modal"
        width="96%"
        :footer="null"
        style="top: 20px"
      >
        <portion-detail
          :download="true"
          :project-id="tableIndex > -1 ? tableData[tableIndex].item_id : 0"
          :prop-date="date"
          :show-actual="true"
          :show-date="false"
        />
      </a-modal>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, onMounted, reactive, toRefs, watch } from 'vue';
import dayjs, { Dayjs } from 'dayjs';
dayjs.locale('zh-cn');
import ajax from '@/server/http';
// components
import CustomTable, {
  useCustomTable,
} from '@/components/common/CustomTable.vue';
import UseDetail from '../details/UseDetail.vue';
import PortionDetail from '../details/PortionDetail.vue';
// hook
import useDetail from '@/components/packet/detailHook';
// const
import { RED_QUERY_PARAMS, DETAIL_MODAL_CONFIG } from '../const';
// fn
import { getLastMonth } from '@/utils/fn';
// type
import { redQueryTableData } from '../type';

type iState = {
  // filters
  date: Dayjs[];
  // table
  total: number;
  tableData: redQueryTableData[];
  // modal
  tableIndex: number;
  portionModalVisible: boolean;
};

export default defineComponent({
  name: 'RedQuery',
  components: {
    CustomTable,
    UseDetail,
    PortionDetail,
  },
  setup() {
    const { detail, jumpDetail } = useDetail();
    const { table, onTableSubmit, onTableChange, rowClassName } =
      useCustomTable();
    const state = reactive<iState>({
      date: getLastMonth() as Dayjs[],
      total: 0,
      tableData: [],
      tableIndex: -1,
      portionModalVisible: false,
    });
    // methods
    const init = () => {
      fetchTable();
    };
    /** 请求表格 */
    const fetchTable = () => {
      detail.detailId = '';
      ajax(
        'getRedQueryTable',
        {
          begin_time: state.date[0].format('YYYY-MM-DD HH:mm:ss'),
          end_time: state.date[1].format('YYYY-MM-DD HH:mm:ss'),
        },
        {
          pageNum: table.pageNum,
          pageSize: table.pageSize,
        }
      )
        .then((res: any) => {
          if (res) {
            state.total = res.total;
            state.tableData = res.crmRecord;
          } else {
            state.total = 0;
            state.tableData = [];
          }
        })
        .catch((e) => {
          state.total = 0;
          state.tableData = [];
        });
    };
    // controller
    /** 搜索表格 */
    const handleSearch = () => {
      onTableChange(fetchTable);
    };
    /** 核销明细跳转 */
    const handleDetail = (record: redQueryTableData, col: any) => {
      const params = [
        { key: 'showDate', value: 'false' },
        { key: 'download', value: 'true' },
      ];
      jumpDetail(col.dataIndex.replace('Detail', ''), 1, record.item_id, params);
    };
    /** 份额明细弹窗 */
    const handlePortionDetail = (index: number) => {
      state.tableIndex = index;
      state.portionModalVisible = true;
    }

    // watch
    // 监听弹窗显示，关闭时重置tableIndex。
    watch(
      () => state.portionModalVisible,
      (visible, preVisible) => {
        if (!visible && preVisible) state.tableIndex = -1;
      }
    )

    onMounted(init);
    return {
      ...toRefs(table),
      ...toRefs(state),
      ...toRefs(detail),
      ...RED_QUERY_PARAMS,
      DETAIL_MODAL_CONFIG,
      rowClassName,
      handleSearch,
      onTableSubmit: onTableSubmit(fetchTable),
      handleDetail,
      handlePortionDetail,
    };
  },
});
</script>

<style lang="less">
@import '../index.less';
</style>
