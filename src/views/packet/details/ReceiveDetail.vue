<template>
  <div class="m-detail-receive">
    <div class="m-receive-tabs">
      <a-tabs v-model:activeKey="tab" type="card">
        <a-tab-pane key="1" tab="通过爱基金账号领取明细"></a-tab-pane>
        <a-tab-pane key="2" tab="通过同花顺账号领取明细"></a-tab-pane>
      </a-tabs>
    </div>
    <div class="m-receive-table table-wrapper">
      <div class="filter-wrapper">
        <div class="m-description-item">
          <span>券模板id：</span>
          <a-input
            v-model:value="couponTemplateId"
            placeholder="请输入券模板id"
          />
        </div>
        <div v-if="calc.showDate">
          <a-range-picker v-model:value="date" />
        </div>
        <a-button type="primary" @click="handleSearch">查询</a-button>
      </div>
      <div class="m-table-content">
        <custom-table
          v-model:page-num="pageNum"
          v-model:page-size="pageSize"
          :table-total="total"
          :table-data="tableData"
          :scroll="{ scrollToFirstRowOnChange: true, x: 900, y: 600 }"
          :columns="columns"
          :page-size-list="['5', '10', '20', '50']"
          :row-class-name="rowClassName()"
          :on-submit="onTableSubmit"
        >
          <!-- 用户账号跳转 -->
          <template #user="{ text, record }">
            <a
              v-if="record.crm_id"
              :href="customSrc(record)"
              target="_blank"
              rel="noopener"
            >
              {{ text }}
            </a>
            <span v-else class="m-disabled-jump">{{ text }}</span>
          </template>
        </custom-table>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import {
  computed,
  defineComponent,
  onMounted,
  PropType,
  reactive,
  toRefs,
  watch,
} from 'vue';
import dayjs, { Dayjs } from 'dayjs';
dayjs.locale('zh-cn');
import { useRouter } from 'vue-router';
import ajax from '@/server/http';
// components
import CustomTable, {
  useCustomTable,
} from '@/components/common/CustomTable.vue';
import useDetailPage from '@/components/packet/detailPageHook';
// const
import { DETAIL_PARAMS } from '../const';
import { baseWebURL } from '@/utils/request';
// fn
import { getLastMonth } from '@/utils/fn';
// type
import { receiveTableData, CalcType } from '../type';
import { getDetailBody, initTable } from '../fn';

type iState = {
  // tab
  tab: '1' | '2';
  // filters
  couponTemplateId: string;
  date: Dayjs[];
  // table
  total: number;
  tableData: receiveTableData[];
};

export default defineComponent({
  name: 'ReceiveDetail',
  components: {
    CustomTable,
  },
  props: {
    // 明细页类型：组件/页面
    componentType: {
      type: String as PropType<'page' | 'component'>,
      default: () => 'page',
    },
    // 明细依赖key，key值变化即刷新数据
    detailKey: {
      type: String,
      default: () => '',
    },
    // 统计维度
    calcType: {
      type: Number as PropType<CalcType>,
      default: () => 1,
    },
    // 统计id
    calcId: {
      type: String,
      default: () => '',
    },
    // 是否展示时间筛选器
    showDate: {
      type: Boolean,
      default: () => true,
    },
    // 是否展示下载按钮
    download: {
      type: Boolean,
      default: () => false,
    },
    // 预留的扩展字段
    expand: {
      type: Object,
      default: () => {},
    },
  },
  setup(props) {
    const router = useRouter();
    const { table, onTableSubmit, onTableChange, rowClassName } =
      useCustomTable();
    const state = reactive<iState>({
      couponTemplateId: null,
      tab: '1',
      date: getLastMonth() as Dayjs[],
      total: 0,
      tableData: [],
    });
    // computed
    const columns = computed(() => {
      let cols = [...DETAIL_PARAMS.RECEIVE_DETAIL_COLUMNS];
      if (state.tab === '2')
        cols = cols.filter((col) => col.dataIndex !== 'cust_name');
      return cols;
    });
    // methods
    /** 初始化 */
    const init = () => {
      fetchTable();
    };
    /** 清空表格 */
    const clear = initTable(state);
    /** 请求表格 */
    const fetchTable = () => {
      clear();
      const data: any = getDetailBody(calc, state.date, {
        cust_type: Number(state.tab),
        template_id_list: state.couponTemplateId
          ? [Number(state.couponTemplateId)]
          : [],
      });
      if (calc.calcType === 4 && props.expand) {
        data.coupon_id_list = [...props.expand.coupon_id_list];
      }
      ajax('getReceiveDetailTable', data, {
        pageNum: table.pageNum,
        pageSize: table.pageSize,
      })
        .then((res: any) => {
          if (res) {
            state.total = res.total;
            state.tableData = res.crmRecord;
          } else {
            clear();
          }
        })
        .catch(() => {
          clear();
        });
    };
    /** 客户详情页链接 */
    const customSrc = (record: receiveTableData) => {
      return `${baseWebURL}#customer/info?id=${record.crm_id}`;
    };
    // controller
    /** 搜索表格 */
    const handleSearch = () => {
      onTableChange(fetchTable);
    };

    // watch
    // 路由变化重新进行初始化
    watch(
      () => router.currentRoute.value,
      () => {
        calc = useDetailPage(props, handleSearch);
        init();
      }
    );
    // 监听tab变化
    watch(
      () => state.tab,
      () => {
        onTableChange(fetchTable);
      }
    );

    let calc = useDetailPage(props, handleSearch);

    onMounted(init);
    return {
      ...toRefs(table),
      ...toRefs(state),
      calc,
      columns,
      rowClassName,
      customSrc,
      onTableSubmit: onTableSubmit(fetchTable),
      handleSearch,
    };
  },
});
</script>

<style lang="less">
@import '../index.less';
</style>
