<!-- 基金份额明细 -->
<template>
  <div class="m-portion-detail">
    <div class="m-portion-detail-tabs">
      <a-tabs v-model:activeKey="state.tab">
        <template v-for="(item, index) in tabs" :key="index">
          <a-tab-pane
            v-if="!showActual || item.key === 'actual'"
            :key="index"
            :tab="item.name"
          >
            <div class="m-portion-detail-table table-wrapper">
              <div class="filter-wrapper">
                <a-range-picker v-show="showDate" v-model:value="state.date" />
                <a-button
                  v-show="showDate"
                  type="primary"
                  @click="handleSearch"
                >
                  查询
                </a-button>
                <a-button v-if="download" @click="handleDownload">
                  下载实发明细
                </a-button>
              </div>
              <div class="m-table-content">
                <custom-table
                  v-model:page-num="tabTable(item.key).pageNum"
                  v-model:page-size="tabTable(item.key).pageSize"
                  :table-total="tabTable(item.key).total"
                  :table-data="tabTable(item.key).tableData"
                  :scroll="{ scrollToFirstRowOnChange: true, x: 1500, y: 560 }"
                  :columns="columns[item.key]"
                  :page-size-list="['5', '10', '20', '50']"
                  :on-submit="handleTableChange"
                >
                  <!-- 基金类型 -->
                  <template #fund_type="{ text }">
                    {{ FundType[text] }}
                  </template>
                </custom-table>
              </div>
            </div>
          </a-tab-pane>
        </template>
      </a-tabs>
    </div>
  </div>
</template>

<script lang="ts" setup>
import {
  computed,
  defineProps,
  onMounted,
  PropType,
  reactive,
  watch,
} from 'vue';
import dayjs, { Dayjs } from 'dayjs';
import ajax from '@/server/http';
// hook
import useTable, { TableHook } from '@/components/hooks/useTable';
// components
import CustomTable from '@/components/common/CustomTable.vue';
import { getLastMonth } from '@/utils/fn';
// const
import { ACTIVITIES_PARAMS } from '../const';
import { PortionDetailTableData } from '../type';
import { message } from 'ant-design-vue';
dayjs.locale('zh-CN');

type TabKey = 'expected' | 'actual';
type iState = {
  date: Dayjs[];
  tab: number;
  tableMap: {
    [x in TabKey]: TableHook<PortionDetailTableData>;
  };
};
/** 基金类型 */
enum FundType {
  货币型,
  债券型,
  混合型,
  股票型,
}
/** tab表 */
const tabs: {
  key: TabKey;
  name: string;
}[] = [
  {
    key: 'expected',
    name: '应发份额',
  },
  {
    key: 'actual',
    name: '实发份额',
  },
];
const dateFormat = 'YYYY-MM-DD HH:mm:ss';
const col1 = ACTIVITIES_PARAMS.PORTION_DETAIL_COLUMNS;
const col2 = JSON.parse(JSON.stringify(col1)).map(col => {
  if (col.dataIndex === 'create_time') {
    col.title = '操作时间';
  }
  if (col.dataIndex === 'user_id') {
    col.dataIndex = 'cus_id';
  }
  return col;
});
const columns = { expected: col1, actual: col2 };

// props
const props = defineProps({
  // 项目id
  projectId: {
    type: Number as PropType<number | string>,
    default: () => 0,
  },
  // 是否展示时间筛选
  showDate: {
    type: Boolean,
    default: () => true,
  },
  // 输入时间
  propDate: {
    type: Array as PropType<Dayjs[]>,
    default: () => [],
  },
  // 是否只展示实发名单
  showActual: {
    type: Boolean,
    default: () => false,
  },
  // 是否导出实发名单
  download: {
    type: Boolean,
    default: () => false,
  },
});
// state
const state = reactive<iState>({
  date: !props.showDate ? props.propDate : getLastMonth(),
  tab: props.showActual ? 1 : 0,
  tableMap: {
    expected: useTable<PortionDetailTableData>(),
    actual: useTable<PortionDetailTableData>(),
  },
});

// computed
/** 当前tab的key */
const tabKey = computed(() => tabs[state.tab].key);
/** 当前tab的tableHook */
const tabTableHook = computed(() => state.tableMap[tabKey.value]);

// methods
/** 初始化 */
const init = () => {
  // 请求应发
  !props.showActual && fetchTable('expected');
  // 请求实发
  fetchTable('actual');
};
/** 请求表格数据 */
const fetchTable = (queryType?: TabKey) => {
  // 如果未传参，默认查询当前tab表格
  const type = queryType || tabs[state.tab].key;
  const key: string | string[] = type.split('');
  key.splice(0, 1, key[0].toUpperCase());
  const data = {
    project_id: props.projectId || '',
    start_time: state.date[0].format(dateFormat),
    end_time: state.date[1].format(dateFormat),
    type: type === 'expected' ? '1' : '2',
  };
  // 请求table
  const table = state.tableMap[type].table;
  ajax(`get${key.join('')}PortionTable`, data, {
    pageNum: table.pageNum,
    pageSize: table.pageSize,
  }).then((res: any) => {
    try {
      if (res) {
        table.total = res.total;
        table.tableData = res.crmRecord;
      } else {
        state.tableMap[type].initTable();
        message.info('份额明细为空!');
      }
    } catch (e) {
      console.error(e);
    }
  });
};
/** 导出明细 */
const exportDetail = () => {
  const data = {
    project_id: props.projectId || '',
    start_time: state.date[0].format(dateFormat),
    end_time: state.date[1].format(dateFormat),
  };
  ajax('exportPortionDetail', data);
};
/** 当前tab的table */
const tabTable = (key: TabKey) => state.tableMap[key].table;

// controller
/** 表格查询 */
const handleSearch = () => {
  // 请求应发
  !props.showActual &&
    state.tableMap.expected.onTableChange(() => fetchTable('expected'));
  // 请求实发
  state.tableMap.actual.onTableChange(() => fetchTable('actual'));
};
/** 表格分页 */
const handleTableChange = (pageNum: number, pageSize: number) => {
  tabTableHook.value.onTableSubmit(fetchTable)({ pageNum, pageSize });
};
/** 下载份额明细 */
const handleDownload = () => {
  exportDetail();
};

// watch
// 监听projectId变化，请求表格数据
watch(
  () => props.projectId,
  (id, preId) => {
    id && id !== preId && handleSearch();
  }
);
// 监听propDate变化，若不为空，替换state.date
watch(
  () => props.propDate,
  (propDate) => {
    if (propDate && propDate.length > 0) {
      state.date = propDate;
    }
  }
);
onMounted(init);
</script>
