<template>
  <div class="m-detail-red">
    <div class="m-red-table table-wrapper">
      <div v-if="calc.showDate" class="filter-wrapper">
        <div class="m-description-item">
          <span>券模板id：</span>
          <a-input
            v-model:value="couponTemplateId"
            placeholder="请输入券模板id"
          />
        </div>
        <a-range-picker v-model:value="date" />
        <a-button type="primary" @click="handleSearch">查询</a-button>
      </div>
      <div class="m-table-content">
        <custom-table
          v-model:page-num="pageNum"
          v-model:page-size="pageSize"
          :table-total="total"
          :table-data="tableData"
          :scroll="{
            scrollToFirstRowOnChange: true,
            x: ifSale() ? 1500 : 2000,
            y: 600,
          }"
          :columns="columns()"
          :page-size-list="['5', '10', '20', '50']"
          :row-class-name="rowClassName()"
          :on-submit="onTableSubmit"
        >
          <!-- 优惠券金额 -->
          <template #redValue="{ record }">
            <span v-if="record.coupon_type === '1'">满{{ record.sum_money }}减{{ record.sub_money }}</span>
            <span v-else>{{ formatMoney(record.sub_rate * 10) }}折</span>
          </template>
          <!-- 百分比 -->
          <template #rate="{ text }">
            {{ Number(text || '').toFixed(2) }}%
          </template>
          <!-- 明细跳转 -->
          <template #detail="{ record, column }">
            <a-button type="link" @click="handleDetail(record, column)">
              查看明细
            </a-button>
          </template>
        </custom-table>
      </div>
    </div>
    <div class="m-detail-red-modal">
      <!-- 明细页弹窗 -->
      <a-modal
        v-model:visible="detailVisible"
        :title="DETAIL_MODAL_CONFIG[detailType].title"
        wrap-class-name="full-modal"
        width="96%"
        :footer="null"
        style="top: 20px"
      >
        <use-detail
          component-type="component"
          :calc-type="3"
          :calc-id="detailId"
          :show-date="false"
        />
      </a-modal>
    </div>
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  onMounted,
  PropType,
  reactive,
  toRefs,
  watch,
} from 'vue';
import dayjs, { Dayjs } from 'dayjs';
dayjs.locale('zh-cn');
import { useRouter } from 'vue-router';
// components
import CustomTable, {
  useCustomTable,
} from '@/components/common/CustomTable.vue';
import UseDetail from './UseDetail.vue';
// hook
import useDetail from '@/components/packet/detailHook';
import useDetailPage from '@/components/packet/detailPageHook';
// const
import { DETAIL_PARAMS, DETAIL_MODAL_CONFIG } from '../const';
import ajax from '@/server/http';
// fn
import { getLastMonth, getUrlParam, formatMoney } from '@/utils/fn';
// type
import { redTableData, CalcType } from '../type';
import { getDetailBody, initTable } from '../fn';

type iState = {
  // filters
  couponTemplateId: string;
  date: Dayjs[];
  // table
  total: number;
  tableData: redTableData[];
};

export default defineComponent({
  name: 'RedDetail',
  components: {
    CustomTable,
    UseDetail,
  },
  props: {
    // 明细页类型：组件/页面
    componentType: {
      type: String as PropType<'page' | 'component'>,
      default: () => 'page',
    },
    // 明细依赖key，key值变化即刷新数据
    detailKey: {
      type: String,
      default: () => '',
    },
    // 统计维度
    calcType: {
      type: Number as PropType<CalcType>,
      default: () => 1,
    },
    // 统计id
    calcId: {
      type: String,
      default: () => '',
    },
    // 是否展示时间筛选器
    showDate: {
      type: Boolean,
      default: () => true,
    },
    // 是否展示下载按钮
    download: {
      type: Boolean,
      default: () => false,
    },
    // 预留的扩展字段
    expand: {
      type: Object,
      default: () => {},
    },
  },
  setup(props) {
    const router = useRouter();
    const { detail, jumpDetail } = useDetail();
    const { table, onTableSubmit, onTableChange, rowClassName } =
      useCustomTable();
    const state = reactive<iState>({
      couponTemplateId: null,
      date: getLastMonth() as Dayjs[],
      total: 0,
      tableData: [],
    });
    // methods
    /** 初始化 */
    const init = () => {
      fetchTable();
    };
    /** 清空表格 */
    const clear = initTable(state);
    /** 请求表格 */
    const fetchTable = () => {
      clear();
      const data: any = getDetailBody(calc, state.date, {
        template_id_list: state.couponTemplateId
          ? [Number(state.couponTemplateId)]
          : [],
      });
      if (calc.calcType === 4 && props.expand) {
        data.coupon_id_list = [...props.expand.coupon_id_list];
      }
      ajax('getRedDetailTable', data, {
        pageNum: table.pageNum,
        pageSize: table.pageSize,
      })
        .then((res: any) => {
          if (res) {
            state.total = res.total;
            state.tableData = res.crmRecord;
          } else {
            clear();
          }
        })
        .catch(() => {
          clear();
        });
    };
    /** 是否是销售维度优惠券明细 */
    const ifSale = () => {
      return (
        (props.componentType === 'page' &&
          getUrlParam('type', 'hash') == '4') ||
        (props.componentType === 'component' && calc.calcType === 4)
      );
    };
    /** 表格列 */
    const columns = () => {
      // 如果是销售维度与其他维度表格列不同
      if (ifSale()) {
        return DETAIL_PARAMS.RED_DETAIL_COLUMNS_SALE;
      }
      return DETAIL_PARAMS.RED_DETAIL_COLUMNS;
    };
    // controller
    /** 搜索表格 */
    const handleSearch = () => {
      onTableChange(fetchTable);
    };
    /** 各明细页跳转 */
    const handleDetail = (record: redTableData, col: any) => {
      const params = [{ key: 'showDate', value: 'false' }];
      detail.detailId = record.coupon_id;
      jumpDetail(
        col.dataIndex.replace('Detail', ''),
        3,
        record.coupon_id,
        params
      );
    };

    // watch
    // 路由变化重新进行初始化
    watch(
      () => router.currentRoute.value,
      () => {
        calc = useDetailPage(props, handleSearch);
        init();
      }
    );

    let calc = useDetailPage(props, handleSearch);

    onMounted(init);
    return {
      ...toRefs(table),
      ...toRefs(state),
      ...toRefs(detail),
      calc,
      ...DETAIL_PARAMS,
      DETAIL_MODAL_CONFIG,
      rowClassName,
      ifSale,
      columns,
      onTableSubmit: onTableSubmit(fetchTable),
      handleSearch,
      handleDetail,
      formatMoney
    };
  },
});
</script>

<style lang="less">
@import '../index.less';
</style>
