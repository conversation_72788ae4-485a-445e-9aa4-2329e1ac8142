<template>
  <div class="m-detail-use">
    <div class="m-use-table table-wrapper">
      <div v-if="calc.showDate || calc.download" class="filter-wrapper">
        <template v-if="calc.showDate">
          <div class="m-description-item">
            <span>券模板id：</span>
            <a-input
              v-model:value="couponTemplateId"
              placeholder="请输入券模板id"
            />
          </div>
          <a-range-picker v-model:value="date" />
          <a-button type="primary" @click="handleSearch">查询</a-button>
        </template>
        <a-button v-if="calc.download" type="primary" @click="handleDownload">
          下载
        </a-button>
      </div>
      <div class="m-table-content">
        <custom-table
          v-model:page-num="pageNum"
          v-model:page-size="pageSize"
          :table-total="total"
          :table-data="tableData"
          :scroll="{ scrollToFirstRowOnChange: true, x: 1720, y: 600 }"
          :columns="columns"
          :page-size-list="['5', '10', '20', '50']"
          :row-class-name="rowClassName()"
          :on-submit="onTableSubmit"
        >
          <!-- 下单日期 -->
          <template #date="{ text }">
            <span>{{ dealYYYYMMDD(text.slice(0, 8)) }}</span>
          </template>
          <!-- 用户账号跳转 -->
          <template #user="{ text, record }">
            <a v-if="record.crm_id" :href="customSrc(record)" target="_blank">
              {{ text }}
            </a>
            <span v-else class="m-disabled-jump">{{ text }}</span>
          </template>
          <!-- 购买基金 -->
          <template #fund="{ record }">
            <div>{{ record.fund_name }}</div>
            <div>({{ record.fund_code }})</div>
          </template>
          <!-- 红包金额 -->
          <template #redValue="{ record }">
            <span v-if="record.coupon_type === '1'">满{{ record.sum_money }}减{{ record.sub_money }}</span>
            <span v-else>{{ formatMoney(record.sub_rate * 10) }}折</span>
          </template>
        </custom-table>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  onMounted,
  PropType,
  reactive,
  toRefs,
  watch,
} from 'vue';
import dayjs, { Dayjs } from 'dayjs';
dayjs.locale('zh-cn');
import { useRouter } from 'vue-router';
import ajax from '@/server/http';
// components
import CustomTable, {
  useCustomTable,
} from '@/components/common/CustomTable.vue';
import useDetailPage from '@/components/packet/detailPageHook';
// const
import { DETAIL_PARAMS } from '../const';
import { baseWebURL } from '@/utils/request';
// fn
import { getLastMonth, dealYYYYMMDD, formatMoney } from '@/utils/fn';
// type
import { useTableData, CalcType } from '../type';
import { getDetailBody, initTable } from '../fn';

type iState = {
  // url
  // filters
  couponTemplateId: string;
  date: Dayjs[];
  // table
  total: number;
  tableData: useTableData[];
};

export default defineComponent({
  name: 'UseDetail',
  components: {
    CustomTable,
  },
  props: {
    // 明细页类型：组件/页面
    componentType: {
      type: String as PropType<'page' | 'component'>,
      default: () => 'page',
    },
    // 明细依赖key，key值变化即刷新数据
    detailKey: {
      type: String,
      default: () => '',
    },
    // 统计维度
    calcType: {
      type: Number as PropType<CalcType>,
      default: () => 1,
    },
    // 统计id
    calcId: {
      type: String,
      default: () => '',
    },
    // 是否展示时间筛选器
    showDate: {
      type: Boolean,
      default: () => true,
    },
    // 是否展示下载按钮
    download: {
      type: Boolean,
      default: () => false,
    },
    // 预留的扩展字段
    expand: {
      type: Object,
      default: () => {},
    },
    // 是否是核销明细
    ifUsed: {
      type: Boolean,
      default: () => false,
    },
    // 核销筛选时间
    usedDate: {
      type: Array as PropType<Dayjs[]>,
      default: () => [dayjs(new Date()), dayjs(new Date())],
    },
  },
  setup(props) {
    const columns = DETAIL_PARAMS.USE_DETAIL_COLUMNS(props.ifUsed);
    const router = useRouter();
    const { table, onTableSubmit, onTableChange, rowClassName } =
      useCustomTable();
    const state = reactive<iState>({
      couponTemplateId: null,
      date: getLastMonth() as Dayjs[],
      total: 0,
      tableData: [],
    });
    // methods
    /** 初始化 */
    const init = () => {
      fetchTable();
    };
    /** 清空表格 */
    const clear = initTable(state);
    /** 请求表格 */
    const fetchTable = () => {
      clear();
      const data: any = getDetailBody(calc, state.date, {
        template_id_list: state.couponTemplateId
          ? [Number(state.couponTemplateId)]
          : [],
      });
      if (calc.calcType === 4 && props.expand) {
        data.coupon_id_list = [...props.expand.coupon_id_list];
      }
      if (props.ifUsed) {
        data.start_time = props.usedDate[0].format('YYYY-MM-DD HH:mm:ss');
        data.end_time = props.usedDate[1].format('YYYY-MM-DD HH:mm:ss');
      }
      // 核销 or 使用
      ajax(props.ifUsed ? 'getPacketUsedTable' : 'getUseDetailTable', data, {
        pageNum: table.pageNum,
        pageSize: table.pageSize,
      })
        .then((res: any) => {
          if (res) {
            state.total = res.total;
            state.tableData = res.crmRecord;
          } else {
            clear();
          }
        })
        .catch(() => {
          clear();
        });
    };
    /** 导出核销明细 */
    const exportDetail = () => {
      ajax('exportPacketUsedFile', {
        project_id: props.calcId,
        start_time: props.usedDate[0].format('YYYY-MM-DD HH:mm:ss'),
        end_time: props.usedDate[1].format('YYYY-MM-DD HH:mm:ss'),
      });
    };
    /** 客户详情页链接 */
    const customSrc = (record: useTableData) => {
      return `${baseWebURL}#customer/info?id=${record.crm_id}`;
    };
    // controller
    /** 搜索表格 */
    const handleSearch = () => {
      onTableChange(fetchTable);
    };
    /** 下载使用明细 */
    const handleDownload = () => {
      exportDetail();
    };

    // watch
    // 路由变化重新进行初始化
    watch(
      () => router.currentRoute.value,
      () => {
        calc = useDetailPage(props, handleSearch);
        init();
      }
    );

    let calc = useDetailPage(props, handleSearch);

    onMounted(init);
    return {
      ...toRefs(table),
      ...toRefs(state),
      calc,
      columns,
      rowClassName,
      customSrc,
      dealYYYYMMDD,
      onTableSubmit: onTableSubmit(fetchTable),
      handleSearch,
      handleDownload,
      formatMoney,
    };
  },
});
</script>

<style lang="less">
@import '../index.less';
</style>
