<template>
  <div class="m-packet-actDetail">
    <div class="m-actDetail-table table-wrapper">
      <div class="filter-wrapper">
        <a-range-picker v-model:value="date" />
        <a-button type="primary" @click="handleSearch">查询</a-button>
      </div>
      <div class="m-table-content">
        <custom-table
          v-model:page-num="pageNum"
          v-model:page-size="pageSize"
          :table-total="total"
          :table-data="tableData"
          :scroll="{ scrollToFirstRowOnChange: true, x: 1500, y: 560 }"
          :columns="ACTIVITIES_DETAIL_COLUMNS"
          :page-size-list="['5', '10', '20', '50']"
          :row-class-name="rowClassName()"
          :on-submit="onTableSubmit"
        >
          <!-- 活动起止时间 -->
          <template #actDate="{ record }">
            <span>{{ record.online_date_str }} ~</span>
            <span>{{ record.offline_date_str }}</span>
          </template>
          <!-- 比例 -->
          <template #rate="{ text }">
            <div>{{ Number(text || '').toFixed(2) }}%</div>
          </template>
          <!-- 明细跳转 -->
          <template #detail="{ record, column }">
            <a-button type="link" @click="handleDetail(record, column)">
              查看明细
            </a-button>
          </template>
        </custom-table>
      </div>
    </div>
    <!-- 明细页弹窗 -->
    <a-modal
      v-model:visible="detailVisible"
      :title="DETAIL_MODAL_CONFIG[detailType].title"
      wrap-class-name="full-modal"
      width="96%"
      :footer="null"
      style="top: 20px"
    >
      <use-detail
        v-if="detailType === 'use'"
        component-type="component"
        :calc-type="2"
        :calc-id="detailId"
        :show-date="false"
      />
      <red-detail
        v-if="detailType === 'red'"
        component-type="component"
        :calc-type="2"
        :calc-id="detailId"
        :show-date="false"
      />
    </a-modal>
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, watch } from 'vue';
import dayjs, { Dayjs } from 'dayjs';
dayjs.locale('zh-cn');
// components
import CustomTable, {
  useCustomTable,
} from '@/components/common/CustomTable.vue';
import UseDetail from '@/views/packet/details/UseDetail.vue';
import RedDetail from '@/views/packet/details/RedDetail.vue';
// hook
import useDetail from '@/components/packet/detailHook';
// fn
import { getLastMonth } from '@/utils/fn';
import { initTable } from '../fn';
// const
import { ACTIVITIES_PARAMS, DETAIL_MODAL_CONFIG } from '@/views/packet/const';
import ajax from '@/server/http';
// type
import { actDetailTableData } from '@/views/packet/activities/type';

type iState = {
  // filter
  date: Dayjs[];
  // table
  total: number;
  tableData: actDetailTableData[];
};

export default defineComponent({
  name: 'ActivityDetail',
  components: {
    CustomTable,
    UseDetail,
    RedDetail,
  },
  props: {
    // 项目id
    projectId: {
      type: Number,
      default: () => 0,
    },
  },
  setup(props) {
    const { ACTIVITIES_DETAIL_COLUMNS } = ACTIVITIES_PARAMS;
    const { detail, jumpDetail } = useDetail();
    const { table, onTableSubmit, onTableChange, rowClassName } =
      useCustomTable();
    const state = reactive<iState>({
      // filter
      date: getLastMonth() as Dayjs[],
      // table
      total: 0,
      tableData: [],
    });

    // methods
    /** 初始化 */
    const init = () => {
      onTableChange(fetchTable);
    };
    /** 清空表格 */
    const clear = initTable(state);
    /** 查询表格 */
    const fetchTable = () => {
      clear();
      ajax(
        'getActivityTable',
        {
          type: '1',
          project_id: props.projectId,
          source: 'b2c-fund',
          start_time: state.date[0].format('YYYY-MM-DD') + ' 00:00:00',
          end_time: state.date[1].format('YYYY-MM-DD') + ' 23:59:59',
        },
        {
          pageNum: table.pageNum,
          pageSize: table.pageSize,
        }
      )
        .then((res: any) => {
          if (res) {
            state.total = res.total;
            state.tableData = res.crmRecord;
          } else {
            clear();
          }
        })
        .catch(() => {
          clear();
        });
    };
    // controller
    /** 搜索表格 */
    const handleSearch = () => {
      onTableChange(fetchTable);
    };
    /** 各明细页跳转 */
    const handleDetail = (record: actDetailTableData, col: any) => {
      jumpDetail(col.dataIndex.replace('Detail', ''), 2, record.activity_id);
    };

    // watch
    watch(
      () => props.projectId,
      (id) => {
        if (id) init();
      },
      {
        immediate: true,
      }
    );

    return {
      ...toRefs(table),
      ...toRefs(detail),
      ...toRefs(state),
      ACTIVITIES_DETAIL_COLUMNS,
      DETAIL_MODAL_CONFIG,
      onTableSubmit: onTableSubmit(fetchTable),
      handleSearch,
      handleDetail,
      rowClassName,
    };
  },
});
</script>

<style lang="less">
@import '../index.less';
</style>
