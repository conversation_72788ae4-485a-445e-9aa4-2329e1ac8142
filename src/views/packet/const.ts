// 券相关常量

import { couponFormItem, couponTableData } from './type';

const OTHER_DETAIL_TITLE = {
  activity: '项目活动明细',
  portion: '基金份额明细',
};
/** 活动项目列表 */
const PROJECT_COLUMNS = [
  {
    dataIndex: 'item_name',
    key: 'item_name',
    title: '项目名称',
    width: '200px',
  },
  {
    dataIndex: 'item_id',
    key: 'item_id',
    title: '项目ID',
    width: '120px',
  },
  {
    dataIndex: 'dep_name',
    key: 'dep_name',
    title: '申请部门',
    width: '150px',
  },
  {
    dataIndex: 'apply_amount',
    key: 'apply_amount',
    title: '总申请资金',
    width: '150px',
  },
  {
    dataIndex: 'move_amount',
    key: 'move_amount',
    title: '迁移资金',
    width: '150px',
  },
  {
    dataIndex: 'write_off_amount',
    key: 'write_off_amount',
    title: '已核销金额',
    width: '150px',
    slotName: 'defaultNum',
  },
  {
    dataIndex: 'used_amount',
    key: 'used_amount',
    title: '已使用金额',
    width: '150px',
    slotName: 'defaultNum',
  },
  {
    dataIndex: 'surplus_amount',
    key: 'surplus_amount',
    title: '余额',
    width: '150px',
    slotName: 'surplus_amount',
  },
  {
    dataIndex: 'redDetail',
    key: 'redDetail',
    title: '优惠券明细',
    slotName: 'detail',
    width: '120px',
  },
  {
    dataIndex: 'receiveDetail',
    key: 'receiveDetail',
    title: '领取明细',
    slotName: 'detail',
    width: '120px',
  },
  {
    dataIndex: 'useDetail',
    key: 'useDetail',
    title: '使用明细',
    slotName: 'detail',
    width: '120px',
  },
  // 2022.6.14 新增基金份额明细
  {
    dataIndex: 'portionDetail',
    key: 'portionDetail',
    title: '基金份额明细',
    slotName: 'otherDetail',
    width: '120px',
  },
  {
    dataIndex: 'audit_count',
    key: 'audit_count',
    title: '状态',
    slotName: 'audit_count',
    width: '100px',
  },
  {
    dataIndex: '',
    key: '',
    title: '操作',
    slotName: 'options',
    width: '170px',
  },
];
/** 活动明细列表 */
const ACTIVITIES_DETAIL_COLUMNS = [
  {
    dataIndex: 'actDate',
    key: 'actDate',
    title: '活动起止时间',
    slotName: 'actDate',
    width: '200px',
  },
  {
    dataIndex: 'activity_name',
    key: 'activity_name',
    title: '活动名称',
    width: '150px',
  },
  {
    dataIndex: 'activity_id',
    key: 'activity_id',
    title: '活动ID',
    width: '100px',
  },
  {
    dataIndex: 'put_in_qty',
    key: 'put_in_qty',
    title: '投放数',
    width: '100px',
  },
  {
    dataIndex: 'receive_qty',
    key: 'receive_qty',
    title: '领取数',
    width: '100px',
  },
  {
    dataIndex: 'used_qty',
    key: 'used_qty',
    title: '已使用数',
    width: '100px',
  },
  {
    dataIndex: 'used_rate',
    key: 'used_rate',
    title: '使用率',
    slotName: 'rate',
    width: '100px',
  },
  {
    dataIndex: 'used_amount',
    key: 'used_amount',
    title: '已使用金额',
    width: '180px',
  },
  {
    dataIndex: 'used_volume',
    key: 'used_volume',
    title: '使用优惠券带来销量',
    width: '180px',
  },
  {
    dataIndex: 'redDetail',
    key: 'redDetail',
    title: '优惠券明细',
    slotName: 'detail',
  },
  {
    dataIndex: 'useDetail',
    key: 'useDetail',
    title: '使用明细',
    slotName: 'detail',
  },
];
// 份额明细
/** 份额明细列表项 */
const PORTION_DETAIL_COLUMNS = [
  {
    dataIndex: 'create_time',
    title: '发放时间',
    width: '200px',
  },
  {
    dataIndex: 'activity_name',
    title: '活动名称',
    width: '200px',
  },
  {
    dataIndex: 'user_id',
    title: '用户客户号',
    width: '100px',
  },
  {
    dataIndex: 'cust_name',
    title: '用户姓名',
    width: '80px',
  },
  {
    dataIndex: 'fund_type',
    title: '基金类型',
    width: '80px',
    slotName: 'fund_type',
  },
  {
    dataIndex: 'fund_code',
    title: '发放基金代码',
    width: '100px',
  },
  {
    dataIndex: 'amount',
    title: '发放金额',
    width: '120px',
  },
];
/** 申请明细列表 */
const APPLY_COLUMNS = [
  {
    dataIndex: 'arrival_date',
    key: 'arrival_date',
    title: '到账/提现日期',
    slotName: 'date',
  },
  {
    dataIndex: 'pay_serial',
    key: 'pay_serial',
    title: '到账流水号',
  },
  {
    dataIndex: 'apply_amount',
    key: 'apply_amount',
    title: '申请金额',
  },
  {
    dataIndex: 'target_name',
    key: 'target_name',
    title: '迁入/迁出方',
  },
  {
    dataIndex: 'audit_status',
    key: 'audit_status',
    title: '审核状态',
    slotName: 'audit_status',
  },
  {
    dataIndex: 'options',
    key: 'options',
    title: '查看凭证',
    slotName: 'options',
  },
  {
    dataIndex: 'remark',
    key: 'remark',
    title: '备注',
  },
  {
    dataIndex: 'si_name',
    key: 'si_name',
    title: '申请人',
  },
];
/** 新增修改/申请资金名 */
const ADD_MODAL_TITLE = {
  add: '新增项目',
  edit: '修改项目',
  budget_add: '资金变更申请',
  budget_edit: '修改资金申请',
  budget_move: '资金迁移',
  '': '',
};
/** 新增修改/申请资金项目表单项 */
const ADD_FORM = [
  {
    key: 'itemName',
    label: '项目名称',
    type: 'input',
  },
  {
    key: 'itemId',
    label: '项目id',
    disabled: true,
    type: 'input',
  },
  {
    key: 'arrivalDate',
    label: '到账/提现日期',
    type: 'date',
  },
  {
    key: 'paySerial',
    label: '到账流水号',
    type: 'input',
    placeholder: '项目金额提现时无需填写',
  },
  {
    key: 'applyAmount',
    label: '申请金额',
    type: 'input',
    placeholder: '如需要进行提现，则金额输入时需要输入负号',
  },

  {
    key: 'siName',
    label: '申请人',
    type: 'input',
    placeholder: '请填写申请人',
  },
  {
    key: 'remark',
    label: '备注',
    type: 'textarea',
    placeholder: '请输入备注信息',
  },
];
// 定义申请人部门
const ADD_FORM_WITH_SI = [
  {
    key: 'depId',
    label: '申请人部门',
    type: 'depId',
    placeholder: '请填写申请人部门',
  },
  {
    key: 'siDepartmentAdmin',
    label: '申请人部门负责人',
    type: 'input',
    placeholder: '请填写申请人部门负责人',
    disabled: true,
  },
  {
    key: 'reserveRatio',
    label: '预留金比例',
    type: 'input',
    placeholder: '请填写预留金比例，填写的数字在0到1内',
  },
  {
    key: 'warningRatio',
    label: '余额告警比例',
    type: 'input',
    placeholder: '请填写余额告警比例，填写的数字在0到1内',
  },
  {
    key: 'warningMember',
    label: '告警人员',
    type: 'warningMember',
    placeholder: '请填写告警人员,请填写邮箱，分号分隔',
  },
  {
    key: 'toleranceRatio',
    label: '兜底比例',
    type: 'input',
    placeholder: '请填写兜底比例，填写的数字在0到1内',
  },
];
/** 资金审核状态表*/
enum BUDGET_STATUS {
  审核中,
  审核通过,
  审核失败,
}
/** 默认表单项 */
const DEFAULT_ADD_KEYS = [
  'itemName',
  'arrivalDate',
  'paySerial',
  'applyAmount',
];
export const ACTIVITIES_PARAMS = {
  OTHER_DETAIL_TITLE,
  PROJECT_COLUMNS,
  ACTIVITIES_DETAIL_COLUMNS,
  PORTION_DETAIL_COLUMNS,
  APPLY_COLUMNS,
  ADD_MODAL_TITLE,
  ADD_FORM,
  ADD_FORM_WITH_SI,
  BUDGET_STATUS,
  DEFAULT_ADD_KEYS,
};

// 优惠券核销查询
const RED_QUERY_COLUNMS = [
  {
    dataIndex: 'item_name',
    key: 'item_name',
    title: '项目名称',
  },
  {
    dataIndex: 'item_id',
    key: 'item_id',
    title: '项目ID',
  },
  {
    dataIndex: 'used_amount',
    key: 'used_amount',
    title: '优惠券已核销金额',
  },
  {
    dataIndex: 'useDetail',
    key: 'useDetail',
    title: '优惠券已核销名单',
    slotName: 'detail',
  },
  {
    dataIndex: 'fund_shares_used_amount',
    key: 'fund_shares_used_amount',
    title: '已发放份额金额',
  },
  {
    dataIndex: 'portionDetail',
    key: 'portionDetail',
    title: '已发放份额名单',
    slotName: 'portionDetail',
  },
  {
    dataIndex: 'total_used_amount',
    key: 'total_used_amount',
    title: '项目总核销金额',
  },
];
export const RED_QUERY_PARAMS = {
  RED_QUERY_COLUNMS,
};

// 券模板
/** 券模板审核状态 */
export const couponVerifyList = [
  {
    label: '审核中',
    value: '0',
  },
  {
    label: '审核通过',
    value: '1',
  },
  {
    label: '审核失败',
    value: '2',
  },
];
/** 券模板审核状态表 */
export const couponVerifyMap = {};
couponVerifyList.forEach(item => {
  couponVerifyMap[item.value] = item.label;
});
/** 券模板状态 */
export const couponTemplateStatusList = [
  {
    label: '审核中',
    value: '-1',
  },
  {
    label: '有效',
    value: '0',
  },
  {
    label: '暂停',
    value: '1',
  },
  {
    label: '中止',
    value: '2',
  },
];
/** 券模板状态表 */
export const couponTemplateStatusMap = {};
couponTemplateStatusList.forEach(item => {
  couponTemplateStatusMap[item.value] = item.label;
});
/** 券模板详情页 - 券模板类型列表 */
export const couponTypeList = [
  {
    label: '满减券',
    value: '1',
  },
];
/** 券模板列表页列表项 */
export const COUPON_TEMPLATE_LIST_COLUMNS: TableColumn[] = [
  {
    dataIndex: 'project_id',
    title: '项目ID',
    width: 80,
  },
  {
    dataIndex: 'project_name',
    title: '项目名称',
  },
  {
    dataIndex: 'template_id',
    title: '券模板ID',
    width: 100,
  },
  {
    dataIndex: 'template_name',
    title: '券模板名称',
    width: 160,
  },
  {
    dataIndex: 'template_type',
    title: '类型',
    slotName: 'couponType',
  },
  {
    dataIndex: 'template_rule',
    title: '具体规则',
    width: 220,
  },
  {
    dataIndex: 'template_status',
    title: '状态',
    slotName: 'couponTemplateStatus',
  },
  {
    dataIndex: 'audit_status',
    title: '审核状态',
    slotName: 'couponVerifyStatus',
  },
  {
    dataIndex: 'put_in_qty',
    title: '投放总量',
    slotName: 'put_in_qty',
  },
  {
    dataIndex: 'residue_receive_qty',
    title: '剩余可领取数',
    width: 140,
  },
  {
    dataIndex: 'receive_qty',
    title: '领取数',
  },
  {
    dataIndex: 'used_qty',
    title: '使用数',
  },
  {
    dataIndex: 'expire_qty',
    title: '到期数',
  },
  {
    dataIndex: 'used_rate',
    title: '使用率',
  },
  {
    dataIndex: 'used_amount',
    title: '已使用金额',
    width: 120,
  },
  {
    dataIndex: 'used_volume',
    title: '优惠券带来销量',
    width: 160,
  },
  {
    dataIndex: 'remark',
    title: '备注',
    slotName: 'textarea',
    width: 200,
  },
  {
    dataIndex: 'apply_audit_text',
    title: '修改记录',
    slotName: 'textarea',
    width: 200,
  },
  {
    dataIndex: 'option',
    title: '操作',
    slotName: 'option',
    width: 200,
    fixed: 'right',
  },
];

// 券活动
/** 明细映射表 */
export const detailTypeMap = {
  receive_qty: 'receive',
  used_qty: 'use',
};
/** 券活动状态 */
export const couponStatusList = [
  {
    label: '已过期',
    value: '-1',
  },
  {
    label: '发放中',
    value: '0',
  },
  {
    label: '暂停',
    value: '1',
  },
  {
    label: '中止',
    value: '2',
  },
];
/** 券活动状态表 */
export const couponStatusMap = {};
couponStatusList.forEach(item => {
  couponStatusMap[item.value] = item.label;
});
/** 券活动列表页列表项 */
export const COUPON_LIST_COLUMNS: TableColumn[] = [
  {
    dataIndex: 'template_id',
    title: '券模板ID',
  },
  {
    dataIndex: 'template_name',
    title: '券模板名称',
    width: 160,
  },
  {
    dataIndex: 'id',
    title: '优惠券ID',
  },
  {
    dataIndex: 'coupon_name',
    title: '券活动名称',
    width: 200,
  },
  {
    dataIndex: 'coupon_type',
    title: '类型',
    slotName: 'couponType',
  },
  {
    dataIndex: 'coupon_description_name',
    title: '具体规则',
    width: 160,
  },
  {
    dataIndex: 'status',
    title: '状态',
    slotName: 'couponStatus',
  },
  {
    dataIndex: 'review',
    title: '审核状态',
    slotName: 'couponVerifyStatus',
  },
  {
    dataIndex: 'num',
    title: '投放总量',
    slotName: 'num',
  },
  {
    dataIndex: 'residue_receive_qty',
    title: '剩余可领取数',
    width: 140,
  },
  {
    dataIndex: 'receive_qty',
    title: '领取数',
    slotName: 'detail',
  },
  {
    dataIndex: 'used_qty',
    title: '使用数',
    slotName: 'detail',
  },
  {
    dataIndex: 'expire_qty',
    title: '到期数',
  },
  {
    dataIndex: 'used_rate',
    title: '使用率',
  },
  {
    dataIndex: 'write_off_amount',
    title: '已使用金额',
    width: 120,
  },
  {
    dataIndex: 'used_volume',
    title: '优惠券带来销量',
    width: 160,
  },
  {
    dataIndex: 'remark',
    title: '备注',
    width: 200,
    slotName: 'textarea',
  },
  {
    dataIndex: 'apply_audit_text',
    title: '修改记录',
    width: 200,
    slotName: 'textarea',
  },
  {
    dataIndex: 'option',
    title: '操作',
    width: 300,
    slotName: 'option',
    fixed: 'right',
  },
];
export const requiredRule = [{ required: true }];
export const numberRules = [
  {
    required: true,
    pattern: /^(0|[1-9]\d*)(\.\d+)?$/,
    message: '请输入数字！',
  },
];
export const intRules = [
  {
    required: true,
    pattern: /^(0|[1-9]\d*)?$/,
    message: '请输入自然数！',
  },
];
export const plusIntRules = [
  {
    required: true,
    pattern: /^([1-9]\d*)?$/,
    message: '请输入正整数！',
  },
];
export const fundCodesRule = [
  {
    pattern: /^[0-9a-zA-Z]{6}(,[0-9a-zA-Z]{6})*$/,
    message: '输入的基金代码不符合规则',
  },
];
// 券模板详情页表单
/** 普通页面的头部表单数组 */
export const COUPON_TEMPLATE_DETAIL_FORM_HEADER: couponFormItem[] = [
  {
    label: '项目名称',
    name: 'project_id',
    rules: requiredRule,
    slotName: 'select',
    placeholder: '请选择项目名称',
    editPartDisabled: true,
    showSearch: true,
    filterOption: (value: string, option: OptionItem) => {
      return (
        option.label.indexOf(value) > -1 ||
        option.value.toString().indexOf(value) > -1
      );
    },
    options: (form, { projectList }) => projectList,
  },
  {
    label: '券模板名称',
    name: 'template_name',
    rules: requiredRule,
    slotName: 'input',
    placeholder: '请输入券模板名称',
  },
  {
    label: '券模板类型',
    name: 'template_type',
    rules: requiredRule,
    slotName: 'select',
    placeholder: '请选择券模板类型',
    editPartDisabled: true,
    options: couponTypeList,
  },
];
/** input形式收集项目id */
export const COUPON_TEMPLATE_DETAIL_PROJECT_ID_INPUT: couponFormItem = {
  label: '项目ID',
  name: 'project_id',
  rules: requiredRule,
  slotName: 'input',
  placeholder: '请输入项目ID',
  editPartDisabled: true,
};
/** 满减 */
export const COUPON_TEMPLATE_DETAIL_FORM_MJ: couponFormItem[] = [
  {
    label: '具体规则',
    rules: requiredRule,
    slotName: 'rule_mj',
    editPartDisabled: true,
  },
];
/** 折扣 */
export const COUPON_TEMPLATE_DETAIL_FORM_ZK: couponFormItem[] = [
  {
    label: '折扣力度',
    rules: requiredRule,
    slotName: 'rule_zk',
    editPartDisabled: true,
  },
  {
    label: '使用次数',
    slotName: 'usedNum',
    editPartDisabled: true,
    rules: requiredRule,
  },
];
/** 投放数量 */
export const COUPON_TEMPLATE_DETAIL_FORM_PUT_NUM: couponFormItem = {
  label: '投放总量',
  name: 'put_in_qty',
  rules: intRules,
  placeholder: '请输入投放总量',
  slotName: 'input',
  editPartDisabled: true,
  maxlength: 9,
};
/** 通用footer */
export const COUPON_TEMPLATE_DETAIL_FORM_FOOTER: couponFormItem[] = [
  COUPON_TEMPLATE_DETAIL_FORM_PUT_NUM,
  {
    label: '投放数修改',
    name: 'edit_num_symbol',
    placeholder: '请选择新增/减少',
    rules: requiredRule,
    slotName: 'select',
    options: [
      {
        label: '新增',
        value: '0',
      },
      {
        label: '减少',
        value: '1',
      },
    ],
    show: (form, { mode }) => mode === 'editPart',
  },
  {
    label: '修改的投放数量',
    name: 'edit_num',
    placeholder: '请输入投放数量',
    rules: intRules,
    slotName: 'input',
    show: (form, { mode }) => mode === 'editPart',
  },
  {
    label: '修改理由',
    name: 'apply_audit_text',
    placeholder: '请说明修改投放数量理由',
    rules: requiredRule,
    slotName: 'textarea',
    show: (form, { mode }) => mode === 'editPart',
  },
  {
    label: '备注',
    name: 'remark',
    placeholder: '请输入备注',
    slotName: 'textarea',
  },
];
export const FUNDTYPE_LIST = [
  {
    label: '货币型',
    value: '0',
  },
  {
    label: '债券型',
    value: '1',
  },
  {
    label: '混合型',
    value: '2',
  },
  {
    label: '股票型',
    value: '3',
  },
  {
    label: '大集合',
    value: '4',
  },
  {
    label: '小集合',
    value: '5',
  },
  {
    label: '专户',
    value: '6',
  },
  {
    label: '组合',
    value: '7',
  },
];
export const FUNDFATHERTYPE_LIST = [
  {
    label: '公募基金',
    value: '01',
  },
  {
    label: '私募资管',
    value: '02',
  },
  {
    label: '私募基金',
    value: '03',
  },
];
// 券活动详情页表单
/** 第一列 */
export const COUPON_DETAIL_FORM_1: couponFormItem[] = [
  // 红包关联信息
  {
    template: 'title',
    title: '红包关联信息',
  },
  {
    label: '券模板',
    name: 'template_id',
    rules: requiredRule,
    placeholder: '请选择券模板',
    editPartDisabled: true,
    showSearch: true,
    slotName: 'select',
    filterOption: false,
    options: (form, { couponTemplateList }) => couponTemplateList,
    onSearch: (value, fetch) => fetch(value),
  },
  {
    label: '优惠券名称',
    name: 'coupon_name',
    rules: requiredRule,
    placeholder: '请输入优惠券名称',
    slotName: 'input',
  },
  // 优惠方式
  {
    template: 'title',
    title: '优惠方式',
  },
  // 满减券
  {
    label: '具体规则',
    rules: requiredRule,
    slotName: 'rule_mj',
    wrapperCol: {
      span: 24,
    },
    show: (form: couponTableData) => form.coupon_type === '1',
  },
  // 折扣券
  {
    label: '折扣',
    name: 'sub_rate',
    rules: numberRules,
    slotName: 'rule_zk',
    show: (form: couponTableData) => form.coupon_type === '2',
  },
  {
    label: '使用次数',
    rules: requiredRule,
    slotName: 'usedNum',
    show: (form: couponTableData) => form.coupon_type === '2',
  },
  // 红包关联信息
  {
    template: 'title',
    title: '红包关联信息',
  },
  {
    label: '到期方式',
    name: 'time_type',
    slotName: 'radio',
    rules: requiredRule,
    editPartDisabled: true,
    options: [
      {
        label: '绝对时间',
        value: '1',
      },
      {
        label: '相对时间',
        value: '2',
      },
    ],
  },
  {
    label: '选择日期',
    name: 'validTime',
    slotName: 'timeRange',
    rules: requiredRule,
    editPartDisabled: true,
    wrapperCol: { span: 16 },
    style: {
      width: '360px',
    },
    show: (form: couponTableData) => form.time_type === '1',
  },
  {
    label: '有效时间',
    name: 'time_hours',
    slotName: 'input',
    rules: plusIntRules,
    editPartDisabled: true,
    addonAfter: '自用户领取起多少小时',
    show: (form: couponTableData) => form.time_type === '2',
  },
  {
    label: '投放数量',
    name: 'num',
    rules: [
      {
        required: true,
        pattern: /^([1-9]\d{0,10})?$/,
        message: '请输入正确的投放数量',
      },
    ],
    labelCol: { span: 4 },
    slotName: 'input',
    editPartDisabled: true,
    placeholder: '请输入投放数量',
    maxlength: 9,
  },
  {
    label: '投放数修改',
    name: 'edit_num_symbol',
    placeholder: '请选择新增/减少',
    labelCol: { span: 4 },
    rules: requiredRule,
    slotName: 'select',
    options: [
      {
        label: '新增',
        value: '0',
      },
      {
        label: '减少',
        value: '1',
      },
    ],
    show: (form, { mode }) => mode === 'editPart',
  },
  {
    label: '修改的投放数量',
    name: 'edit_num',
    placeholder: '请输入投放数量',
    labelCol: { span: 4 },
    rules: intRules,
    slotName: 'input',
    maxlength: 9,
    show: (form, { mode }) => mode === 'editPart',
  },
  // 红包关联信息
  {
    template: 'title',
    title: '适用人群',
  },
  {
    slotName: 'crowdFilter',
  },
];
/** 第二列 */
export const COUPON_DETAIL_FORM_2: couponFormItem[] = [
  // 适用基金
  {
    template: 'title',
    title: '适用基金',
  },
  {
    label: '适用对象',
    name: 'object_type',
    slotName: 'radio',
    rules: requiredRule,
    editPartDisabled: true,
    options: [
      {
        label: '基金单品',
        value: '1',
      },
      {
        label: '投顾',
        value: '3',
      },
    ],
  },
  {
    label: '基金公司',
    name: 'fund_manager_code',
    slotName: 'select',
    rules: requiredRule,
    editPartDisabled: true,
    placeholder: '请选择基金公司',
    showSearch: true,
    options: (form, { companyList }) => companyList,
    filterOption: (value: string, option: OptionItem) => {
      return option.label.indexOf(value) > -1;
    },
    show: (form: couponTableData) =>
      form.object_type === OBJECT_TYPE.FUND_COMPANY,
  },
  {
    label: '适用基金',
    name: 'match_code',
    slotName: 'radio',
    rules: requiredRule,
    editPartDisabled: true,
    options: (form: couponTableData) => {
      const options = [
        {
          label: '全部',
          value: '2',
        },
        {
          label: '按类型选择',
          value: '3',
        },
        {
          label: '手动选择',
          value: '1',
        },
      ];
      if (form.object_type === OBJECT_TYPE.FUND_COMPANY) return options;
      else return [options[0], options[2]];
    },
  },

  {
    label: '基金类型',
    name: 'code_list',
    slotName: 'checkbox',
    rules: requiredRule,
    editPartDisabled: true,
    wrapperCol: { span: 18 },
    options: FUNDTYPE_LIST,
    show: (form: couponTableData) =>
      form.object_type === '1' && form.match_code === '3',
  },
  {
    label: '交易方式',
    name: 'trade_type',
    slotName: 'checkbox',
    rules: requiredRule,
    editPartDisabled: true,
    options: [
      {
        label: '申购',
        value: '1',
      },
      {
        label: '认购',
        value: '2',
      },
      {
        label: '定投',
        value: '3',
      },
    ],
    show: (form: couponTableData) =>
      form.object_type === OBJECT_TYPE.FUND_COMPANY &&
      form.match_code === MATCH_CODE.BY_TYPE,
  },
  {
    label: state =>
      state.editForm.object_type === OBJECT_TYPE.TG ? '投顾代码' : '选择基金',
    name: 'code_list',
    slotName: 'textarea',
    rules: [
      ...requiredRule,
      {
        pattern: /^[\da-zA-Z,]*$/,
        message: '请输入正确的格式！',
      },
    ],
    editPartDisabled: true,
    placeholder: '请输入基金/组合/投顾代码，多个用英文逗号隔开',
    show: (form: couponTableData) => form.match_code === MATCH_CODE.MANUAL,
  },
  // 选择红包组
  {
    template: 'title',
    title: '选择红包组',
  },
  {
    label: '选择组',
    name: 'group_id',
    slotName: 'couponGroup',
    editPartDisabled: true,
    options: (form, { couponGroupList }) => couponGroupList,
  },
  // 优惠券角标展示
  {
    template: 'title',
    title: '优惠券角标展示',
  },
  {
    label: '角标',
    name: 'mark_text',
    slotName: 'input',
    editPartDisabled: true,
    placeholder: '此优惠券设定为权益卡，则请输入角标需要展示的文案',
    wrapperCol: { span: 14 },
  },
  // 立即使用跳转地址
  {
    template: 'title',
    title: '立即使用跳转地址',
  },
  {
    label: '跳转地址',
    name: 'jump_url',
    slotName: 'input',
    editPartDisabled: true,
    placeholder: '请输入要跳转的地址',
    rules: [
      {
        pattern: /^http(s)?:\/\/[^ ]+$/,
        message: '请输入正确链接',
      },
    ],
  },
  {
    label: '备注',
    name: 'remark',
    slotName: 'textarea',
    placeholder: '请输入备注',
  },
];

// 各明细页
// 领取明细页
/** 领取明细列表 */
const RECEIVE_DETAIL_COLUMNS = [
  {
    dataIndex: 'template_id',
    title: '券模板id',
    width: '100px',
  },
  {
    dataIndex: 'coupon_id',
    title: '券活动id',
    width: '100px',
  },
  {
    dataIndex: 'receive_time_str',
    title: '领取时间',
  },
  {
    dataIndex: 'cust_name',
    title: '用户姓名',
  },
  {
    dataIndex: 'account_id',
    title: '用户账号',
    slotName: 'user',
  },
];
/** 使用明细列表 */
const USE_DETAIL_COLUMNS = function (ifUsed = false) {
  return [
    {
      dataIndex: 'template_id',
      title: '券模板id',
      width: '100px',
    },
    {
      dataIndex: 'coupon_id',
      title: '券活动id',
      width: '100px',
    },
    {
      dataIndex: 'vc_accepttime',
      title: ifUsed ? '确认日期' : '下单日期',
      width: '170px',
      slotName: 'date',
    },
    {
      dataIndex: 'vc_appsheetserialno',
      title: ifUsed ? 'TA确认单号' : '订单号',
      width: '110px',
    },
    {
      dataIndex: 'cust_name',
      title: '用户姓名',
      width: '90px',
    },
    {
      dataIndex: 'cust_id',
      title: '客户号',
      slotName: 'user',
      width: '130px',
    },
    {
      dataIndex: 'cust_i_card',
      title: '身份证',
      width: '150px',
    },
    {
      dataIndex: 'fund_name',
      title: '购买基金',
      slotName: 'fund',
      width: '220px',
    },
    {
      dataIndex: 'application_amount',
      title: '申请金额',
    },
    {
      dataIndex: 'real_amount',
      title: '实付金额',
      width: '120px',
    },
    {
      dataIndex: 'nd_discountmaount',
      title: '抵扣金额',
      width: '100px',
    },
    {
      dataIndex: 'coupon_name',
      title: '优惠券名称',
      width: '240px',
    },
    {
      dataIndex: 'redValue',
      title: '优惠券金额',
      slotName: 'redValue',
    },
    {
      dataIndex: 'nd_zero_discount_amount',
      title: '零折权益抵扣',
    },
  ];
};
/** 优惠券明细 - 完整 */
const RED_DETAIL_COLUMNS = [
  {
    dataIndex: 'template_id',
    title: '券模板id',
    width: '100px',
  },
  {
    dataIndex: 'coupon_id',
    title: '券活动id',
    width: '100px',
  },
  {
    dataIndex: 'create_time_str',
    title: '优惠券创建时间',
    width: '140px',
  },
  {
    dataIndex: 'coupon_name',
    title: '优惠券名称',
    width: '240px',
  },
  {
    dataIndex: 'sub_money',
    title: '优惠券金额',
    slotName: 'redValue',
    width: '160px',
  },
  {
    dataIndex: 'put_in_qty',
    title: '投放数',
    width: '110px',
  },
  {
    dataIndex: 'residue_receive_qty',
    title: '剩余可领取数',
    width: '120px',
  },
  {
    dataIndex: 'receive_qty',
    title: '领取数',
    width: '100px',
  },
  {
    dataIndex: 'used_qty',
    title: '已使用数',
    width: '100px',
  },
  {
    dataIndex: 'expire_qty',
    title: '到期数',
    width: '100px',
  },
  {
    dataIndex: 'used_rate',
    title: '使用率',
    slotName: 'rate',
    width: '90px',
  },
  {
    dataIndex: 'write_off_amount',
    title: '已使用金额',
    width: '120px',
  },
  {
    dataIndex: 'sales_volume',
    title: '使用优惠券带来销量',
    width: '160px',
  },
  {
    dataIndex: 'activity_name',
    title: '关联活动',
    width: '240px',
  },
  {
    dataIndex: 'useDetail',
    title: '使用明细',
    slotName: 'detail',
  },
];
/** 优惠券明细 - 销售 */
const RED_DETAIL_COLUMNS_SALE = [
  {
    dataIndex: 'template_id',
    key: 'template_id',
    title: '券模板id',
    width: '100px',
  },
  {
    dataIndex: 'coupon_id',
    key: 'coupon_id',
    title: '券活动id',
  },
  {
    dataIndex: 'coupon_name',
    key: 'coupon_name',
    title: '优惠券名称',
  },
  {
    dataIndex: 'sub_money',
    key: 'sub_money',
    title: '优惠券金额',
    slotName: 'redValue',
  },
  {
    dataIndex: 'receive_qty',
    key: 'receive_qty',
    title: '领取数',
  },
  {
    dataIndex: 'used_qty',
    key: 'used_qty',
    title: '已使用数',
  },
  {
    dataIndex: 'write_off_amount',
    key: 'write_off_amount',
    title: '已使用金额',
  },
  {
    dataIndex: 'sales_volume',
    key: 'sales_volume',
    title: '使用优惠券带来销量',
  },
  {
    dataIndex: 'activity_name',
    key: 'activity_name',
    title: '关联活动',
  },
  {
    dataIndex: 'useDetail',
    key: 'useDetail',
    title: '使用明细',
    slotName: 'detail',
  },
];
export const DETAIL_PARAMS = {
  RECEIVE_DETAIL_COLUMNS,
  USE_DETAIL_COLUMNS,
  RED_DETAIL_COLUMNS,
  RED_DETAIL_COLUMNS_SALE,
};

// 通用
/** 明细弹窗配置 */
export const DETAIL_MODAL_CONFIG = {
  red: {
    title: '优惠券明细',
  },
  receive: {
    title: '领取明细',
  },
  use: {
    title: '使用明细',
  },
  '': {
    title: '',
  },
};
/**
 * 明细页维度表
 * @param 1 项目
 * @param 2 活动
 * @param 3 优惠券
 * @param 4 销售
 */
export enum DETAIL_DIMENSION {
  'p_id' = 1,
  'a_id',
  'r_id',
  'si_id',
}
/** 优惠券状态 */
export enum PACKET_STATUS {
  已过期 = -1,
  发放中,
  暂停中,
  终止,
  已结束,
}

// 券黑名单列表页列表项
export const COUPON_BLACKLIST_COLUMNS: TableColumn[] = [
  {
    dataIndex: 'index',
    title: '序列',
    slotName: 'index',
  },
  {
    dataIndex: 'rightType',
    title: '券类型',
    slotName: 'rightType',
  },
  {
    dataIndex: 'nonFunds',
    title: '黑名单基金',
    slotName: 'nonFunds',
  },
  {
    dataIndex: 'remark',
    title: '备注',
    slotName: 'textarea',
  },
  {
    dataIndex: 'auditStatus',
    title: '状态',
    slotName: 'auditStatus',
  },
  {
    dataIndex: 'option',
    title: '操作',
    slotName: 'option',
    fixed: 'right',
  },
];

// 0全部，1满减券，2“0折券”
export const RIGHT_TYPE_LIST = ['全部', '满减券', '0折券'];

export enum PRODUCT_TYPE {
  mutualFund = '01',
  privateEquityManagement = '02',
  privateOfferingFund = '03',
}

export enum INVEST_GOAL_TYPE {
  '01' = '股票基金',
  '02' = '债券基金',
  '03' = '货币基金',
  '04' = '混合基金',
}

// 1待审核，2已审核，3已暂停，4中止，5审核拒绝
export const AUDIT_STATUS = {
  1: '待审核',
  2: '已审核',
  3: '已暂停',
  4: '中止',
  5: '审核拒绝',
};

export const fundTypeMapList = [
  {
    key: '03',
    value: '货币型',
  },
  {
    key: '02',
    value: '债券型',
  },
  {
    key: '04',
    value: '混合型',
  },
  {
    key: '01',
    value: '股票型',
  },
];

// 审核状态，0全部，1审核中，2审核通过，3已暂停，4已中止，5审核拒绝
export enum AUDIT_STATUS_NUM {
  all = 0,
  pending = 1,
  approved = 2,
  paused = 3,
  aborted = 4,
  refuse = 5,
}

// 黑名单详情页模式，分为新增、修改和编辑
export enum BLACKLIST_DETAIL_MODE {
  ADD = 'add',
  EDIT = 'edit',
  SCAN = 'scan',
}

/** 优惠券投放形式 */
export enum PACKET_PUT_TYPE {
  DIRECT = 'DIRECT',
  ACTIVITY = 'ACTIVITY',
}
/** 标签参数组件类型 */
export enum TAG_COMPONENT_TYPE {
  SELECT = 1,
  INPUT = 0,
}
/** 标签所属业务范围，查询所有标签时适用 */
export enum BIZ_CODE {
  FUND_COUPON = 'fund_coupon',
}
/** 券模板/券活动详情模式 */
export enum COUPON_MODE {
  /** 添加券详情 */
  ADD = 'add',
  /** 编辑没有审核过的券详情 */
  EDIT_ALL = 'editAll',
  /** 编辑审核过的券详情 */
  EDIT_PART = 'editPart',
  /** 查看券详情 */
  SCAN = 'scan',
  /** 无意义的初始值 */
  INIT_VALUE = '',
}
/** 角色权限 */
export enum ROLE_PERMISSION {
  /** 具有审核权限 */
  VERIFY = 'verify',
  /** 没有审核权限，只有操作权限 */
  OPERATE = 'operate',
}
/** 券活动有效时间类型 */
export enum COUPON_TIME_TYPE {
  ABSOLUTE = '1',
  RELATIVE = '2',
}
/** 无效的红包id，使用该参数查询接口，得到的数据为null */
export const INVALID_COUPON_ID = '-1';
/** 无效的黑名单审核id，使用该参数查询接口，得到的数据为null */
export const INVALID_AUDIT_ID = -1;

/** 适用基金 */
export enum MATCH_CODE {
  /** 手动选择 */
  MANUAL = '1',
  /** 全部基金 */
  ALL = '2',
  /** 按类型选择 */
  BY_TYPE = '3',
  /** 投顾全部 */
  TG_ALL = '6',
  /** 投顾组合 */
  TG_GROUP = '7',
}
/** 红包类型 */
export enum COUPON_TYPE {
  /** 满减 */
  MJ = '1',
  /** 购买费率优惠券 */
  ZK = '2',
}
/** 适用对象 */
export enum OBJECT_TYPE {
  /** 基金公司 */
  FUND_COMPANY = '1',
  /** 基金组合 */
  FUND_GROUP = '2',
  /** 投顾 */
  TG = '3',
}
