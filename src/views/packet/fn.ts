import { CalcType } from './type';
import { Dayjs } from 'dayjs';

export function getDetailBody(
  calc: {
    calcType: CalcType;
    calcId: string;
    showDate: boolean;
    download: boolean;
    detailKey?: string;
  },
  date: Dayjs[],
  data = {}
): any {
  const detail_type_map = {
    1: 'project_id',
    2: 'activity_id_list',
    3: 'coupon_id_list',
    4: 'si_id_list',
  };
  const _data: any = {
    type: calc.calcType.toString(),
    source: 'b2c-fund',
    ...data,
  };
  switch (calc.calcType) {
    case 1: {
      _data[detail_type_map[calc.calcType]] = Number(calc.calcId || '');
      break;
    }
    case 2:
    case 3:
    case 4: {
      _data[detail_type_map[calc.calcType]] = [Number(calc.calcId || '')];
      break;
    }
  }
  // 装填时间
  if (calc.showDate) {
    _data.start_time = date[0].format('YYYY-MM-DD') + ' 00:00:00';
    _data.end_time = date[1].format('YYYY-MM-DD') + ' 23:59:59';
  }
  return _data;
}

export function initTable(state: {
  total: number;
  tableData: any[];
}): () => void {
  return function () {
    state.total = 0;
    state.tableData = [];
  };
}

// 数组是否严格递增
export const isStrictltIncrease = (arr: string[]) => {
  if (!arr.length) {
    return false;
  }
  const numArr = arr.map((item) => Number(item));
  if (numArr.length === 1) {
    return true;
  }
  let initialValue = numArr[0];
  for (let i = 1; i < arr.length; i++) {
    if (numArr[i] > initialValue) {
      initialValue = numArr[i];
      continue;
    } else {
      return false;
    }
  }
  return true;
};
