// 红包使用查询

import { Dayjs } from 'dayjs';
import { PACKET_PUT_TYPE, TAG_COMPONENT_TYPE } from './const';

/** 红包使用查询表格数据 */
export type redQueryTableData = {
  item_name: string; // 项目名称
  item_id: string; // 项目ID
  used_amount: string; // 已使用金额
  fund_shares_used_amount: string; // 基金份额实发金额
  total_used_amount: string; // 项目总核销金额
};

export interface ICouponList {
  level: number;
  coupon_limit: string;
  coupon_value: string;
}

// 券模板
/** 券模板列表页列表数据 */
export type couponTemplateTableData = {
  project_id?: string; // 项目ID
  project_name?: string; // 项目名称
  template_id?: number; // 券模板ID
  template_name?: string; // 券模板名称
  template_type?: string; // 券模板类型 1-满减；2-折扣
  coupon_money_list?: ICouponList[]; // 满减规则列表
  sub_rate?: number; // 折扣率
  template_status?: string; // 状态 -1-审核中；0-有效；1-暂停；2-中止
  audit_status?: string; // 审核状态 0-审核中；1-审核通过；2-审核失败
  put_in_qty?: number; // 投放总量
  edit_num_symbol?: string; // 投放数修改
  edit_num?: number; // 修改的投放数量
  residue_receive_qty?: string; // 剩余可领取数
  receive_qty?: string; // 领取数
  frequency?: number; // 使用次数
  limitless?: number | boolean; // 是否无限次使用 0-有限；1-无限
  expire_qty?: string; // 到期数
  used_rate?: string; // 使用率
  used_amount?: string; // 已使用金额
  used_volume?: string; // 优惠券带来销量
  remark?: string; // 备注
  apply_audit_text?: string; // 修改记录
  audit_success?: number; // 是否曾审核通过。0-未曾通过；1-曾通过
};
/** 标签参数类型 */
export type TagParamItem = {
  paramId: string;
  code: string;
  name: string;
  tagId: string; // 关联的标签ID
  displayOrder: number; // 参数的展示顺序，越小展示在前面
  value: string; // 传给后端的真实值
  notNull: boolean; // 是否必填，true必填
  fillType: TAG_COMPONENT_TYPE; // 填写方式，0手填，1下拉选择
  valueDataType: string; // 填写的数据类型，但统一用字符串形式传下来
};
/** 标签可选参数类型 */
export type TagParamOption = {
  tagId: string;
  paramCode: string;
  value: string;
  display: string;
};
/** 红包投放配置类型 */
export type CouponDelivery = {
  scene?: PACKET_PUT_TYPE; // 投放形式：DIRECT直接投放；ACTIVITY活动投放
  crowdId?: string; // 投放人群id
  tagList?: TagListData[]; // 投放人群标签
};

/** 标签接口返回数据类型 */
export type TagListData = {
  tagId: string; // 标签id
  name: string; // 标签名称
  bizCode: string; // 业务范围：fund_coupon
  type: string; // 标签类型：KYC，OLAS_PUSH（olas推送标签），BIZ_DEFINE（业务自定义）
  logicExpression: string; // 逻辑表达式
  paramList: TagParamItem[]; // 标签参数列表
};
/** 券活动列表页列表数据 */
export type couponTableData = {
  template_id?: number | string; // 券模板ID
  template_name?: string; // 券模板名称
  id?: string; // 优惠券ID
  coupon_name?: string; // 券活动名称
  coupon_type?: string; // 类型，1:满减 2:购买费率优惠券
  coupon_description_name?: string; // 具体规则
  group_id?: number; // 券活动组id
  sum_money?: number; // 满减门槛
  sub_money?: number; // 满减金额
  coupon_money_list?: ICouponList[]; // 满减规则列表
  sub_rate?: number; // 折扣率
  status?: string; // 状态; 0：发放中; 1：暂停中; 2：终止; 3：已结束; -1：已过期
  review?: string; // 审核状态; 0：未审核; 1：审核通过; 2：审核拒绝
  num?: number; // 投放数量
  edit_num_symbol?: string; // 投放数修改
  edit_num?: number; // 修改的投放数量
  frequency?: number; // 使用次数
  limitless?: number | boolean; // 是否无限次使用 0-有限；1-无限
  object_type?: string; // 适用对象。1：基金公司; 2：基金组合
  fund_manager_code?: string | string[]; // 适用基金公司
  match_code?: string; // 适用基金。1：手动选择; 2：全部基金; 3：按类型选择
  code_list?: string | string[]; // 基金类型
  trade_type?: string | string[]; // 交易方式; 1:申购; 2:认购
  residue_receive_qty?: string; // 剩余可领取数
  receive_qty?: string; // 领取数
  used_qty?: string; // 使用数
  expire_qty?: string; // 到期数
  time_type?: string; // 到期方式。1-绝对时间；2-相对时间
  validTime?: Dayjs[]; // 绝对时间段
  sdate?: string; // 绝对时间-开始时间; yyyy-MM-dd HH:mm:ss
  edate?: string; // 绝对时间-结束时间; yyyy-MM-dd HH:mm:ss
  time_hours?: number; // 相对时间; 小时数
  used_rate?: string; // 使用率
  write_off_amount?: string; // 已使用金额
  used_volume?: string; // 优惠券带来销量
  mark_text?: string; // 角标文案
  jump_url?: string; // 跳转地址
  remark?: string; // 备注
  apply_audit_text?: string; // 修改记录
  has_review?: number; // 是否曾审核通过。0-未曾通过；1-曾通过
  // 红包投放信息
  delivery?: CouponDelivery;
};
export type formItemType =
  | 'input'
  | 'textarea'
  | 'select'
  | 'radio'
  | 'timeRange'
  | 'slot';
/** 券模板/券活动表单项配置 */
export type couponFormItem = {
  template?: 'title' | 'formItem'; // 模板类型
  title?: string; // 标题内容
  label?: string | ((...args) => string); // 标签
  name?: string; // 表单项name
  rules?: { [x: string]: any }[]; // 校验规则
  style?: { [x: string]: any }; // 样式
  showTime?: boolean; // 是否展示时间
  showSearch?: boolean; // 是否展示搜索
  itemType?: formItemType; // 表单项类型
  placeholder?: string; // 占位符
  addonBefore?: string; // 前缀
  addonAfter?: string; // 后缀
  options?: OptionItem[] | ((...args: any) => OptionItem[]); // 选择项列表
  filterOption?: boolean | ((...args: any) => boolean); // 筛选规则
  autoSize?: { [x: string]: any } | boolean; // 多行文本框size
  editPartDisabled?: boolean; // 在部分编辑下是否可编辑
  slotName?: string; // 插槽名称
  labelCol?: { [x: string]: any }; // 标签宽度
  wrapperCol?: { [x: string]: any }; // 容器宽度
  show?: (...args: any) => boolean; // 配置展示条件，不配置则任何条件都展示
  onSearch?: (value: string, ...args: any) => void; // 文本框变化
  maxlength?: number;
};

// 领取明细
/** 领取明细表格数据 */
export type receiveTableData = {
  template_id?: string; // 券模板id
  coupon_id?: string; // 券活动id
  receive_time_str?: string; // 领取时间
  cust_name?: string; // 用户姓名
  account_id?: string; // 用户账号
  crm_id?: string; // 客户号
};

// 使用明细
/** 使用明细表格数据 */
export type useTableData = {
  vc_accepttime?: string; // 下单日期
  vc_appsheetserialno?: string; // 订单号
  cust_name?: string; // 用户姓名
  cust_id?: string; // 客户号
  crm_id?: string; // 客户销售号
  cust_i_card?: string; // 身份证
  fund_name?: string; // 基金名称
  fund_code?: string; // 基金代码
  real_amount?: string; // 实付金额
  nd_discountmaount?: string; // 抵扣金额
  template_id?: string; // 券模板id
  coupon_name?: string; // 红包名称
  coupon_id?: string; // 券活动id
  sub_money?: string; // 红包金额
  sum_money?: string; // 红包门槛
  applicationAmount?: string; // 申请金额
  ndZeroDiscountAmount?: string; // 零折权益抵扣
};

// 红包明细
/** 红包明细表格数据 */
export type redTableData = {
  create_time?: string; // 红包创建时间
  create_time_str?: string; // 红包创建时间 年月日
  template_id?: string; // 券模板id
  coupon_name?: string; // 红包名称
  coupon_id?: string; // 券活动id
  sub_money?: string; // 红包金额
  sum_money?: string; // 红包门槛
  put_in_qty?: string; // 投放数
  residue_receive_qty?: string; // 剩余可领取数
  receive_qty?: string; // 领取数
  used_qty?: string; // 已使用数
  expire_qty?: string; // 到期数
  used_rate?: string; // 使用率
  write_off_amount?: string; // 已使用金额
  sales_volume?: string; // 使用红包带来销量
  activity_name?: string; // 关联活动
  activity_id?: string; // 关联活动id
};

// 份额明细
/** 基金份额列表项 */
export type PortionDetailTableData = {
  crm_id?: string; // aiId
  create_time?: string; // 发放时间
  activity_id?: string; // 活动id
  activity_name?: string; // 活动名称
  project_id?: string; // 项目id
  project_name?: string; // 项目名称
  user_id?: string; // 用户手炒号
  cust_id?: string; // 用户基金号
  transfer_id?: string; // 转账id
  cust_name?: string; // 用户姓名
  fund_type?: string; // 基金类型
  fund_code?: string; // 发放基金代码
  amount?: string; // 发放金额
  remark?: string; // 备注
};

/** 统计维度 1 项目 2 活动 3 红包 4 销售 */
export type CalcType = 1 | 2 | 3 | 4;


// 券黑名单相关
export interface IRuleItem {
  // 1基金,2基金类型,3费率折扣未打折,4七天内赎回无手续费,5尾随低于多少,6认申购费率折后高于,7申请金额全额上滑,8例外基金
  ruleType: number;
  fundType?: string;
  exceptionalFundCodeListStr?: string;
  fundCodeListStr?: string;
  // 公募基金
  mutualFund?: string[];
  // 私募资管
  privateEquityManagement?: string[];
  // 私募基金
  privateOfferingFund?: string[];
  // 费率折扣未打折是否勾选
  discount?: boolean;
  // 七天无手续费是否勾选
  redemption?: boolean;
  deductibleAmount?: number | '';
  purchaseDiscount?: number | '';
  // 申请金额全额上滑是否勾选
  open?: boolean;
}
export interface IBlacklistDetail {
  operator?: string;
  couponBlackListAuditDTO: {
    // 权益类型，0全部，1满减，2折扣
    rightType: 0 | 1 | 2;
    ruleContentList: IRuleItem[];
  };
  remark: string;
}

export interface IBlacklistItem extends IBlacklistDetail {
  auditId: number;
  auditStatus: number;
}


