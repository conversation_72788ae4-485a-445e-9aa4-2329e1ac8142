<template>
  <div class="m-coupon-list">
    <p class="m-page-title">券活动列表页</p>
    <div class="table-wrapper">
      <div class="filter-wrapper">
        <div class="m-description-item">
          <span>券模板：</span>
          <a-select
            v-model:value="state.couponTemplateId"
            style="width: 200px"
            placeholder="请选择券模板"
            :options="state.couponTemplateList"
            allow-clear
            show-search
            :filter-option="false"
            @search="fetchTemplateList"
          ></a-select>
        </div>
        <a-range-picker v-model:value="state.couponValidTime" />
        <a-button type="primary" @click="handleSearch">查询</a-button>
        <a-button @click="handleAdd">新建券活动</a-button>
      </div>
      <div class="m-table-content">
        <custom-table
          v-model:page-num="table.pageNum"
          v-model:page-size="table.pageSize"
          :table-total="table.total"
          :table-data="table.tableData"
          :columns="COUPON_LIST_COLUMNS"
          :scroll="{ x: 2600, y: 600 }"
          :on-submit="onTableSubmit(fetchTable)"
        >
          <!-- 券活动状态 -->
          <template #couponStatus="{ text }">
            {{ couponStatusMap[text] || '--' }}
          </template>
          <!-- 券活动审核状态 -->
          <template #couponVerifyStatus="{ text }">
            {{ couponVerifyMap[text] || '--' }}
          </template>
          <!-- 优惠券类型 -->
          <template #couponType="{ text }">
            {{ couponTypeMap[text] || '--' }}
          </template>
          <!-- 投放数量 -->
          <template #num="{ record }">
            {{ Number(record.num || 0) + Number(record.edit_num || 0) }}
          </template>
          <!-- 明细查看 -->
          <template #detail="{ text, record, column }">
            <a-button
              type="link"
              :disabled="text !== 0 && !text"
              @click="() => handleDetail(record, column)"
            >
              {{ text || text === 0 ? text : '--' }}
            </a-button>
          </template>
          <!-- 多行文本 -->
          <template #textarea="{ text }">
            <a-textarea :value="text" :disabled="true" />
          </template>
          <!-- 操作 -->
          <template #option="{ record, index }">
            <!-- 各操作是否可点击需根据接口字段（券模板状态）判断 -->
            <template v-if="state.verifyAuth">
              <a-button
                :style="{
                  color: optionsValid(record, 'verify') ? '#40e12d' : '',
                }"
                type="link"
                :disabled="!optionsValid(record, 'verify')"
                @click="handleVerify(true, index)"
              >
                审核通过
              </a-button>
              <a-button
                type="link"
                danger
                :disabled="!optionsValid(record, 'verify')"
                @click="handleRefuse(index)"
              >
                打回
              </a-button>
            </template>
            <a-button
              :style="{
                color: optionsValid(record, 'edit')
                  ? ifEditPart(record)
                    ? 'red'
                    : '#ffd400'
                  : '',
              }"
              :disabled="!optionsValid(record, 'edit')"
              type="link"
              @click="handleEdit(index)"
            >
              修改
            </a-button>
            <a-button
              v-if="optionsValid(record, 'continue')"
              type="link"
              @click="handleContinue(index)"
            >
              继续
            </a-button>
            <a-popconfirm
              v-else
              title="确定暂停吗？"
              ok-text="确定"
              cancel-text="取消"
              :disabled="!optionsValid(record, 'pause')"
              @confirm="handlePause(index)"
            >
              <a-button
                type="link"
                danger
                :disabled="!optionsValid(record, 'pause')"
              >
                暂停
              </a-button>
            </a-popconfirm>
            <a-button type="link" @click="handleScan(index)">查看</a-button>
            <a-popconfirm
              title="确定中止吗？"
              ok-text="确定"
              cancel-text="取消"
              :disabled="!optionsValid(record, 'over')"
              @confirm="handleOver(index)"
            >
              <a-button
                type="link"
                danger
                :disabled="!optionsValid(record, 'over')"
              >
                中止
              </a-button>
            </a-popconfirm>
          </template>
        </custom-table>
      </div>
    </div>
    <div class="m-page-modal">
      <!-- 打回弹窗 -->
      <a-modal
        :visible="state.refuseIndex > -1"
        title="请输入打回理由"
        @cancel="() => (state.refuseIndex = -1)"
      >
        <!-- 页脚 -->
        <template #footer>
          <a-button @click="() => (state.refuseIndex = -1)">取消</a-button>
          <a-button
            type="primary"
            :loading="store.state.loading"
            @click="handleVerify(false, state.refuseIndex)"
          >
            确认
          </a-button>
        </template>
        <!-- 窗体 -->
        <a-textarea
          v-model:value="state.refuseReason"
          :rows="6"
          show-count
          :maxlength="100"
        />
      </a-modal>
      <!-- 明细页弹窗 -->
      <a-modal
        v-model:visible="detail.detailVisible"
        :title="DETAIL_MODAL_CONFIG[detail.detailType].title"
        wrap-class-name="full-modal"
        width="96%"
        :footer="null"
        style="top: 20px"
      >
        <receive-detail
          v-if="detail.detailType === 'receive'"
          component-type="component"
          :calc-type="3"
          :calc-id="detail.detailId"
        />
        <use-detail
          v-if="detail.detailType === 'use'"
          component-type="component"
          :calc-type="3"
          :calc-id="detail.detailId"
        />
      </a-modal>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, reactive } from 'vue';
import { useRouter } from 'vue-router';
import { TableColumnVue } from '@/type/global';
import CustomTable, {
  useCustomTable,
} from '@/components/common/CustomTable.vue';
import { couponTableData } from '../type';
import {
  DETAIL_MODAL_CONFIG,
  COUPON_LIST_COLUMNS,
  couponStatusMap,
  couponVerifyMap,
  couponTypeList,
  detailTypeMap,
  COUPON_MODE,
  ROLE_PERMISSION,
  INVALID_COUPON_ID
} from '../const';
import store from '@/store/index';
import { getLastMonth } from '@/utils/fn';
import { OptionItemVue } from '@/type/global';
import dayjs, { Dayjs } from 'dayjs';
import ajax from '@/server/http';
import { debounce } from 'lodash';
import { message } from 'ant-design-vue';
import { fetchUserAdmin } from '@/server/api';
import authResource from '@/router/resource';
import useDetail from '@/components/packet/detailHook';
import ReceiveDetail from '../details/ReceiveDetail.vue';
import UseDetail from '../details/UseDetail.vue';
dayjs.locale('zh-CN');

type iState = {
  /** 券模板ID */
  couponTemplateId: string;
  /** 券模板选项列表 */
  couponTemplateList: OptionItemVue[];
  /** 优惠券时间 */
  couponValidTime: Dayjs[];
  /** 操作行索引 */
  optionIndex: number;
  /** 是否展示打回弹窗 */
  refuseIndex: number;
  /** 打回理由 */
  refuseReason: string;
  /** 审核权限 */
  verifyAuth: boolean;
};

// state
let defaultTemplateList: OptionItemVue[] = null;
const { detail, jumpDetail } = useDetail();
const format = 'YYYY-MM-DD HH:mm:ss';
const router = useRouter();
const { table, onTableChange, onTableSubmit } =
  useCustomTable<couponTableData>();
const state = reactive<iState>({
  couponTemplateId: null,
  couponTemplateList: [],
  couponValidTime: getLastMonth() as Dayjs[],
  optionIndex: -1,
  refuseIndex: -1,
  refuseReason: null,
  verifyAuth: false,
});
const couponTypeMap = {};
couponTypeList.forEach((type) => {
  couponTypeMap[type.value] = type.label;
});

// methods
/** 初始化 */
const init = () => {
  fetchTemplateList();
  fetchTable();
  fetchUserVerify();
};
/** 请求表格 */
const fetchTable = async () => {
  // 筛选条件
  const data = {
    template_id: state.couponTemplateId,
    start_date: state.couponValidTime[0].format(format),
    end_date: state.couponValidTime[1].format(format),
  };
  const res = await ajax('fetchCouponList', data, {
    pageNum: table.pageNum,
    pageSize: table.pageSize,
  });
  if (res) {
    table.tableData = res.crmRecord || [];
    table.total = res.total;
  }
};
/** 请求券模板列表 */
const fetchTemplateList = debounce(async (templateName: string = null) => {
  if (defaultTemplateList && !templateName) {
    state.couponTemplateList = defaultTemplateList;
    return;
  }
  const res = await ajax('getCouponTemplate', {
    pageNum: 1,
    pageSize: 50,
    template_name: templateName,
  });
  if (res) {
    state.couponTemplateList = res.record.map((item) => ({
      value: item.template_id,
      label: item.template_name,
      type: item.template_type,
    }));
    if (!defaultTemplateList) {
      defaultTemplateList = state.couponTemplateList;
    }
  }
}, 300);
/** 请求用户券活动审核权限 */
const fetchUserVerify = async () => {
  state.verifyAuth = await fetchUserAdmin(authResource.coupon_list_verify);
};
/** 修改券模板状态/审核状态 */
const postRecord = async (
  type: 'review' | 'modify',
  value: string,
  reason?: string
) => {
  const record: couponTableData = table.tableData[state.optionIndex] || {};
  const res = await ajax('postCouponStatus', {
    id: record.id,
    type,
    value,
    reason,
  });
  if (res) {
    message.success('修改成功');
    onTableChange(fetchTable);
  }
};
/** 审核 */
const postVerify = (verify: boolean) => {
  const reason =  state.refuseReason
  if (!verify) {
    // 审核不通过
    state.refuseIndex = -1;
    state.refuseReason = null;
  }
  // 审核通过
  postRecord('review', verify ? '1' : '2', reason);
};
/** 暂停 */
const postPause = () => {
  postRecord('modify', '1');
};
/** 继续 */
const postContinue = () => {
  postRecord('modify', '0');
};
/** 中止 */
const postOver = () => {
  postRecord('modify', '2');
};
/** 修改状态 */
const ifEditPart = (record: couponTableData) => !!record.has_review;
/** 操作按钮各禁用状态 */
const optionsValid = (
  record: couponTableData,
  type: 'verify' | 'edit' | 'pause' | 'continue' | 'over'
) => {
  switch (type) {
    case 'verify': {
      return record.status === '1' && record.review === '0';
    }
    case 'pause': {
      return record.status === '0' && record.review === '1';
    }
    case 'continue': {
      return record.status === '1' && record.review === '1';
    }
    case 'edit': {
      return record.status === '0' || record.status === '1';
    }
    case 'over': {
      return (
        (record.status === '0' || record.status === '1') &&
        record.review === '1'
      );
    }
    default:
      return true;
  }
};
/** 跳转券模板详情页 */
const jumpCouponDetail = (mode: COUPON_MODE, index?: number | string) => {
  router.push({
    name: 'couponDetail',
    // 编辑模式
    query: {
      mode,
      couponId: mode === COUPON_MODE.ADD ? INVALID_COUPON_ID : table.tableData[index].id,
      auth: state.verifyAuth ? ROLE_PERMISSION.VERIFY : ROLE_PERMISSION.OPERATE,
    },
  });
};

// controllers
/** 查询表格 */
const handleSearch = () => {
  onTableChange(fetchTable);
};
/** 新增券模板 */
const handleAdd = () => {
  jumpCouponDetail(COUPON_MODE.ADD);
};
// 操作
/** 修改 */
const handleEdit = (index: number) => {
  state.optionIndex = index;
  const part = table.tableData[index].has_review;
  const mode: COUPON_MODE = part ? COUPON_MODE.EDIT_PART : COUPON_MODE.EDIT_ALL;
  // 判断是全部可编辑/局部可编辑
  jumpCouponDetail(mode, index);
};
/** 暂停 */
const handlePause = (index: number) => {
  state.optionIndex = index;
  postPause();
};
/** 继续 */
const handleContinue = (index: number) => {
  state.optionIndex = index;
  postContinue();
};
/** 查看 */
const handleScan = (index: number) => {
  state.optionIndex = index;
  jumpCouponDetail(COUPON_MODE.SCAN, index);
};
/** 中止 */
const handleOver = (index: number) => {
  state.optionIndex = index;
  postOver();
};
/** 打回 */
const handleRefuse = (index: number) => {
  state.optionIndex = index;
  state.refuseIndex = index;
};
/** 审核 */
const handleVerify = (verify: boolean, index: number) => {
  if (verify) {
    state.optionIndex = index;
  }
  postVerify(verify);
};
/** 查看明细 */
const handleDetail = (record: couponTableData, col: TableColumnVue) => {
  const type = detailTypeMap[col.dataIndex as string];
  jumpDetail(type, 3, record.id);
};

onMounted(init);
</script>

<style lang="less">
@import '../index.less';
</style>
