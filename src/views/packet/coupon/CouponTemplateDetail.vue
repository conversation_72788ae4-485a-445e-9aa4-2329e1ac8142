<template>
  <div class="m-coupon-template-detail" style="width: 60%">
    <p class="m-page-title">券模板详情</p>
    <div class="m-form-wrapper">
      <a-form
        ref="formRef"
        name="couponTemplateDetailForm"
        :model="state.editForm"
        label-align="left"
        :label-col="{ span: 8 }"
        :wrapper-col="{ span: 16 }"
        @finish="handleFinish"
      >
        <div class="m-form-content">
          <div
            v-for="(content, contentIndex) in renderFormContent()"
            :key="contentIndex"
          >
            <template v-for="(item, index) in content" :key="index">
              <span
                v-if="item.show && !item.show(state.editForm, state)"
              ></span>
              <form-item-input
                v-else
                v-model:model-value="state.editForm[item.name]"
                :label="(item.label as string)"
                :name="item.name"
                :rules="item.rules"
                :auto-size="item.autoSize"
                :label-col="item.labelCol"
                :wrapper-col="item.wrapperCol"
                :slot-name="item.slotName"
              >
                <template #input>
                  <a-input
                    v-model:value="state.editForm[item.name]"
                    :disabled="formItemDisabled(item)"
                    :placeholder="item.placeholder"
                    :addon-before="item.addonBefore"
                    :addon-after="item.addonAfter"
                    :maxlength="item.maxlength"
                    allow-clear
                  />
                </template>
                <template #select>
                  <a-select
                    v-model:value="state.editForm[item.name]"
                    :disabled="formItemDisabled(item)"
                    :placeholder="item.placeholder"
                    allow-clear
                    :show-search="item.showSearch"
                    :options="itemOptions(item)"
                    :filter-option="item.filterOption"
                  ></a-select>
                </template>
                <template #textarea>
                  <a-textarea
                    v-model:value="state.editForm[item.name]"
                    :disabled="formItemDisabled(item)"
                    :placeholder="item.placeholder"
                    :auto-size="item.autoSize"
                    allow-clear
                  />
                </template>
                <template #rule_mj>
                  <div
                    v-for="(couponItem, couponIndex) in state.editForm
                      .coupon_money_list"
                    :key="couponIndex"
                    class="m-form-item-custom"
                  >
                    <span>满</span>
                    <a-form-item
                      :name="['coupon_money_list', couponIndex, 'coupon_limit']"
                      :rules="numberRules"
                    >
                      <a-input
                        v-model:value="couponItem.coupon_limit"
                        placeholder="请输入满减门槛金额"
                        allow-clear
                        :disabled="formItemDisabled(item)"
                      />
                    </a-form-item>
                    <span>减</span>
                    <a-form-item
                      :name="['coupon_money_list', couponIndex, 'coupon_value']"
                      :rules="numberRules"
                    >
                      <a-input
                        v-model:value="couponItem.coupon_value"
                        placeholder="请输入满减金额"
                        allow-clear
                        :disabled="formItemDisabled(item)"
                      />
                    </a-form-item>
                  </div>
                  <a-button
                    type="primary"
                    style="margin-right: 20px; margin-top: 20px"
                    :disabled="formItemDisabled(item)"
                    @click="handleAddDiscount"
                  >
                    添加规则
                  </a-button>
                  <a-button
                    :disabled="
                      state.editForm.coupon_money_list.length <= 1 ||
                      formItemDisabled(item)
                    "
                    type="primary"
                    @click="handleDeleteDiscount"
                  >
                    删除规则
                  </a-button>
                </template>
                <template #rule_zk>
                  <div class="m-form-item-custom">
                    <a-form-item name="sub_rate" :rules="numberRules">
                      <a-input
                        v-model:value="state.editForm.sub_rate"
                        style="width: 140px"
                        placeholder="请输入折扣率"
                        allow-clear
                        :disabled="formItemDisabled(item)"
                      />
                    </a-form-item>
                    <span>折</span>
                  </div>
                </template>
                <template #usedNum>
                  <div class="m-form-item-custom">
                    <a-form-item
                      name="frequency"
                      :rules="[{ ...intRules[0], required: true }]"
                      style="width: 140px"
                    >
                      <a-input
                        v-model:value="state.editForm.frequency"
                        placeholder="请输入使用次数"
                        allow-clear
                        :disabled="
                          formItemDisabled(item) || state.editForm.limitless
                        "
                      />
                    </a-form-item>
                    <span>次</span>
                    <a-form-item name="limitless">
                      <a-checkbox
                        v-model:checked="state.editForm.limitless"
                        :disabled="formItemDisabled(item)"
                        @change="handleLimitless"
                      >
                        无限次
                      </a-checkbox>
                    </a-form-item>
                  </div>
                </template>
              </form-item-input>
            </template>
          </div>
          <a-form-item :wrapper-col="{ span: 8 }">
            <!-- 审核页 && 查看 && 审核中 -->
            <template
              v-if="
                state.mode === COUPON_MODE.SCAN &&
                auth === ROLE_PERMISSION.VERIFY &&
                router.currentRoute.value.params.audit_status === '0'
              "
            >
              <a-button
                type="primary"
                :loading="store.state.loading"
                @click="handleVerify(true)"
              >
                审核通过
              </a-button>
              <a-button danger @click="handleRefuse">打回</a-button>
            </template>
            <!-- 这里是券模版的提交按钮 -->
            <a-button
              v-show="state.mode !== COUPON_MODE.SCAN"
              type="primary"
              html-type="submit"
              :loading="store.state.loading"
            >
              提交
            </a-button>
            <a-button @click="handleBack">返回</a-button>
          </a-form-item>
        </div>
      </a-form>
    </div>
    <div class="m-modal-wrapper">
      <a-modal v-model:visible="state.refuseVisible" title="打回理由">
        <!-- 页脚 -->
        <template #footer>
          <a-button @click="() => (state.refuseVisible = false)">取消</a-button>
          <a-button
            type="primary"
            :loading="store.state.loading"
            @click="handleVerify(false)"
          >
            确认
          </a-button>
        </template>
        <!-- 窗体 -->
        <a-textarea
          v-model:value="state.refuseReason"
          :rows="6"
          show-count
          :maxlength="100"
        />
      </a-modal>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, reactive, ref } from 'vue';
import { useRouter } from 'vue-router';
import store from '@/store';
import { couponTemplateTableData, couponFormItem } from '../type';
import {
  numberRules,
  intRules,
  COUPON_TEMPLATE_DETAIL_FORM_HEADER,
  COUPON_TEMPLATE_DETAIL_FORM_MJ,
  COUPON_TEMPLATE_DETAIL_FORM_ZK,
  COUPON_TEMPLATE_DETAIL_FORM_FOOTER,
  COUPON_MODE,
  ROLE_PERMISSION,
} from '../const';
import FormItemInput from '@/components/packet/FormItemInput.vue';
import ajax from '@/server/http';
import { FormInstance, message } from 'ant-design-vue';
import { formatMoney } from '@/utils/fn';
import { CheckboxChangeEvent } from 'ant-design-vue/es/_util/EventInterface';
import { isStrictltIncrease } from '../fn';

type iState = {
  /** 详情类型 */
  mode: COUPON_MODE;
  /** 详情数据 */
  detail: couponTemplateTableData;
  /** 详情编辑表单 */
  editForm: couponTemplateTableData;
  /** 项目列表 */
  projectList: {
    label: string;
    value: string;
  }[];
  /** 是否显示打回弹窗 */
  refuseVisible: boolean;
  /** 打回内容 */
  refuseReason: string;
  /**初始的名字 */
  initName: string;
};

// state
const router = useRouter();
const formRef = ref<FormInstance>();
const auth = router.currentRoute.value.query.auth as ROLE_PERMISSION;
const templateId = Number(router.currentRoute.value.query.templateId as string);
const state = reactive<iState>({
  mode: COUPON_MODE.INIT_VALUE,
  detail: {},
  editForm: {
    coupon_money_list: [
      {
        level: 0,
        coupon_limit: '',
        coupon_value: '',
      },
    ],
  },
  projectList: [],
  refuseVisible: false,
  refuseReason: null,
  initName: '',
});

// methods
const init = () => {
  state.mode = router.currentRoute.value.query.mode as COUPON_MODE;
  fetchProjectList();
  // 以携带详情数据进行初始化
  fetchDetail();
};
/** 请求券模板详情 */
const fetchDetail = async () => {
  if (state.mode === COUPON_MODE.ADD) {
    state.detail = {};
    return;
  }
  const res = await ajax(
    'fetchCouponTemplateDetail',
    {},
    {
      template_id: templateId,
    }
  );
  if (res) {
    state.detail = res;
    // 满减规则只有一条时，返回的 coupon_money_list 为null，需要用 sum_money 和 sub_money 还原 coupon_money_list
    const recoveredMoneyList = [
      {
        level: 0,
        coupon_limit: res.sum_money,
        coupon_value: res.sub_money,
      },
    ];
    state.editForm = {
      ...state.detail,
      coupon_money_list: !res.coupon_money_list?.length ? recoveredMoneyList : res.coupon_money_list,
    };
    state.initName = res.template_name;
    /** 需要特殊处理的字段：折扣率、投放总量、修改数量、是否无限次 */
    state.editForm.put_in_qty =
      Number(state.detail.put_in_qty || 0) + Number(state.detail.edit_num || 0);
    state.editForm.edit_num = null;
    if (state.editForm.template_type === '2') {
      state.editForm.sub_rate = Number(
        formatMoney(state.editForm.sub_rate * 10)
      );
      state.editForm.limitless = !!state.editForm.limitless;
    }
  } else {
    message.error('获取券模板详情失败');
  }
};
/** 请求项目列表 */
const fetchProjectList = async () => {
  const res = await ajax('getProjectListForDropdown');
  if (res) {
    state.projectList = res.map(item => ({
      label: item.select_name,
      value: item.select_id,
    }));
  }
};
/** 提交表单，判断编辑类型，editPart提交项不同 */
const postForm = async (form: couponTemplateTableData) => {
  /** 需要特殊处理的字段：折扣率、投放总量、使用次数无限次 */
  let data: couponTemplateTableData = {};
  switch (state.mode) {
    case COUPON_MODE.ADD: {
      data = form;
      break;
    }
    case COUPON_MODE.EDIT_ALL: {
      data = {
        ...form,
        template_id: templateId,
      };
      break;
    }
    case COUPON_MODE.EDIT_PART: {
      const editNum =
        Number(form.edit_num) * (form.edit_num_symbol === '0' ? 1 : -1);
      data = {
        ...form,
        template_id: templateId,
        put_in_qty: state.detail.put_in_qty,
        edit_num: editNum,
      };
      delete data.edit_num_symbol;
      break;
    }
    default:
      break;
  }
  const checkTemplateNameResult = await ajax(
    'checkCouponTemplateNameIsDuplicate',
    {
      couponTemplateName: state.editForm.template_name,
    }
  );
  if (
    checkTemplateNameResult &&
    state.initName !== state.editForm.template_name
  ) {
    message.error('券模板名称重复');
    return;
  }
  // 特殊字段处理
  if (state.editForm.template_type === '2') {
    data.sub_rate = Number(formatMoney(form.sub_rate / 10, { precision: 3 }));
    data.limitless = Number(form.limitless);
  }
  if (state.editForm.template_type === '1') {
    const couponRulesList = data.coupon_money_list;
    const couponLimitList = couponRulesList.map(item => item.coupon_limit);
    const couponValueList = couponRulesList.map(item => item.coupon_value);
    if (
      !isStrictltIncrease(couponLimitList) ||
      !isStrictltIncrease(couponValueList)
    ) {
      message.error('红包不满足逐级递增');
      return;
    }
    for (let i = 0; i < couponLimitList.length; i++) {
      if (Number(couponLimitList[i]) < Number(couponValueList[i])) {
        message.error('红包使用门槛不能小于红包金额');
        return;
      }
    }
    couponRulesList.forEach((item, index) => {
      item.level = index;
    });
  }
  const res = await ajax(
    `couponTemplate${state.mode[0].toUpperCase()}${state.mode.slice(1)}`,
    data
  );
  if (res) {
    message.success('提交成功');
    handleBack();
  }
};
/** 审核 */
const postVerify = async (verify: boolean) => {
  const res = await ajax('postCouponTemplateStatus', {
    id: templateId,
    type: 'review',
    value: verify ? '1' : '2',
    reason: state.refuseReason,
  });
  if (res) {
    message.success('审核成功');
    setTimeout(() => {
      if (!verify) {
        state.refuseVisible = false;
        state.refuseReason = null;
      }
      handleBack();
    }, 1000);
  }
};
/** 渲染表单块 */
const renderFormContent = () => {
  const formContents = [COUPON_TEMPLATE_DETAIL_FORM_HEADER];
  switch (state.editForm.template_type) {
    case '1':
      formContents.push(COUPON_TEMPLATE_DETAIL_FORM_MJ);
      break;
    case '2':
      formContents.push(COUPON_TEMPLATE_DETAIL_FORM_ZK);
      break;
    default:
      break;
  }
  formContents.push(COUPON_TEMPLATE_DETAIL_FORM_FOOTER);
  return formContents;
};
/** 表单项options */
const itemOptions = (item: couponFormItem) => {
  if (Array.isArray(item.options)) return item.options;
  else return item.options(state.editForm, state);
};
/** 判断表单项是否禁用 */
const formItemDisabled = (item?: couponFormItem) => {
  switch (state.mode) {
    case COUPON_MODE.ADD:
    case COUPON_MODE.EDIT_ALL:
      return false;
    case COUPON_MODE.EDIT_PART:
      return !!item.editPartDisabled;
    case COUPON_MODE.SCAN:
      return true;
    default:
      return true;
  }
};

// controller
/** 提交表单 */
const handleFinish = (value: couponTemplateTableData) => {
  postForm(value);
};
/** 返回上一级 */
const handleBack = () => {
  router.back();
};
/** 审核通过/确认打回 */
const handleVerify = (verify: boolean) => {
  postVerify(verify);
};
/** 打回 */
const handleRefuse = () => {
  state.refuseVisible = true;
};
/** 是否无限次 */
const handleLimitless = (e: CheckboxChangeEvent) => {
  const checked = e.target.checked;
  const maxFrequency = 999999999;
  state.editForm.frequency = checked ? maxFrequency : null;
  formRef.value.validateFields(['frequency']);
};
// 添加满减规则
const handleAddDiscount = () => {
  const couponRulesNum = state.editForm.coupon_money_list.length;
  state.editForm.coupon_money_list.push({
    level: couponRulesNum + 1,
    coupon_limit: '',
    coupon_value: '',
  });
};
// 删除满减规则
const handleDeleteDiscount = () => {
  state.editForm.coupon_money_list.pop();
};

onMounted(init);
</script>

<style lang="less">
@import '../index.less';
</style>
