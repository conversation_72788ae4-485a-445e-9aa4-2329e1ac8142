<template>
  <div :key="auth" class="m-coupon-template-list">
    <p class="m-page-title">
      {{ `券模板${auth === ROLE_PERMISSION.OPERATE ? '列表' : '审核'}页` }}
    </p>
    <div class="table-wrapper">
      <div class="filter-wrapper">
        <div class="m-description-item">
          <span>券模板：</span>
          <a-select
            v-model:value="state.templateName"
            style="width: 200px"
            placeholder="请选择券模板"
            :options="state.couponTemplateList"
            allow-clear
            show-search
            :filter-option="false"
            @search="fetchTemplateList"
          ></a-select>
        </div>
        <div v-show="auth === ROLE_PERMISSION.OPERATE" class="m-description-item">
          <span>审核状态：</span>
          <a-select
            v-model:value="state.couponVerifyStatus"
            style="width: 160px"
            placeholder="请选择审核状态"
            allow-clear
          >
            <a-select-option
              v-for="item in couponVerifyList"
              :key="item.value"
              :value="item.value"
            >
              {{ item.label }}
            </a-select-option>
          </a-select>
        </div>
        <div v-show="auth === ROLE_PERMISSION.OPERATE" class="m-description-item">
          <span>优惠券状态：</span>
          <a-select
            v-model:value="state.couponStatus"
            style="width: 160px"
            placeholder="请选择优惠券状态"
            allow-clear
          >
            <a-select-option
              v-for="item in couponTemplateStatusList"
              :key="item.value"
              :value="item.value"
            >
              {{ item.label }}
            </a-select-option>
          </a-select>
        </div>
        <a-button type="primary" @click="handleSearch">查询</a-button>
        <a-button v-show="auth === ROLE_PERMISSION.OPERATE" @click="handleAdd">
          新建券模板
        </a-button>
      </div>
      <div class="m-table-content">
        <custom-table
          v-model:page-num="table.pageNum"
          v-model:page-size="table.pageSize"
          :table-total="table.total"
          :table-data="table.tableData"
          :columns="COUPON_TEMPLATE_LIST_COLUMNS"
          :scroll="{ x: 2400, y: 600 }"
          :on-submit="onTableSubmit(fetchTable)"
        >
          <!-- 券模板状态 -->
          <template #couponTemplateStatus="{ text }">
            {{ couponTemplateStatusMap[text] || '--' }}
          </template>
          <!-- 券模板审核状态 -->
          <template #couponVerifyStatus="{ text }">
            {{ couponVerifyMap[text] || '--' }}
          </template>
          <!-- 优惠券类型 -->
          <template #couponType="{ text }">
            {{ couponTypeMap[text] || '--' }}
          </template>
          <!-- 投放总量 -->
          <template #put_in_qty="{ record }">
            {{ Number(record.put_in_qty || 0) + Number(record.edit_num || 0) }}
          </template>
          <!-- 多行文本 -->
          <template #textarea="{ text }">
            <a-textarea :value="text" :disabled="true" />
          </template>
          <!-- 操作 -->
          <template #option="{ record, index }">
            <template v-if="auth === ROLE_PERMISSION.VERIFY">
              <a-button
                :style="{ color: record.audit_status !== '0' ? '' : '#40e12d' }"
                type="link"
                :disabled="record.audit_status !== '0'"
                @click="handleVerify(true, index)"
              >
                审核通过
              </a-button>
              <a-button
                type="link"
                danger
                :disabled="record.audit_status !== '0'"
                @click="handleRefuse(index)"
              >
                打回
              </a-button>
              <a-button type="link" @click="handleScan(index)">查看</a-button>
            </template>
            <template v-else>
              <a-button
                :style="{
                  color:
                    record.template_status === '2'
                      ? ''
                      : record.audit_success
                      ? 'red'
                      : '#ffd400',
                }"
                type="link"
                :disabled="record.template_status === '2'"
                @click="handleEdit(index)"
              >
                修改
              </a-button>
              <a-button
                v-if="record.template_status === '1'"
                type="link"
                @click="handleContinue(index)"
              >
                继续
              </a-button>
              <a-popconfirm
                v-else
                title="确定暂停吗？"
                ok-text="确定"
                cancel-text="取消"
                :disabled="record.template_status !== '0'"
                @confirm="handlePause(index)"
              >
                <a-button
                  type="link"
                  danger
                  :disabled="record.template_status !== '0'"
                >
                  暂停
                </a-button>
              </a-popconfirm>
              <a-button type="link" @click="handleScan(index)">查看</a-button>
              <a-popconfirm
                title="确定中止吗？"
                ok-text="确定"
                cancel-text="取消"
                :disabled="
                  record.template_status === '-1' ||
                  record.template_status === '2'
                "
                @confirm="handleOver(index)"
              >
                <a-button
                  type="link"
                  danger
                  :disabled="
                    record.template_status === '-1' ||
                    record.template_status === '2'
                  "
                >
                  中止
                </a-button>
              </a-popconfirm>
            </template>
          </template>
        </custom-table>
      </div>
    </div>
    <div class="m-page-modal">
      <!-- 打回弹窗 -->
      <a-modal
        :visible="state.refuseIndex > -1"
        title="请输入打回理由"
        @cancel="() => (state.refuseIndex = -1)"
      >
        <!-- 页脚 -->
        <template #footer>
          <a-button @click="() => (state.refuseIndex = -1)">取消</a-button>
          <a-button
            type="primary"
            :loading="store.state.loading"
            @click="handleVerify(false, state.refuseIndex)"
          >
            确认
          </a-button>
        </template>
        <!-- 窗体 -->
        <a-textarea
          v-model:value="state.refuseReason"
          :rows="6"
          show-count
          :maxlength="100"
        />
      </a-modal>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, reactive, computed } from 'vue';
import { useRouter } from 'vue-router';
import CustomTable, {
  useCustomTable,
} from '@/components/common/CustomTable.vue';
import { couponTemplateTableData } from '../type';
import {
  COUPON_TEMPLATE_LIST_COLUMNS,
  couponVerifyList,
  couponTemplateStatusList,
  couponVerifyMap,
  couponTemplateStatusMap,
  couponTypeList,
  COUPON_MODE,
  ROLE_PERMISSION,
} from '../const';
import store from '@/store/index';
import ajax from '@/server/http';
import { message } from 'ant-design-vue';
import { OptionItemVue } from '@/type/global';
import { debounce } from 'lodash';

type iState = {
  /** 券模板选项列表 */
  couponTemplateList: OptionItemVue[];
  /** 券模板名称 */
  templateName: string;
  /** 券模板审核状态 */
  couponVerifyStatus: string;
  /** 券模板发放状态 */
  couponStatus: string;
  /** 操作行索引 */
  optionIndex: number;
  /** 是否展示打回弹窗 */
  refuseIndex: number;
  /** 打回理由 */
  refuseReason: string;
};

// state
let defaultTemplateList: OptionItemVue[] = null;
const router = useRouter();
const { table, onTableChange, onTableSubmit } =
  useCustomTable<couponTemplateTableData>();
const state = reactive<iState>({
  couponTemplateList: [],
  templateName: null,
  couponVerifyStatus: null,
  couponStatus: null,
  optionIndex: -1,
  refuseIndex: -1,
  refuseReason: null,
});
const couponTypeMap = {};
couponTypeList.forEach((type) => {
  couponTypeMap[type.value] = type.label;
});

// computed
const auth = computed(() =>
  router.currentRoute.value.name === 'couponTemplateList' ? ROLE_PERMISSION.OPERATE : ROLE_PERMISSION.VERIFY
);

// methods
/** 初始化 */
const init = () => {
  fetchTable();
  fetchTemplateList();
};
/** 请求表格 */
const fetchTable = async () => {
  const data = {
    template_name: state.templateName,
    template_status: state.couponStatus,
    audit_status: state.couponVerifyStatus,
    pageNum: table.pageNum,
    pageSize: table.pageSize,
  };
  const res = await ajax('fetchCouponTemplateTable', data);
  if (res) {
    table.tableData = res.record || [];
    table.total = res.total;
  }
};
/** 请求券模板列表 */
const fetchTemplateList = debounce(async (templateName: string = null) => {
  if (defaultTemplateList && !templateName) {
    state.couponTemplateList = defaultTemplateList;
    return;
  }
  const res = await ajax('getCouponTemplate', {
    pageNum: 1,
    pageSize: 50,
    template_name: templateName,
  });
  if (res) {
    state.couponTemplateList = res.record.map((item) => ({
      value: item.template_name,
      label: item.template_name,
      type: item.template_type,
    }));
    if (!defaultTemplateList) {
      defaultTemplateList = state.couponTemplateList;
    }
  }
}, 300);
/** 修改券模板状态/审核状态 */
const postRecord = async (
  type: 'review' | 'modify',
  value: string,
  reason?: string
) => {
  const record: couponTemplateTableData =
    table.tableData[state.optionIndex] || {};
  const res = await ajax('postCouponTemplateStatus', {
    id: record.template_id,
    type,
    value,
    reason,
  });
  if (res) {
    message.success('修改成功');
    onTableChange(fetchTable);
    return true;
  }
  return false;
};
/** 审核 */
const postVerify = async (verify: boolean) => {
  await postRecord('review', verify ? '1' : '2', state.refuseReason);
  if (!verify) {
    // 审核不通过关闭打回弹窗
    state.refuseIndex = -1;
    state.refuseReason = null;
  }
};
/** 暂停 */
const postPause = () => {
  postRecord('modify', '1');
};
/** 结束暂停 */
const postContinue = () => {
  postRecord('modify', '0');
};
/** 中止 */
const postOver = () => {
  postRecord('modify', '2');
};
/** 跳转券模板详情页 */
const jumpCouponDetail = (mode: COUPON_MODE, index: number | string) => {
  const record: couponTemplateTableData = table.tableData[index];
  console.log('record', record);
  router.push({
    name: 'couponTemplateDetail',
    // 编辑模式
    query: {
      auth: auth.value,
      mode,
      templateId: mode === COUPON_MODE.ADD ? -1 : record.template_id,
    },
    params: {
      audit_status: mode === COUPON_MODE.ADD ? -1 : record.audit_status,
    },
  });
};

// controllers
/** 查询表格 */
const handleSearch = () => {
  onTableChange(fetchTable);
};
/** 新增券模板 */
const handleAdd = () => {
  jumpCouponDetail(COUPON_MODE.ADD, -1);
};
// 操作
/** 修改 */
const handleEdit = (index: number) => {
  state.optionIndex = index;
  const part = table.tableData[index].audit_success;
  const mode: COUPON_MODE = part ? COUPON_MODE.EDIT_PART : COUPON_MODE.EDIT_ALL;
  // 判断是全部可编辑/局部可编辑
  jumpCouponDetail(mode, index);
};
/** 暂停 */
const handlePause = (index: number) => {
  state.optionIndex = index;
  postPause();
};
/** 继续 */
const handleContinue = (index: number) => {
  state.optionIndex = index;
  postContinue();
};
/** 查看 */
const handleScan = (index: number) => {
  state.optionIndex = index;
  jumpCouponDetail(COUPON_MODE.SCAN, index);
};
/** 中止 */
const handleOver = (index: number) => {
  state.optionIndex = index;
  postOver();
};
/** 打回 */
const handleRefuse = (index: number) => {
  state.optionIndex = index;
  state.refuseIndex = index;
};
/** 审核 */
const handleVerify = (verify: boolean, index: number) => {
  if (verify) {
    state.optionIndex = index;
  }
  postVerify(verify);
};

onMounted(init);
</script>

<style lang="less">
@import '../index.less';
</style>
