<template>
  <div class="m-coupon-template-detail" style="width: 30%">
    <p class="m-page-title">券黑名单详情</p>
    <a-form :model="formState" @finish="handleSubmit">
      <a-form-item
        label="券类型"
        :name="['couponBlackListAuditDTO', 'rightType']"
        :rules="requiredRule"
      >
        <a-select
          v-model:value="formState.couponBlackListAuditDTO.rightType"
          :disabled="isDisabled"
          placeholder="请选择券类型"
        >
          <a-select-option :value="1">满减券</a-select-option>
          <a-select-option :value="2">0折券</a-select-option>
          <a-select-option :value="0">全部</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item
        label="例外基金"
        :name="[
          'couponBlackListAuditDTO',
          'ruleContentList',
          7,
          'exceptionalFundCodeListStr',
        ]"
        :rules="fundCodesRule"
      >
        <a-textarea
          v-model:value="
            formState.couponBlackListAuditDTO.ruleContentList[7]
              .exceptionalFundCodeListStr
          "
          :disabled="isDisabled"
          placeholder="请输入基金代码，多个用英文逗号隔开"
        />
      </a-form-item>
      <p class="m-page-subtitle">黑名单基金</p>
      <a-form-item
        label="基金"
        :name="[
          'couponBlackListAuditDTO',
          'ruleContentList',
          0,
          'fundCodeListStr',
        ]"
        :rules="fundCodesRule"
      >
        <a-textarea
          v-model:value="
            formState.couponBlackListAuditDTO.ruleContentList[0].fundCodeListStr
          "
          :disabled="isDisabled"
          placeholder="请输入基金代码，多个用英文逗号隔开"
        />
      </a-form-item>
      <hr />
      <a-form-item
        label="公募基金"
        :name="['couponBlackListAuditDTO', 'ruleContentList', 1, 'mutualFund']"
      >
        <a-checkbox-group
          v-model:value="
            formState.couponBlackListAuditDTO.ruleContentList[1].mutualFund
          "
          :disabled="isDisabled"
        >
          <a-checkbox
            v-for="(item, index) in fundTypeMapList"
            :key="index"
            :value="'01' + item.key"
          >
            {{ item.value }}
          </a-checkbox>
        </a-checkbox-group>
      </a-form-item>
      <a-form-item
        label="私募资管"
        :name="[
          'couponBlackListAuditDTO',
          'ruleContentList',
          1,
          'privateEquityManagement',
        ]"
      >
        <a-checkbox-group
          v-model:value="
            formState.couponBlackListAuditDTO.ruleContentList[1]
              .privateEquityManagement
          "
          :disabled="isDisabled"
        >
          <a-checkbox
            v-for="(item, index) in fundTypeMapList"
            :key="index"
            :value="'02' + item.key"
          >
            {{ item.value }}
          </a-checkbox>
        </a-checkbox-group>
      </a-form-item>
      <a-form-item
        label="私募基金"
        :name="[
          'couponBlackListAuditDTO',
          'ruleContentList',
          1,
          'privateOfferingFund',
        ]"
      >
        <a-checkbox-group
          v-model:value="
            formState.couponBlackListAuditDTO.ruleContentList[1]
              .privateOfferingFund
          "
          :disabled="isDisabled"
        >
          <a-checkbox
            v-for="(item, index) in fundTypeMapList"
            :key="index"
            :value="'03' + item.key"
          >
            {{ item.value }}
          </a-checkbox>
        </a-checkbox-group>
      </a-form-item>
      <hr />
      <a-form-item
        :name="['couponBlackListAuditDTO', 'ruleContentList', 2, 'discount']"
      >
        <a-checkbox
          v-model:checked="
            formState.couponBlackListAuditDTO.ruleContentList[2].discount
          "
          :disabled="isDisabled"
        >
          费率折扣未打折
        </a-checkbox>
      </a-form-item>
      <a-form-item
        :name="['couponBlackListAuditDTO', 'ruleContentList', 3, 'redemption']"
      >
        <a-checkbox
          v-model:checked="
            formState.couponBlackListAuditDTO.ruleContentList[3].redemption
          "
          :disabled="isDisabled"
        >
          七天内赎回无手续费
        </a-checkbox>
      </a-form-item>
      <a-form-item
        label="尾随低于"
        :name="[
          'couponBlackListAuditDTO',
          'ruleContentList',
          4,
          'deductibleAmount',
        ]"
      >
        <a-input-number
          v-model:value="
            formState.couponBlackListAuditDTO.ruleContentList[4]
              .deductibleAmount
          "
          :disabled="isDisabled"
        />
        %
      </a-form-item>
      <a-form-item
        label="认申购费折后高于"
        :name="[
          'couponBlackListAuditDTO',
          'ruleContentList',
          5,
          'purchaseDiscount',
        ]"
      >
        <a-input-number
          v-model:value="
            formState.couponBlackListAuditDTO.ruleContentList[5]
              .purchaseDiscount
          "
          :disabled="isDisabled"
        />
        %
      </a-form-item>
      <a-form-item
        :name="['couponBlackListAuditDTO', 'ruleContentList', 6, 'open']"
      >
        <a-checkbox
          v-model:checked="
            formState.couponBlackListAuditDTO.ruleContentList[6].open
          "
          :disabled="isDisabled"
        >
          申请金额全额上划
        </a-checkbox>
      </a-form-item>
      <a-form-item label="备注" name="remark">
        <a-textarea
          v-model:value="formState.remark"
          :disabled="isDisabled"
          placeholder="请输入备注"
        />
      </a-form-item>
      <a-form-item>
        <a-button
          v-if="!isDisabled"
          type="primary"
          html-type="submit"
          style="margin-right: 20px"
        >
          提交
        </a-button>
        <a-button @click="handleBack">返回</a-button>
      </a-form-item>
    </a-form>
  </div>
</template>

<script lang="ts" setup>
import { computed, reactive, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { useStore } from 'vuex';
import ajax from '@/server/http';
import {
  requiredRule,
  fundCodesRule,
  fundTypeMapList,
  BLACKLIST_DETAIL_MODE,
} from '../const';
import { IBlacklistDetail, IBlacklistItem } from '../type';
import { message } from 'ant-design-vue';

const router = useRouter();
const store = useStore();

const formState = reactive<IBlacklistDetail>({
  couponBlackListAuditDTO: {
    rightType: null,
    ruleContentList: [
      { ruleType: 1, fundCodeListStr: '' },
      { ruleType: 2, fundType: '' },
      { ruleType: 3, discount: false },
      {
        ruleType: 4,
        redemption: false,
      },
      { ruleType: 5, deductibleAmount: '' },
      { ruleType: 6, purchaseDiscount: '' },
      { ruleType: 7, open: false },
      { ruleType: 8, exceptionalFundCodeListStr: '' },
    ],
  },
  remark: '',
});
const isDisabled = computed(() => {
  const mode = router.currentRoute.value.query.mode;
  return mode === BLACKLIST_DETAIL_MODE.SCAN;
});
const userInfo = computed(() => store.state.userInfo);

const handleSubmit = async (values: IBlacklistItem) => {
  const auditId = router.currentRoute.value.query.auditId as string;
  if (!userInfo.value) {
    message.error('请求用户信息失败');
  }
  // 这里主要是为了去除proxy
  const newValues: IBlacklistItem = JSON.parse(JSON.stringify(values));
  if (auditId !== '-1') {
    newValues.auditId = Number(auditId);
  }
  newValues.operator = userInfo.value.userName;
  // 给每一项加上ruleType属性
  newValues.couponBlackListAuditDTO.ruleContentList.forEach((item, index) => {
    item.ruleType = index + 1;
  });
  // 基金类型的表单项需要手动修改数据结构
  const fundTypeRule = newValues.couponBlackListAuditDTO.ruleContentList[1];
  // 三种基金一级产品分类
  const keyArr = [
    'mutualFund',
    'privateEquityManagement',
    'privateOfferingFund',
  ];
  const fundTypeList: string[] = [];
  for (let i = 0; i < keyArr.length; i++) {
    const value: string[] = fundTypeRule[keyArr[i]];
    if (value && value.length) {
      value.forEach(item => fundTypeList.push(item));
    }
    delete fundTypeRule[keyArr[i]];
  }
  fundTypeRule.fundType = fundTypeList.toString();
  const res = (await ajax('saveCouponBlacklistDetail', newValues)) as boolean;
  if (res) {
    router.back();
  } else {
    message.error('修改失败');
  }
};
const handleBack = () => {
  router.back();
};
onMounted(async () => {
  const mode = router.currentRoute.value.query.mode as BLACKLIST_DETAIL_MODE;
  // 查看或编辑时才需要调接口获取详情信息
  if (
    mode === BLACKLIST_DETAIL_MODE.SCAN ||
    mode === BLACKLIST_DETAIL_MODE.EDIT
  ) {
    const res = await ajax('queryCouponBlacklistDetail', {
      auditId: router.currentRoute.value.query.auditId,
    });
    if (res) {
      formState.couponBlackListAuditDTO = res.couponBlackListAuditDTO;
      const mutualFundList: string[] = [];
      const privateEquityManagementList: string[] = [];
      const privateOfferingFundList: string[] = [];
      const fundTypeList =
        formState.couponBlackListAuditDTO.ruleContentList[1].fundType?.split(
          ','
        ) || [];
      fundTypeList.forEach(item => {
        if (item.slice(0, 2) === '01') {
          mutualFundList.push(item);
        }
        if (item.slice(0, 2) === '02') {
          privateEquityManagementList.push(item);
        }
        if (item.slice(0, 2) === '03') {
          privateOfferingFundList.push(item);
        }
      });
      formState.couponBlackListAuditDTO.ruleContentList[1].mutualFund =
        mutualFundList;
      formState.couponBlackListAuditDTO.ruleContentList[1].privateEquityManagement =
        privateEquityManagementList;
      formState.couponBlackListAuditDTO.ruleContentList[1].privateOfferingFund =
        privateOfferingFundList;
      formState.remark = res.remark;
    } else {
      message.error('获取黑名单详情失败');
    }
  }
});
</script>

<style lang="less">
@import '../index.less';
</style>
