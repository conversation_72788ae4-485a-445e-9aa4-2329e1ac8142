<template>
  <div class="m-coupon-template-list">
    <p class="m-page-title">
      {{ `券黑名单${auth === ROLE_PERMISSION.OPERATE ? '列表' : '审核'}页` }}
    </p>
    <div class="table-wrapper">
      <div class="filter-wrapper">
        <div class="m-description-item">
          <span>券类型：</span>
          <a-select
            v-model:value="state.rightType"
            style="width: 150px"
            placeholder="请选择券类型"
            allow-clear
          >
            <a-select-option :value="1">满减券</a-select-option>
            <a-select-option :value="2">0折券</a-select-option>
            <a-select-option :value="0">全部</a-select-option>
          </a-select>
        </div>
        <div class="m-description-item">
          <span>审核状态：</span>
          <a-select
            v-model:value="state.auditStatus"
            style="width: 150px"
            placeholder="请选择审核状态"
            allow-clear
          >
            <a-select-option
              v-for="(item, index) in Object.keys(AUDIT_STATUS)"
              :key="index"
              :value="Number(item)"
            >
              {{ AUDIT_STATUS[item] }}
            </a-select-option>
          </a-select>
        </div>
        <a-button type="primary" @click="handleSearch">查询</a-button>
        <a-button
          v-show="auth === ROLE_PERMISSION.OPERATE"
          @click="
            jumpBlacklistDetail(BLACKLIST_DETAIL_MODE.ADD, INVALID_AUDIT_ID)
          "
        >
          新建黑名单
        </a-button>
      </div>
      <div class="m-table-content">
        <custom-table
          v-model:page-num="table.pageNum"
          v-model:page-size="table.pageSize"
          :table-total="table.total"
          :table-data="table.tableData"
          :columns="COUPON_BLACKLIST_COLUMNS"
          :on-submit="onTableSubmit(fetchTable)"
        >
          <!-- 序列 -->
          <template #index="{ index }">{{ index + 1 }}</template>
          <!-- 券类型 -->
          <template #rightType="{ record }">
            {{ RIGHT_TYPE_LIST[record.couponBlackListAuditDTO.rightType] }}
          </template>
          <!-- 不适用基金 -->
          <template #nonFunds="{ record }">
            <div
              v-for="(item, index) in generateNonFundRules(
                record.couponBlackListAuditDTO.ruleContentList
              )"
              :key="index"
            >
              {{ item }}
            </div>
          </template>
          <!-- 备注 -->
          <template #textarea="{ text }">
            <a-textarea :value="text" :disabled="true" />
          </template>
          <!-- 黑名单当前状态 -->
          <template #auditStatus="{ text }">
            {{ AUDIT_STATUS[text] }}
          </template>
          <!-- 操作 -->
          <template #option="{ record }">
            <!-- 审核页 -->
            <template v-if="auth === ROLE_PERMISSION.VERIFY">
              <a-button
                type="link"
                :disabled="record.auditStatus !== AUDIT_STATUS_NUM.pending"
                @click="handleApprove(record.auditId)"
              >
                审核通过
              </a-button>
              <a-button
                type="link"
                danger
                :disabled="record.auditStatus !== AUDIT_STATUS_NUM.pending"
                @click="handleRefuse(record.auditId)"
              >
                打回
              </a-button>
              <a-button
                type="link"
                @click="
                  jumpBlacklistDetail(
                    BLACKLIST_DETAIL_MODE.SCAN,
                    record.auditId
                  )
                "
              >
                查看详情
              </a-button>
            </template>
            <!-- 列表页 -->
            <template v-else>
              <!-- 已暂停情况下不能修改，其余情况下均能修改 -->
              <a-button
                type="link"
                danger
                :disabled="record.auditStatus === AUDIT_STATUS_NUM.paused"
                @click="
                  jumpBlacklistDetail(
                    BLACKLIST_DETAIL_MODE.EDIT,
                    record.auditId
                  )
                "
              >
                修改
              </a-button>
              <!-- 只有已暂停情况下才有继续按钮 -->
              <a-button
                v-if="record.auditStatus === AUDIT_STATUS_NUM.paused"
                type="link"
                @click="handleContinue(record.auditId)"
              >
                继续
              </a-button>
              <!-- 只有审核通过时才能暂停 -->
              <a-popconfirm
                v-else
                title="确定暂停吗？"
                ok-text="确定"
                cancel-text="取消"
                :disabled="record.auditStatus !== AUDIT_STATUS_NUM.approved"
                @confirm="handlePause(record.auditId)"
              >
                <a-button
                  type="link"
                  danger
                  :disabled="record.auditStatus !== AUDIT_STATUS_NUM.approved"
                >
                  暂停
                </a-button>
              </a-popconfirm>
              <a-button
                type="link"
                @click="
                  jumpBlacklistDetail(
                    BLACKLIST_DETAIL_MODE.SCAN,
                    record.auditId
                  )
                "
              >
                查看
              </a-button>
              <!-- 只有审批通过时才能中止 -->
              <a-popconfirm
                title="确定中止吗？"
                ok-text="确定"
                cancel-text="取消"
                :disabled="record.auditStatus !== AUDIT_STATUS_NUM.approved"
                @confirm="handleOver(record.auditId)"
              >
                <a-button
                  type="link"
                  danger
                  :disabled="record.auditStatus !== AUDIT_STATUS_NUM.approved"
                >
                  中止
                </a-button>
              </a-popconfirm>
            </template>
          </template>
        </custom-table>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, reactive, computed } from 'vue';
import { useRouter } from 'vue-router';
import { useStore } from 'vuex';
import { message } from 'ant-design-vue';
import CustomTable, {
  useCustomTable,
} from '@/components/common/CustomTable.vue';
import { IBlacklistItem, IRuleItem } from '../type';
import {
  COUPON_BLACKLIST_COLUMNS,
  INVEST_GOAL_TYPE,
  PRODUCT_TYPE,
  AUDIT_STATUS,
  AUDIT_STATUS_NUM,
  RIGHT_TYPE_LIST,
  BLACKLIST_DETAIL_MODE,
  ROLE_PERMISSION,
  INVALID_AUDIT_ID,
} from '../const';
import ajax from '@/server/http';

interface IState {
  // 权益类型，0全部，1满减，2折扣
  rightType: 0 | 1 | 2;
  // 审批状态，1待审批，2审批通过，3已暂停，4已中止，5审批拒绝
  auditStatus: 1 | 2 | 3 | 4 | 5;
  userName: string;
}

// state
const router = useRouter();
const store = useStore();
// auth用于判断当前是列表页还是审核页
const auth = computed(() => {
  return router.currentRoute.value.name === 'blacklist'
    ? ROLE_PERMISSION.OPERATE
    : ROLE_PERMISSION.VERIFY;
});
const userInfo = computed(() => store.state.userInfo);
const { table, onTableChange, onTableSubmit } =
  useCustomTable<IBlacklistItem>();
const state = reactive<IState>({
  rightType: null,
  // 审核页默认查询待审核列表，列表页默认查询所有
  auditStatus: auth.value === ROLE_PERMISSION.VERIFY ? 1 : null,
  userName: '',
});

// methods
// 请求表格
const fetchTable = async () => {
  const data = {
    rightType: state.rightType,
    auditStatus: state.auditStatus,
    offset: table.pageNum,
    pageSize: table.pageSize,
  };
  const listInfo = await ajax('queryCouponBlacklistAll', data);
  if (listInfo && userInfo.value) {
    table.tableData = listInfo.blackListAuditDTOList || [];
    table.total = listInfo.count;
    state.userName = userInfo.value.userName;
  } else {
    message.error('请求黑名单列表错误');
  }
};

// 生成黑名单基金规则
const generateNonFundRules = (ruleContentList: IRuleItem[]) => {
  const ruleList: string[] = [];
  // 例外基金
  if (ruleContentList[7]?.exceptionalFundCodeListStr) {
    const exceptionalFundCodeList =
      ruleContentList[7].exceptionalFundCodeListStr.split(',');
    // 3个以上基金加省略号
    if (exceptionalFundCodeList.length > 3) {
      ruleList.push(
        `例外基金：${exceptionalFundCodeList.slice(0, 3).toString()}...`
      );
    } else {
      ruleList.push(`例外基金：${exceptionalFundCodeList.toString()}`);
    }
  }

  // 基金代码
  if (ruleContentList[0]?.fundCodeListStr) {
    const { fundCodeListStr } = ruleContentList[0];
    const fundCodeList = fundCodeListStr.split(',');
    if (fundCodeList.length > 3) {
      ruleList.push(`基金代码：${fundCodeList.slice(0, 3).toString()}...`);
    } else {
      ruleList.push(`基金代码：${fundCodeList.toString()}`);
    }
  }

  // 基金类型
  if (ruleContentList[1]?.fundType) {
    const fundTypeList: string[] = ruleContentList[1].fundType.split(',');
    // 公募基金
    const mutualFundList = fundTypeList.filter(
      item => item.slice(0, 2) === PRODUCT_TYPE.mutualFund
    );
    if (mutualFundList.length) {
      const mutualFundStrList = mutualFundList.map(
        item => INVEST_GOAL_TYPE[item.slice(2)]
      );
      ruleList.push(`公募基金：${mutualFundStrList.toString()}`);
    }
    // 私募资管
    const privateEquityManagementList = fundTypeList.filter(
      item => item.slice(0, 2) === PRODUCT_TYPE.privateEquityManagement
    );
    if (privateEquityManagementList.length) {
      const privateEquityManagementStrList = privateEquityManagementList.map(
        item => INVEST_GOAL_TYPE[item.slice(2)]
      );
      ruleList.push(`私募资管：${privateEquityManagementStrList.toString()}`);
    }
    // 私募基金
    const privateOfferingFundList = fundTypeList.filter(
      item => item.slice(0, 2) === PRODUCT_TYPE.privateOfferingFund
    );
    if (privateOfferingFundList.length) {
      const privateOfferingFundStrList = privateOfferingFundList.map(
        item => INVEST_GOAL_TYPE[item.slice(2)]
      );
      ruleList.push(`私募基金：${privateOfferingFundStrList.toString()}`);
    }
  }

  // 费率折扣未打折
  if (ruleContentList[2]?.discount) {
    ruleList.push('费率折扣未打折');
  }

  // 七天内赎回无手续费-是否开启
  if (ruleContentList[3]?.redemption) {
    ruleList.push('七天内赎回无手续费');
  }

  // 尾随金额
  if (ruleContentList[4]?.deductibleAmount) {
    const { deductibleAmount } = ruleContentList[4];
    ruleList.push(`尾随低于：${deductibleAmount}%`);
  }

  // 认申购费折后高于
  if (ruleContentList[5]?.purchaseDiscount) {
    const { purchaseDiscount } = ruleContentList[5];
    ruleList.push(`认申购费折后高于：${purchaseDiscount}%`);
  }

  // 申请金额全额上滑-是否开启
  if (ruleContentList[6]?.open) {
    ruleList.push('申请金额全额上滑');
  }
  return ruleList;
};

// 跳转券黑名单详情页，有三种情形：查看、新增和修改
const jumpBlacklistDetail = (mode: BLACKLIST_DETAIL_MODE, id: number) => {
  router.push({
    name: 'blacklistDetail',
    query: {
      mode,
      auditId: id,
    },
  });
};

// controllers
// 查询表格
const handleSearch = () => {
  onTableChange(fetchTable);
};

// 操作
// 暂停
const handlePause = (id: number) => {
  ajax('auditCouponBlacklist', {
    auditId: id,
    auditStatus: AUDIT_STATUS_NUM.paused,
    operator: state.userName,
  }).then(res => {
    if (res) {
      onTableChange(fetchTable);
    } else {
      message.error('暂停失败');
    }
  });
};

// 继续
const handleContinue = (id: number) => {
  ajax('auditCouponBlacklist', {
    auditId: id,
    auditStatus: AUDIT_STATUS_NUM.approved,
    operator: state.userName,
  }).then(res => {
    if (res) {
      onTableChange(fetchTable);
    } else {
      message.error('继续失败');
    }
  });
};

// 中止
const handleOver = (id: number) => {
  ajax('auditCouponBlacklist', {
    auditId: id,
    auditStatus: AUDIT_STATUS_NUM.aborted,
    operator: state.userName,
  }).then(res => {
    if (res) {
      onTableChange(fetchTable);
    } else {
      message.error('中止失败');
    }
  });
};

// 审核通过
const handleApprove = (id: number) => {
  ajax('auditCouponBlacklist', {
    auditId: id,
    auditStatus: AUDIT_STATUS_NUM.approved,
    operator: state.userName,
  }).then(res => {
    if (res) {
      onTableChange(fetchTable);
    } else {
      message.error('审核失败');
    }
  });
};

// 打回
const handleRefuse = (id: number) => {
  ajax('auditCouponBlacklist', {
    auditId: id,
    auditStatus: AUDIT_STATUS_NUM.refuse,
    operator: state.userName,
  }).then(res => {
    if (res) {
      onTableChange(fetchTable);
    } else {
      message.error('审核失败');
    }
  });
};

onMounted(fetchTable);
</script>

<style lang="less">
@import '../index.less';
</style>
