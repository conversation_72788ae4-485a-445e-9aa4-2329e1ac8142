<template>
  <div class="m-coupon-detail">
    <p class="m-page-title">券活动详情页</p>
    <div class="m-form-wrapper">
      <a-form
        name="couponDetailForm"
        :model="state.editForm"
        :label-col="{ span: 4 }"
        label-align="left"
        :wrapper-col="{ span: 10 }"
        @finish="handleFinish"
        @finishFailed="handleFinishFail"
      >
        <div class="m-form-content-wrapper">
          <div
            v-for="(list, index) in [
              COUPON_DETAIL_FORM_1,
              COUPON_DETAIL_FORM_2,
            ]"
            :key="index"
            class="m-form-content"
          >
            <template v-for="(item, itemIndex) in list" :key="itemIndex">
              <p v-if="item.template === 'title'" class="m-page-subtitle">
                {{ item.title }}
              </p>
              <template v-else-if="item.slotName === 'crowdFilter'">
                <a-form-item
                  :name="['delivery', 'scene']"
                  label="投放形式"
                  :rules="requiredRule"
                >
                  <a-select
                    v-model:value="state.editForm.delivery.scene"
                    allow-clear
                    :options="state.putTypeList"
                    :disabled="formItemDisabled()"
                    placeholder="请选择投放形式"
                    @change="handleChangePutType"
                  ></a-select>
                </a-form-item>
                <a-form-item
                  v-if="
                    state.editForm.delivery.scene === PACKET_PUT_TYPE.DIRECT
                  "
                  name="tagList"
                  label="适用人群"
                  :wrapper-col="{ span: 13 }"
                >
                  <a-button
                    :disabled="addTagDisabled"
                    type="primary"
                    ghost
                    @click="handleAddTag"
                  >
                    添加标签
                  </a-button>
                  <a-form-item
                    v-for="(tag, tagIndex) in state.editForm.delivery.tagList ||
                    []"
                    :key="tag.tagId"
                    class="tag-param-item"
                  >
                    <div class="split-line"></div>
                    <div class="tag-header">
                      <a-select
                        :value="tag.name"
                        :disabled="formItemDisabled()"
                        class="tag-header-select"
                      >
                        <a-select-option
                          v-for="(option, optionIndex) in state.allTagList"
                          :key="option.tagId"
                          :value="option.name"
                          :label="option.name"
                          :disabled="state.tagSelectedRecord[optionIndex]"
                          @click="
                            handleSelectTag(option, optionIndex, tagIndex)
                          "
                        ></a-select-option>
                      </a-select>
                      <a-button
                        :disabled="formItemDisabled()"
                        type="primary"
                        ghost
                        :icon="h(CloseOutlined)"
                        class="tag-header-delete"
                        @click="handleDeleteTag(tagIndex)"
                      ></a-button>
                    </div>
                    <a-form-item
                      v-for="(param, paramIndex) in tag.paramList.sort(
                        (a, b) => a.displayOrder - b.displayOrder
                      )"
                      :key="param.paramId"
                      :name="[
                        'delivery',
                        'tagList',
                        tagIndex,
                        'paramList',
                        paramIndex,
                        'value',
                      ]"
                      :label-col="{ span: 8 }"
                      :wrapper-col="{ span: 16 }"
                      :rules="getParamRule(param)"
                      class="tag-param-item"
                    >
                      <template #label>
                        <span class="tag-param-item-label">
                          {{ param.name }}
                        </span>
                      </template>
                      <a-select
                        v-if="param.fillType === TAG_COMPONENT_TYPE.SELECT"
                        :value="param.value ? JSON.parse(param.value) : []"
                        :disabled="formItemDisabled()"
                        mode="multiple"
                        show-arrow
                        allow-clear
                        :options="
                          state.tagParamOptions[
                            `${param.tagId}-${param.code}`
                          ] || []
                        "
                        :field-names="{ label: 'display', value: 'value' }"
                        :placeholder="`请输入${param.name}`"
                        @click="fetchTagParamOptions(param.tagId, param.code)"
                        @change="
                          (values) => handleSelectTagParam(param, values)
                        "
                      ></a-select>
                      <template
                        v-else-if="param.fillType === TAG_COMPONENT_TYPE.INPUT"
                      >
                        <a-date-picker
                          v-if="param.valueDataType === 'DATE'"
                          :value="param.value"
                          value-format="YYYYMMDD"
                          :disabled="formItemDisabled()"
                          class="param-date-picker"
                          @change="
                            (value) => handleDatePickerChange(param, value)"
                        ></a-date-picker>
                        <a-input-number
                          v-else-if="param.valueDataType === 'NUMBER'"
                          :value="param.value || ''"
                          :disabled="formItemDisabled()"
                          class="param-input-number"
                          @change="
                            (value) => handleNumberInputChange(param, value)
                          "
                        ></a-input-number>
                        <a-input
                          v-else
                          v-model:value="param.value"
                          :disabled="formItemDisabled()"
                        ></a-input>
                      </template>
                    </a-form-item>
                  </a-form-item>
                </a-form-item>
              </template>
              <span
                v-else-if="item.show && !item.show(state.editForm, state)"
              ></span>
              <form-item-input
                v-else
                :label="
                  typeof item.label === 'string'
                    ? item.label
                    : item.label(state)
                "
                :name="item.name"
                :rules="item.rules"
                :auto-size="item.autoSize"
                :label-col="item.labelCol"
                :wrapper-col="item.wrapperCol"
                :slot-name="item.slotName"
              >
                <template #input>
                  <a-input
                    v-model:value="state.editForm[item.name]"
                    :disabled="formItemDisabled(item)"
                    :placeholder="item.placeholder"
                    :addon-before="item.addonBefore"
                    :addon-after="item.addonAfter"
                    :maxlength="item.maxlength"
                    allow-clear
                  />
                </template>
                <template #select>
                  <a-select
                    v-model:value="state.editForm[item.name]"
                    :disabled="formItemDisabled(item)"
                    :placeholder="item.placeholder"
                    :show-search="item.showSearch"
                    :options="itemOptions(item)"
                    :filter-option="item.filterOption"
                    allow-clear
                    @search="(value: string) => item.onSearch(value, fetchTemplateList)"
                  ></a-select>
                </template>
                <template #textarea>
                  <a-textarea
                    v-model:value="state.editForm[item.name]"
                    :disabled="formItemDisabled(item)"
                    :placeholder="item.placeholder"
                    :auto-size="item.autoSize"
                    allow-clear
                  />
                </template>
                <template #radio>
                  <a-radio-group
                    v-model:value="state.editForm[item.name]"
                    :disabled="formItemDisabled(item)"
                    :options="itemOptions(item)"
                  />
                </template>
                <template #checkbox>
                  <a-checkbox-group
                    v-model:value="state.editForm[item.name]"
                    :disabled="formItemDisabled(item)"
                    :options="itemOptions(item)"
                  />
                </template>
                <template #timeRange>
                  <a-range-picker
                    v-model:value="state.editForm[item.name]"
                    :style="item.style"
                    :show-time="
                      typeof item.showTime === 'undefined'
                        ? true
                        : item.showTime
                    "
                    :disabled="formItemDisabled(item)"
                  />
                </template>
                <template
                  v-if="state.editForm.coupon_money_list?.length"
                  #rule_mj
                >
                  <div
                    v-for="(couponItem, couponIndex) in state.editForm
                      .coupon_money_list"
                    :key="couponIndex"
                    class="m-form-item-custom"
                  >
                    <span>满</span>
                    <a-form-item
                      :name="['coupon_money_list', couponIndex, 'coupon_limit']"
                      :rules="numberRules"
                    >
                      <a-input
                        v-model:value="couponItem.coupon_limit"
                        :disabled="true"
                        placeholder="请输入满减门槛"
                        allow-clear
                      />
                    </a-form-item>
                    <span>减</span>
                    <a-form-item
                      :name="['coupon_money_list', couponIndex, 'coupon_limit']"
                      :rules="numberRules"
                    >
                      <a-input
                        v-model:value="couponItem.coupon_value"
                        :disabled="true"
                        placeholder="请输入满减金额"
                        allow-clear
                      />
                    </a-form-item>
                  </div>
                </template>
                <template v-else #rule_mj>
                  <div class="m-form-item-custom">
                    <span>满</span>
                    <a-form-item name="sum_money" :rules="numberRules">
                      <a-input
                        v-model:value="state.editForm['sum_money']"
                        :disabled="true"
                        placeholder="请输入满减门槛"
                        allow-clear
                      />
                    </a-form-item>
                    <span>减</span>
                    <a-form-item name="sub_money" label="" :rules="numberRules">
                      <a-input
                        v-model:value="state.editForm['sub_money']"
                        :disabled="true"
                        placeholder="请输入满减金额"
                        allow-clear
                      />
                    </a-form-item>
                  </div>
                </template>
                <template #rule_zk>
                  <div class="m-form-item-custom">
                    <a-input
                      v-model:value="state.editForm['sub_rate']"
                      :disabled="true"
                      placeholder="请输入折扣"
                      allow-clear
                    />
                    折
                  </div>
                </template>
                <template #usedNum>
                  <div class="m-form-item-custom">
                    <a-form-item
                      name="frequency"
                      :rules="intRules"
                      style="width: 140px"
                    >
                      <a-input
                        v-model:value="state.editForm.frequency"
                        placeholder="请输入使用次数"
                        allow-clear
                        :disabled="true"
                      />
                    </a-form-item>
                    <span>次</span>
                    <a-form-item name="limitless">
                      <a-checkbox
                        :checked="!!state.editForm.limitless"
                        :disabled="true"
                      >
                        无限次
                      </a-checkbox>
                    </a-form-item>
                  </div>
                </template>
                <template #couponGroup>
                  <div class="m-form-item-custom">
                    <a-select
                      v-model:value="state.editForm[item.name]"
                      :disabled="formItemDisabled(item)"
                      placeholder="请选择红包组"
                      allow-clear
                    >
                      <a-select-option
                        v-for="option in itemOptions(item)"
                        :key="option.value"
                        :value="option.value"
                      >
                        {{ option.label }}
                      </a-select-option>
                    </a-select>
                    <a-button
                      type="primary"
                      ghost
                      size="small"
                      :disabled="formItemDisabled(item)"
                      @click="handleCouponGroup"
                    >
                      创建新的红包组
                    </a-button>
                  </div>
                </template>
              </form-item-input>
            </template>
          </div>
        </div>
        <a-form-item class="m-form-options" :wrapper-col="{ span: 12 }">
          <!-- 审核 && 查看 && 审核中 -->
          <template
            v-if="
              state.mode === COUPON_MODE.SCAN &&
              auth === ROLE_PERMISSION.VERIFY &&
              state.detail.status === '1' &&
              state.detail.review === '0'
            "
          >
            <a-button
              type="primary"
              :loading="store.state.loading"
              @click="handleVerify(true)"
            >
              审核通过
            </a-button>
            <a-button danger @click="handleRefuse">打回</a-button>
          </template>
          <a-button
            v-show="state.mode !== COUPON_MODE.SCAN"
            type="primary"
            html-type="submit"
            :loading="store.state.loading"
          >
            提交
          </a-button>
          <a-button @click="handleBack">返回</a-button>
        </a-form-item>
      </a-form>
    </div>
    <div class="m-modal-wrapper">
      <a-modal
        v-model:visible="state.couponGroupVisible"
        title="创建新的红包组"
        ok-text="完成"
        @ok="handleAddCouponGroup"
      >
        <a-input
          v-model:value="state.couponGroup"
          placeholder="请输入新的红包组"
          allow-clear
        />
      </a-modal>
      <a-modal v-model:visible="state.refuseVisible" title="打回理由">
        <!-- 页脚 -->
        <template #footer>
          <a-button @click="() => (state.refuseVisible = false)">取消</a-button>
          <a-button
            type="primary"
            :loading="store.state.loading"
            @click="handleVerify(false)"
          >
            确认
          </a-button>
        </template>
        <!-- 窗体 -->
        <a-textarea
          v-model:value="state.refuseReason"
          :rows="6"
          show-count
          :maxlength="100"
        />
      </a-modal>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { OptionItemVue } from '@/type/global';
import { reactive, onMounted, watch, h, computed } from 'vue';
import { CloseOutlined } from '@ant-design/icons-vue';
import { useRouter } from 'vue-router';
import {
  couponFormItem,
  couponTableData,
  couponTemplateTableData,
  TagListData,
  TagParamOption,
  TagParamItem,
  CouponDelivery,
} from '../type';
import FormItemInput from '@/components/packet/FormItemInput.vue';
import {
  COUPON_DETAIL_FORM_1,
  COUPON_DETAIL_FORM_2,
  numberRules,
  intRules,
  requiredRule,
  PACKET_PUT_TYPE,
  TAG_COMPONENT_TYPE,
  BIZ_CODE,
  COUPON_MODE,
  ROLE_PERMISSION,
  COUPON_TIME_TYPE,
  MATCH_CODE,
  COUPON_TYPE,
  OBJECT_TYPE,
} from '../const';
import ajax from '@/server/http';
import { localData } from '@/utils/common';
import { formatMoney } from '@/utils/fn';
import { message } from 'ant-design-vue';
import { debounce } from 'lodash';
import store from '@/store/index';
import dayjs from 'dayjs';
dayjs.locale('zh-CN');
const PRODUCT_TYPE_MAP = {
  PUBLIC_CODE: '01',
  PRIVATE_CODE: '03',
  PRIVATE_ASSET: '02',
};
type iState = {
  /** 优惠券详情 */
  detail: couponTableData;
  /** 详情表单 */
  editForm: couponTableData;
  /** 编辑模式 */
  mode: COUPON_MODE;
  /** 券模板列表 */
  couponTemplateList: OptionItemVue[];
  /** 券模板map */
  couponTemplateMap: { [x: string]: any };
  /** 券模板详情 */
  couponTemplateDetail: couponTemplateTableData;
  /** 创建红包组弹窗 */
  couponGroupVisible: boolean;
  /** 红包组列表 */
  couponGroupList: OptionItemVue[];
  /** 新增红包组 */
  couponGroup: string;
  /** 公司列表 */
  companyList: OptionItemVue[];
  /** 投放类型列表 */
  putTypeList: OptionItemVue[];
  /** 标签类型列表 */
  allTagList: TagListData[];
  /** 标签选中记录 */
  tagSelectedRecord: boolean[];
  /** 标签参数选项列表 */
  tagParamOptions: Record<string, TagParamOption[]>;
  /** 是否显示打回弹窗 */
  refuseVisible: boolean;
  /** 打回内容 */
  refuseReason: string;
  /**初始的名字 */
  initName: string;
};

// state
let defaultTemplateList: OptionItemVue[] = null;
const defaultCompanyList: OptionItemVue[] = [
  {
    label: '全部',
    value: '307',
  },
];
const timer = 300;
const router = useRouter();
const auth = router.currentRoute.value.query.auth as ROLE_PERMISSION;
const couponId = Number(router.currentRoute.value.query.couponId);
const state = reactive<iState>({
  detail: {},
  editForm: {
    delivery: {
      tagList: [],
    },
  },
  mode: COUPON_MODE.INIT_VALUE,
  couponTemplateList: [],
  couponTemplateMap: {},
  couponTemplateDetail: {},
  couponGroupVisible: false,
  couponGroupList: [],
  couponGroup: null,
  companyList: defaultCompanyList,
  putTypeList: [
    {
      label: '活动投放',
      value: PACKET_PUT_TYPE.ACTIVITY,
    },
    {
      label: '直接投放',
      value: PACKET_PUT_TYPE.DIRECT,
    },
  ],
  allTagList: [],
  tagSelectedRecord: [],
  tagParamOptions: {},
  refuseVisible: false,
  refuseReason: null,
  initName: '',
});
const addTagDisabled = computed(() => {
  const noAvailableType =
    state.tagSelectedRecord.findIndex((isSelected) => !isSelected) === -1;
  return noAvailableType || formItemDisabled();
});
const handleChangePutType = (value: string) => {
  // 如果投放形式修改为直接投放，且当前没有配置标签，则默认添加第一个标签
  if (value === PACKET_PUT_TYPE.DIRECT && !state.editForm.delivery?.tagList?.length) {
    handleAddTag();
  }
};

// methods
/** 初始化 */
const init = async () => {
  state.mode = router.currentRoute.value.query.mode as COUPON_MODE;
  // 请求红包组列表
  fetchCouponGroup();
  // 请求基金公司
  fetchCompanyList();
  // 请求标签类型
  await fetchAllTagList();
  // 请求券模板列表，券模板请求存在防抖延迟，详情请求延迟相同时间
  await fetchTemplateList();
  // 请求详情
  setTimeout(() => {
    fetchCouponDetail();
  }, timer);
};
/** delivery字段兜底处理 */
const getSafeDelivery = (originDelivery?: CouponDelivery) => ({
  ...(originDelivery || {}),
  tagList: originDelivery?.tagList || [],
});
/** 请求券活动详情 */
const fetchCouponDetail = async () => {
  if (state.mode === COUPON_MODE.ADD) {
    state.detail = {};
    state.editForm = {
      delivery: getSafeDelivery(),
    };
    return;
  }
  const res = await ajax('fetchCouponDetail', { id: couponId });
  if (res) {
    state.detail = res;
    /** 需要特殊处理的字段：折扣率、投放数量、使用次数、绝对时间段、基金类型、交易方式 */
    state.editForm = {
      ...res,
      public_code_list: [],
      private_code_list: [],
      private_asset_list: [],
    };
    const form = state.editForm;
    // delivery字段兜底
    form.delivery = getSafeDelivery(form.delivery);
    // 记录已有的标签类型
    if (form.delivery && form.delivery.tagList) {
      form.delivery.tagList.forEach((tag) => {
        const tagIndex = state.allTagList.findIndex(
          (item) => item.tagId === tag.tagId
        );
        if (tagIndex !== -1) {
          state.tagSelectedRecord[tagIndex] = true;
        }
      });
    }
    // 获取已有标签的所有参数选项列表（函数内部已catch错误，此处报错不会阻塞之后的代码）
    const promises = (form.delivery?.tagList || []).map((tag) => {
      return fetchTagParamOptions(tag.tagId, '');
    });
    await Promise.all(promises);
    state.editForm.num =
      Number(state.detail.num || 0) + Number(state.detail.edit_num || 0);
    state.editForm.edit_num = null;
    // 如果是折扣券
    if (form.coupon_type === COUPON_TYPE.ZK) {
      form.sub_rate = Number(formatMoney(form.sub_rate * 10));
      form.limitless = !!state.detail.limitless;
    }
    // 如果是绝对时间
    if (form.time_type === COUPON_TIME_TYPE.ABSOLUTE) {
      const { sdate, edate } = form;
      form.validTime = [dayjs(sdate), dayjs(edate)];
    }
    // 基金类型
    if (form.code_list) {
      form.code_list = (form.code_list as string).split(',');
    }
    // 交易方式
    if (form.trade_type) {
      form.trade_type = (form.trade_type as string).split(',');
    }
    if (form.object_type === OBJECT_TYPE.TG) {
      if (form.match_code === MATCH_CODE.TG_ALL) {
        form.match_code = MATCH_CODE.ALL;
      } else {
        form.match_code = MATCH_CODE.MANUAL;
      }
    }
    state.initName = res.coupon_name;
  } else {
    message.error('获取券活动详情失败');
  }
};
/** 请求券模板列表 */
const fetchTemplateList = debounce(async (templateName: string = null) => {
  if (defaultTemplateList && !templateName) {
    state.couponTemplateList = defaultTemplateList;
    return;
  }
  const res = await ajax('getCouponTemplate', {
    pageNum: 1,
    pageSize: 100,
    template_name: templateName,
  });
  if (res) {
    state.couponTemplateList = res.record.map((item) => {
      const template = {
        value: item.template_id,
        label: item.template_name,
        type: item.template_type,
        frequency: item.frequency,
        limitless: item.limitless,
        sub_money: item.sub_money,
        sub_rate: item.sub_rate,
        sum_money: item.sum_money,
        coupon_moeny_list: item.coupon_money_list,
      };
      state.couponTemplateMap[template.value] = template;
      return template;
    });
    if (!defaultTemplateList) {
      defaultTemplateList = state.couponTemplateList;
    }
  }
}, timer);
/** 获取基金公司列表 */
const fetchCompanyList = async () => {
  // 使用localStorage进行缓存
  const localCompany: {
    time: number;
    companys: OptionItemVue[];
  } = localData.get('crm_company_list');
  const now = new Date().getTime();
  if (localCompany && now <= localCompany.time + 72 * 3600 * 1000) {
    // 若缓存时间未过期，获取基金公司列表
    state.companyList = localCompany.companys;
    return;
  }
  const res = await ajax('getAllCompany');
  if (res) {
    const list: OptionItemVue[] = res.map((item) => ({
      label: item.ta_name,
      value: item.ta_code,
    }));
    state.companyList = [...defaultCompanyList, ...list];
    localData.set('crm_company_list', {
      time: now,
      companys: state.companyList,
    });
  }
};
/** 获取所有标签类型 */
const fetchAllTagList = async () => {
  try {
    const res = (await ajax('fetchCouponTagList', {
      bizCode: BIZ_CODE.FUND_COUPON,
    })) as TagListData[];
    if (res) {
      state.allTagList = res.map((item) => ({
        ...item,
      }));
      state.tagSelectedRecord.push(
        ...new Array(state.allTagList.length).fill(false)
      );
    }
  } catch (error) {
    message.error(error);
    state.allTagList = [];
    state.tagSelectedRecord = [];
  }
};
/** 提交表单 */
const postForm = async () => {
  const form: couponTableData = JSON.parse(JSON.stringify(state.editForm));

  // 校验券活动
  const checkCouponNameResult = await ajax('checkCouponActiveNameIsDuplicate', {
    couponName: form.coupon_name,
  });
  if (checkCouponNameResult && state.initName !== form.coupon_name) {
    message.error('券活动名称重复');
    return;
  }
  /** 需要特殊处理的字段：折扣率、投放数量、使用次数、绝对时间段、基金类型、交易方式、修改数量 */
  // 如果是折扣券
  delete form.sub_rate;
  delete form.limitless;
  if (form.coupon_type === COUPON_TYPE.ZK) {
    form.sub_rate = Number(
      formatMoney(state.editForm.sub_rate / 10, { precision: 3 })
    );
    form.limitless = Number(state.editForm.limitless);
  }
  // 如果是绝对时间
  delete form.validTime;
  if (form.time_type === COUPON_TIME_TYPE.ABSOLUTE) {
    const format = 'YYYY-MM-DD HH:mm:ss';
    form.sdate = state.editForm.validTime[0].format(format);
    form.edate = state.editForm.validTime[1].format(format);
  }
  // 基金类型

  if (form.code_list && form.code_list instanceof Array) {
  form.code_list = (form.code_list as string[]).join();
  }
  // 交易方式
  if (form.trade_type && form.trade_type instanceof Array) {
    form.trade_type = (form.trade_type as string[]).join();
  }
  // 投放数量、修改数量
  if (state.mode === COUPON_MODE.EDIT_PART) {
    // 小修改，投放数量重置为原数据
    form.num = state.detail.num;
    form.edit_num =
      Number(form.edit_num) * (form.edit_num_symbol === '0' ? 1 : -1);
    delete form.edit_num_symbol;
  }
  if (form.object_type === OBJECT_TYPE.TG) {
    if (form.match_code === MATCH_CODE.ALL) {
      form.match_code = MATCH_CODE.TG_ALL;
    } else {
      form.match_code = MATCH_CODE.TG_GROUP;
    }
  }
  // 如果适用人群选择了活动投放，则tagList数组置空，不带给后端
  if (form.delivery?.scene === PACKET_PUT_TYPE.ACTIVITY) {
    form.delivery.tagList = [];
  }
  const res = await ajax(
    `postCoupon${state.mode[0].toUpperCase()}${state.mode.slice(1)}`,
    form
  );
  if (res) {
    message.success('提交成功');
    handleBack();
  }
};
/** 审核 */
const postVerify = async (verify: boolean) => {
  const res = await ajax('postCouponStatus', {
    id: couponId,
    type: 'review',
    value: verify ? '1' : '2',
    reason: state.refuseReason,
  });
  if (res) {
    message.success('审核成功');
    setTimeout(() => {
      if (!verify) {
        state.refuseVisible = false;
        state.refuseReason = null;
      }
      handleBack();
    }, 1000);
  }
};
/** 请求红包组 */
const fetchCouponGroup = async () => {
  const res = (await ajax('fetchCouponGroupAll')) || [];
  if (res) {
    state.couponGroupList = res.map((item) => ({
      value: item.id,
      label: item.group_name,
    }));
  }
};
/** 新增红包组 */
const postAddCouponGroup = async () => {
  if (!state.couponGroup) return message.error('红包组名称不可为空！');
  const res = await ajax('addCouponGroup', {
    group_name: state.couponGroup,
  });
  if (res) {
    message.success('新增成功');
    state.couponGroupVisible = false;
    fetchCouponGroup();
  }
};
/** 表单项options */
const itemOptions = (item: couponFormItem) => {
  if (Array.isArray(item.options)) return item.options;
  else return item.options(state.editForm, state);
};
/** 判断表单项是否禁用 */
const formItemDisabled = (item?: couponFormItem) => {
  switch (state.mode) {
    case COUPON_MODE.ADD:
    case COUPON_MODE.EDIT_ALL:
      return false;
    case COUPON_MODE.EDIT_PART:
      return !!item?.editPartDisabled;
    case COUPON_MODE.SCAN:
      return true;
    default:
      return true;
  }
};
/** 获取一个标签下，所有参数 / 某个参数的options，填paramCode就是查某个参数，填空字符串就是查所有 */
const fetchTagParamOptions = (
  tagId: string,
  paramCode: string
): Promise<void> => {
  // 命中缓存，直接返回
  if (paramCode && state.tagParamOptions[`${tagId}-${paramCode}`]) {
    return Promise.resolve();
  }
  return ajax('fetchCouponTagParams', { tagId, paramCode })
    .then((res: TagParamOption[]) => {
      if (res.length) {
        let currentCode = '';
        res.forEach((option) => {
          if (option.paramCode !== currentCode) {
            currentCode = option.paramCode;
            state.tagParamOptions[`${tagId}-${option.paramCode}`] = [option];
          } else {
            state.tagParamOptions[`${tagId}-${option.paramCode}`].push(option);
          }
        });
        return;
      }
      // 兜底，防止重复发请求
      paramCode && (state.tagParamOptions[`${tagId}-${paramCode}`] = []);
    })
    .catch((error) => {
      // 兜底，防止重复发请求
      paramCode && (state.tagParamOptions[`${tagId}-${paramCode}`] = []);
      message.error('标签参数获取失败', error);
    });
};
/** 添加标签 */
const handleAddTag = () => {
  // 获取第一个可用的tag类型
  const firstAvailableIndex = state.tagSelectedRecord.findIndex(
    (isSelected) => !isSelected
  );
  // allTagList中的所有标签不能修改，这里深拷贝一个到editForm中，收集参数
  const tagData = JSON.parse(
    JSON.stringify(state.allTagList[firstAvailableIndex])
  );
  if (tagData) {
    state.editForm.delivery.tagList.push(tagData);
    state.tagSelectedRecord[firstAvailableIndex] = true;
  }
};
/** 标签类型下拉框 选中处理函数 */
const handleSelectTag = (
  tagData: TagListData,
  curOptionIndex: number,
  curTagIndex: number
) => {
  if (state.tagSelectedRecord[curOptionIndex]) {
    return;
  }
  // 选择的tag类型记录为true
  handleTagRecord(tagData, true);
  // 被替换的tag类型记录为false
  handleTagRecord(state.editForm.delivery.tagList[curTagIndex], false);
  // 替换tag类型，深拷贝防止数据错乱
  state.editForm.delivery.tagList[curTagIndex] = JSON.parse(
    JSON.stringify(tagData)
  );
};
/** 修改标签记录 */
const handleTagRecord = (tagData: TagListData, result: boolean) => {
  const tagIndex = state.allTagList.findIndex(
    (item) => item.tagId === tagData.tagId
  );
  if (tagIndex !== -1) {
    state.tagSelectedRecord[tagIndex] = result;
  }
};
/** 删除标签 */
const handleDeleteTag = (index: number) => {
  // 重置tag类型
  handleTagRecord(state.editForm.delivery.tagList[index], false);
  // 删除tag
  state.editForm.delivery.tagList.splice(index, 1);
};
/** 标签的某个参数下拉框 选中处理函数 */
const handleSelectTagParam = (tagParam: TagParamItem, values: string[]) => {
  // 多选框默认收集的值是数组，需要手动转换一下格式，使用英文逗号拼接再传给后端
  tagParam.value = JSON.stringify(values);
};
/** 标签的某个参数日期选择框，收集该表单值的处理函数 */
const handleDatePickerChange = (tagParam: TagParamItem, values: string) => {
  console.log('value', values);
  tagParam.value = values || '';
};
/** 标签的某个参数数字输入框，收集该表单值的处理函数 */
const handleNumberInputChange = (tagParam: TagParamItem, value: number) => {
  tagParam.value = value || value === 0 ? String(value) : '';
};
/** 动态地获取标签参数校验规则 */
const getParamRule = (param: TagParamItem) => ({
  required: !!param.notNull,
  validator: (rule: any, value: string) => {
    // 如果不是必填项，则不校验
    if (!param.notNull) return Promise.resolve();
    if (
      (param.fillType === TAG_COMPONENT_TYPE.SELECT && value === '[]') ||
      !value
    ) {
      return Promise.reject(`请输入${param.name}`);
    }
    return Promise.resolve();
  },
  trigger: 'change',
});

// controller
/** 提交表单 */
const handleFinish = () => {
  postForm();
};
/** 提交表单失败 */
const handleFinishFail = ({
  values,
  errorFields,
}: {
  values: couponTableData;
  errorFields: any[];
}) => {
  console.log('fail finish', values, errorFields);
};
/** 红包组控制器 */
const handleCouponGroup = () => {
  state.couponGroupVisible = true;
};
/** 新增红包组 */
const handleAddCouponGroup = () => {
  postAddCouponGroup();
};
/** 返回上一级 */
const handleBack = () => {
  router.back();
};
/** 审核通过/确认打回 */
const handleVerify = (verify: boolean) => {
  postVerify(verify);
};
/** 打回 */
const handleRefuse = () => {
  state.refuseVisible = true;
};

// watch
// 监听券模板id变化
watch(
  () => [state.editForm.template_id, state.couponTemplateMap],
  () => {
    // 修改券模板时自动填充券模板的信息
    const template = state.couponTemplateMap[state.editForm.template_id];
    state.editForm.coupon_type = template.type;
    if (template.type === '1') {
      state.editForm.sum_money = template.sum_money;
      state.editForm.sub_money = template.sub_money;
      state.editForm.coupon_money_list = template.coupon_moeny_list;
    } else {
      state.editForm.sub_rate = Number(formatMoney(template.sub_rate * 10));
      state.editForm.limitless = !!template.limitless;
      state.editForm.frequency = template.frequency;
    }
  }
);
// 监听红包组弹窗变化
watch(
  () => state.couponGroupVisible,
  (visible) => {
    if (!visible) state.couponGroup = null;
  }
);

onMounted(init);
</script>

<style lang="less">
@import '../index.less';
</style>
