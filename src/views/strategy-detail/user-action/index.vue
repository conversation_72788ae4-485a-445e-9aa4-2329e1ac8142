<template>
  <a-form-item :wrapper-col="{ span: 2 }" label="条件逻辑关系" required>
    <a-select v-model:value="modelRef.triggerConditionLogic" :options="conditionLogic"></a-select>
  </a-form-item>
  <a-form-item v-for="(item, index) in modelRef.triggers" :key="index" :wrapper-col="{ span: 20, offset: 2 }">
    <div class="trigger-item">
      <a-form-item>
        <a-button type="danger" @click="del(index)">删除</a-button>
      </a-form-item>
      <a-form-item label="触达页面">
        <a-space>
          <a-select v-model:value="item.page" :options="triggerScene">
          </a-select>
          <a-select v-model:value="item.condition" :options="triggerConditionMap[item.page]">
          </a-select>
          <a-select v-if="item.page === 2" v-model:value="item.compare" :options="statLogicList" class="w100"></a-select>
          <a-form-item v-if="[0, 2].includes(item.page)" class="threshold-item" label="阈值" required>
            <div class="threshold">
              <a-input v-model:value="item.threshold"></a-input>
              <span class="extra">次</span>
            </div>
          </a-form-item>
        </a-space>
      </a-form-item>
      <a-form-item v-if="item.page === 2" label="指定埋点" required>
        <a-input v-model:value="item.buried_point" class="w300" placeholder="请输入埋点"></a-input>
      </a-form-item>
      <UploadUserFile v-if="item.page === 1" :is-exit-download-url="!!modelRef.holdFundList?.length" type="holdFundFile" title="基金代码" download-text="持仓-自定义基金列表文件"
        help="请上传xlsx文档，每行填入一个基金代码，第一行不要填任何数据" />
    </div>
  </a-form-item>
  <!-- <a-form-item v-bind="validateInfos.triggers" :wrapper-col="{ span: 22, offset: 2 }">
  </a-form-item> -->
  <a-form-item :wrapper-col="{ span: 22, offset: 2 }">
    <a-button type="primary" @click="add">添加</a-button>
  </a-form-item>
</template>

<script lang='ts'>
import { defineComponent } from 'vue'

import { modelRef, useAntForm } from '../useAntForm';
import UploadUserFile from '../user-property/UploadUserFile.vue';
import { ConditionLogicList, TriggerScene, TriggerConditionMap, StatLogicList } from '../constants';

/**
 * 基金账户
 * 持仓列表 指定基金 自定义基金列表
 * 指定埋点 触发次数 
 */
export default defineComponent({
  components: {
    UploadUserFile: UploadUserFile as any,
  },

  setup() {
    const { validateInfos } = useAntForm();
    const add = () => {
      modelRef.triggers.push({
        page: 0,
        condition: 0,
        compare: '='
      });
    }

    const del = (index) => { modelRef.triggers.splice(index, 1) }

    return {
      modelRef,
      conditionLogic: ConditionLogicList,
      triggerScene: TriggerScene,
      triggerConditionMap: TriggerConditionMap,
      validateInfos,
      statLogicList: StatLogicList,
      add,
      del
    }
  }
})
</script>
<style lang='less' scoped>
.trigger-item {
  padding: 20px 0 0 20px;
  border: 1px solid #bdbbbb
}

.threshold-item.ant-form-item {
  margin-bottom: 0;
}

.threshold {
  display: flex;
  align-items: center;

  .extra {
    margin-left: 10px;
  }
}

.w300 {
  width: 300px;
}

.w100 {
  min-width: 100px;
}
</style>
