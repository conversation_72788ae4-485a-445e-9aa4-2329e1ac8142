import { reactive } from 'vue';
import { Form } from 'ant-design-vue';
import { ILabelItem } from './types';

interface ITrigger {
  page: number;
  condition: number;
  threshold?: string;
  appointFund?: 1;
  compare?: string;
  buried_point?: string;
}

export const modelRef = reactive<{
  targetType?: 'kyc' | 'olas' | 'file' | '';
  kycLogic?: 'and' | 'or';
  kycs?: Array<{
    label: string;
    logic:
      | 'equal'
      | 'notEqual'
      | 'in'
      | 'notIn'
      | 'isSet'
      | 'notSet'
      | 'greater'
      | 'less'
      | 'greaterEqual'
      | 'lessEqual';
    value: string;
  }>;
  rangeTime: [string, string] | [];
  olasId?: string;
  whiteList: string[];
  blackList: string[];
  holdFundList: string[];
  strategyName: string;
  triggerConditionLogic: 'and' | 'or';
  triggers: ITrigger[];
  strategyId?: string;
  fileMap?: Record<string, any>;
}>({
  targetType: '',
  kycLogic: 'and',
  kycs: [],
  olasId: null,
  whiteList: null,
  blackList: null,
  rangeTime: [],
  strategyName: '',
  triggerConditionLogic: 'and',
  triggers: [],
  fileMap: {},
  holdFundList: null
});

const rulesRef = reactive({
  strategyName: [
    {
      required: true,
      message: '不能为空',
    },
  ],
  rangeTime: [
    {
      required: true,
      message: '不能为空',
    },
  ],
  triggers: [
    {
      validator: async (_, value: ITrigger[]) => {
        if (value.find((i) => [0, 2].includes(i.page) && !i.threshold)) {
          return Promise.reject('阈值不能为空');
        }
        if (value.find((i) => [2].includes(i.page) && !i.buried_point)) {
          return Promise.reject('指定埋点不能为空');
        }
        return Promise.resolve();
      },
    },
  ],
});

export const useAntForm = () => {
  const { resetFields, validate, validateInfos } = Form.useForm(
    modelRef,
    rulesRef
  );

  return { resetFields, validate, validateInfos };
};

export const userPropertyModel = reactive<{
  labelList: ILabelItem[];
  renderLabelList: { value: string; label: string }[];
  userList: { value: string; label: string }[];
}>({
  labelList: [],
  renderLabelList: [],
  userList: []
});
