import { defineComponent, onMounted } from 'vue';

import ajax from '@/server/http';
import { useAntForm, modelRef, userPropertyModel } from '../useAntForm';
import KycTagList from './KycTagList.vue';
import UploadUserFile from './UploadUserFile.vue';
import { ILabelItem } from '../types';

export default defineComponent({
  name: 'user-property',

  components: {
    KycTagList: KycTagList as any,
    UploadUserFile: UploadUserFile as any,
  },

  setup() {
    const { resetFields } = useAntForm();

    onMounted(() => {
      ajax('queryintelligentlistlabel').then((res: ILabelItem[]) => {
        userPropertyModel.labelList = res;
        userPropertyModel.renderLabelList = res.map(i => ({
          value: i.label_code,
          label: i.label_name,
        }));
      });

      ajax("userList", { userType: 'all' }).then(res => {
        userPropertyModel.userList = res.map((i) => {
          return {
            label: i.description,
            value: i.taskId + '_' + i.types,
          };
        });;
      });
    });

    return () => {
      return (
        <>
          <a-form-item label="指定用户">
            <a-radio-group v-model:value={modelRef.targetType}>
              {[
                { value: 'kyc', name: '通过KYC标签' },
                {
                  value: 'olas',
                  name: '通过OLAS平台推送',
                },
                {
                  value: 'file',
                  name: '手动上传用户名单',
                },
              ].map(({ name, value }) => {
                return <a-radio value={value}>{name}</a-radio>;
              })}
            </a-radio-group>
            <a-button onClick={() => resetFields('targetType')}>
              清除指定用户
            </a-button>
          </a-form-item>
          {modelRef.targetType === 'kyc' && (
            <kyc-tag-list />
          )}
          {modelRef.targetType === 'olas' && (
            <a-form-item label="用户群体" wrapperCol={{ span: 4 }}>
              <a-select
                showSearch
                v-model:value={modelRef.olasId}
                options={userPropertyModel.userList}
                filterOption={(input, option: any) =>
                  option.label.indexOf(input) >= 0
                }
              />
            </a-form-item>
          )}
          {modelRef.targetType === 'file' && (
            <upload-user-file
              isExitDownloadUrl={!!modelRef.whiteList?.length}
              title="上传文件"
              downloadText="白名单"
              type="whiteListFile"
            />
          )}
          <upload-user-file
            isExitDownloadUrl={!!modelRef.blackList?.length}
            title="黑名单"
            downloadText="用户黑名单"
            type="blackListFile"
          />
        </>
      );
    };
  },
});
