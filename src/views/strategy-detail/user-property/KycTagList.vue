<template>
  <a-form-item label="标签逻辑关系" :wrapper-col="{ span: 2 }">
    <a-select v-model:value="modelRef.kycLogic" :options="ConditionLogicList" />
  </a-form-item>
  <a-form-item label="标签值">
    <a-button type="primary" @click="addTag">添加</a-button>
  </a-form-item>
  <a-form-item v-for="(tag, index) in tagList" :key="index" :wrapper-col="{ span: 14, offset: 4 }">
    <a-space style="display: flex; margin-bottom: 8px" align="baseline">
      <a-form-item>
        <a-select v-model:value="tag.label" :show-search="true" class="tag-item" :options="userPropertyModel.renderLabelList"
          :filter-option="filterOption" />
      </a-form-item>
      <a-form-item>
        <a-select v-model:value="tag.logic" class="tag-item" :options="LogicList" />
      </a-form-item>
      <a-form-item v-if="tag.logic !== 'isSet' && tag.logic !== 'notSet'">
        <a-input v-model:value="tag.value" class="tag-item"></a-input>
      </a-form-item>
      <a-button @click="delTag(index)">删除</a-button>
    </a-space>
    <div v-if="!isLabelAvailable(tag.label)" class="red">
      此标签未添加进基金的应用场景fund0005，请联系叶志飞（<EMAIL>）手动添加，15:00前添加，T+1生效</div>
  </a-form-item>
</template>
<script lang="ts">
import { defineComponent } from 'vue';

import { modelRef, userPropertyModel } from '../useAntForm';
import { LogicList, ConditionLogicList } from '../constants';

export default defineComponent({
  setup() {
    const tagList = modelRef.kycs;

    const addTag = () => {
      tagList.push({
        label: (userPropertyModel.labelList[0])?.label_code,
        logic: 'equal',
        value: ''
      })
    }

    const delTag = (index) => {
      tagList.splice(index, 1);
    }

    const isLabelAvailable = (label: string) => {
      return userPropertyModel.labelList.find(i => i.label_code === label)?.app_info?.includes('fund0005');
    }

    return {
      tagList,
      addTag,
      LogicList,
      delTag,
      filterOption: (input, option: any) => {
        return option.label.indexOf(input) >= 0;
      },
      isLabelAvailable,
      userPropertyModel,
      modelRef,
      ConditionLogicList
    };
  },
});
</script>
<style scoped lang="less">
.red {
  color: red;
}

.tag-item {
  width: 200px;
}
</style>
