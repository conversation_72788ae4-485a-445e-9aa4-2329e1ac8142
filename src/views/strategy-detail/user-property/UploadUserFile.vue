<template>
  <a-form-item :label="title">
    <a-space :size="50" align="start">
      <a v-if="isExitDownloadUrl" @click="downLoadFile">{{ downloadText }}</a>
      <a-upload :max-count="1" :before-upload="beforeUpload">
        <a-button>上传文件</a-button>
      </a-upload>
      <a-button @click="downloadTemplate">下载模板</a-button>
      <p class="red">{{ help }}</p>
    </a-space>
  </a-form-item>
</template>

<script lang="ts">
import { defineComponent, PropType } from 'vue';

import ajax from '@/server/http';
import { modelRef } from '../useAntForm';

export default defineComponent({
  props: {
    title: {
      type: String,
      required: true
    },
    downloadText: {
      type: String,
      required: true
    },
    isExitDownloadUrl: {
      type: Boolean,
      default: false
    },
    help: {
      type: String,
      default: '请上传xlsx文档，每行填入一个基金客户号（cust_id），第一行不要填数据'
    },
    type: {
      type: String as PropType<'whiteListFile' | 'blackListFile' | 'holdFundFile'>,
      required: true
    }
  },

  setup(props) {
    return {
      beforeUpload: (file) => {
        console.log('file', file);
        modelRef.fileMap[props.type] = file;
        return false;
      },
      downloadTemplate: () => {
        ajax('downloadTemplate', { filename: props.type });
      },
      downLoadFile: () => {
        ajax('fileDownload', { filename: props.type, strategyId: modelRef.strategyId });
      },
    }
  }
})
</script>
<style scoped lang="less">
.red {
  color: red;
}
</style>