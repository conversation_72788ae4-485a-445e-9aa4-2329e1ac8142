<template>
  <a-form :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }">
    <a-form-item :wrapper-col="{ span: 22, offset: 2 }">
      <block-header>策略基本信息</block-header>
    </a-form-item>
    <a-form-item
      label="策略名称"
      :wrapper-col="{ span: 4 }"
      :required="true"
      v-bind="validateInfos.strategyName"
    >
      <a-input
        v-model:value="modelRef.strategyName"
        placeholder="请输入策略名称"
      ></a-input>
    </a-form-item>
    <a-form-item
      label="策略生效时间"
      :required="true"
      v-bind="validateInfos.rangeTime"
    >
      <a-range-picker
        v-model:value="modelRef.rangeTime"
        class="range-picker"
        show-time
        format="YYYY-MM-DD HH:mm:ss"
        value-format="YYYY-MM-DD HH:mm:ss"
        :placeholder="['策略开始时间', '策略结束时间']"
      />
    </a-form-item>
    <a-form-item :wrapper-col="{ span: 22, offset: 2 }">
      <block-header>用户属性</block-header>
    </a-form-item>
    <user-property />
    <a-form-item :wrapper-col="{ span: 22, offset: 2 }">
      <block-header>
        <div class="title">用户行为</div>
      </block-header>
    </a-form-item>
    <user-action />
    <a-form-item v-if="isEditPage" :wrapper-col="{ span: 14, offset: 4 }">
      <a-space :size="50">
        <a-button type="primary" @click.prevent="save">保存</a-button>
        <a-button @click.prevent="goList">取消</a-button>
      </a-space>
    </a-form-item>
  </a-form>
</template>
<script type="ts">
import { defineComponent, toRaw, onMounted, computed } from 'vue';
import { message } from 'ant-design-vue';
import { useRouter } from 'vue-router';
import { useStore } from 'vuex';
import ajax from '@/server/http';
import BlockHeader from '@/components/common/BlockHeader.vue';
import { getUrlParam } from '@/utils/fn';
import UserProperty from './user-property';
import UserAction from './user-action/index.vue';
import { modelRef, useAntForm } from './useAntForm';
import { transformFieldsToParams, transformParamsToFields } from './helper';

export default defineComponent({
  components: {
    UserProperty,
    BlockHeader,
    UserAction
  },

  setup() {
    const store = useStore();
    const { resetFields, validate, validateInfos } = useAntForm();
    const id = getUrlParam('id', 'hash');
    const router = useRouter();
    const userInfo = computed(() => store.state.userInfo);

    onMounted(() => {
      if (id) {
        ajax('queryStrategy', { strategyId: id }).then((data) => {
          resetFields(transformParamsToFields(data));
        });
      }
    });

    const goList = () => {
      router.push('/clueStrategy/list');
    }

    const save = () => {
      const data = transformFieldsToParams(toRaw(modelRef));
      validate().then(() => {
        if (!id) {
          ajax('addStrategy', { ...data, lastEditor: userInfo.value.userName || 'admin' }).then((res) => {
            modelRef.strategyId = res;
            if (Object.keys(modelRef.fileMap).length) {
              ajax('fileUpload', { strategyId: res, ...modelRef.fileMap }, {}, {
                headers: {
                  'Content-Type':
                    'multipart/form-data;',
                },
              }).then(() => {
                goList();
              });
              return;
            }
            goList();
          });
          return;
        }
        if (Object.keys(modelRef.fileMap).length) {
          ajax('fileUpload', { strategyId: id, ...modelRef.fileMap }, {}, {
            headers: {
              'Content-Type':
                'multipart/form-data;',
            },
          }).then((res) => {
            if (res) {
              message.error(res);
              return;
            }
            ajax('updateStrategy', { ...data, type: '0' }).then(() => {
              message.success('保存成功');
              goList();
            }).catch(() => {
              message.error('保存失败');
            });
          });
          return;
        }
        ajax('updateStrategy', { ...data, type: '0' }).then(() => {
          message.success('保存成功');
          goList();
        }).catch(() => {
          message.error('保存失败');
        });
      }).catch(res => {
        message.error(res?.errorFields[0]?.errors[0] || '输入错误');
      });
    }

    return {
      save,
      modelRef,
      validateInfos,
      isEditPage: getUrlParam('type', 'hash') !== 'detail',
      goList
    }
  },
});
</script>
<style scoped lang="less">
.title::before {
  display: inline-block;
  margin-right: 4px;
  color: #ff4d4f;
  font-size: 14px;
  font-family: SimSun, sans-serif;
  line-height: 1;
  content: '*';
}

.range-picker {
  width: 400px;
}
</style>
