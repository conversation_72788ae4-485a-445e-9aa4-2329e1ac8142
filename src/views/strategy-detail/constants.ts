export const LogicList = [
  {
    value: 'equal',
    label: '等于',
  },
  {
    value: 'notEqual',
    label: '不等于',
  },
  {
    value: 'in',
    label: '字符串包含',
  },
  {
    value: 'notIn',
    label: '字符串不包含',
  },
  {
    value: 'isSet',
    label: '有值',
  },
  {
    value: 'notSet',
    label: '没值',
  },
  {
    value: 'greater',
    label: '大于',
  },
  {
    value: 'less',
    label: '小于',
  },
  {
    value: 'greaterEqual',
    label: '大于等于',
  },
  {
    value: 'lessEqual',
    label: '小于等于',
  },
];

export const ConditionLogicList = [
  { value: 'and', label: '与' },
  { value: 'or', label: '或' },
];

export const TriggerScene = [
  {
    value: 0,
    label: '基金账户',
  },
  {
    value: 1,
    label: '持仓列表',
  },
  {
    value: 2,
    label: '指定埋点',
  },
];

export const TriggerConditionMap = {
  '0': [
    {
      label: '单笔申购金额≥n',
      value: 0,
    },
    {
      label: '单笔赎回金额≥n',
      value: 1,
    },
    {
      label: '当日申购金额累计≥n',
      value: 2,
    },
    {
      label: '当日赎回金额累计≥n',
      value: 3,
    },
    {
      label: '7日内申购金额累计≥n',
      value: 4,
    },
    {
      label: '7日内赎回金额累计≥n',
      value: 5,
    },
  ],
  '1': [
    {
      label: '指定基金',
      value: 0,
    },
  ],
  '2': [
    {
      label: '触发次数',
      value: 0,
    },
  ],
};

export const StatLogicList = [
  {
    value: '=',
    label: '等于',
  },
  {
    value: '>',
    label: '大于',
  },
  {
    value: '<',
    label: '小于',
  },
  {
    value: '>=',
    label: '大于等于',
  },
  {
    value: '<=',
    label: '小于等于',
  },
];
