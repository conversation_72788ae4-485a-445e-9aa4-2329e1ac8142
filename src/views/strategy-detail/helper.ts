import { omit } from 'lodash';

export const transformParamsToFields = (data) => {
  const { olasId, startTime, endTime, ...rest } = data;

  return {
    olasId: olasId ? olasId[0] : olasId,
    rangeTime: [startTime, endTime],
    ...rest
  };
}

export const transformFieldsToParams = ({ rangeTime, ...rest }) => {
  const [startTime, endTime] = rangeTime;
  const { olasId } = rest;

  return {
    ...omit(rest, ['fileMap', 'olasId', 'blackList', 'holdFundList', 'whiteList']),
    startTime,
    endTime,
    strategyType: '常规运营线索',
    olasId: olasId ? [olasId] : olasId
  };
};
