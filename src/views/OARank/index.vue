<!--
 * @Author: like4
 * @Date: 2023-05-16 13:13:30
 * @LastEditTime: 2023-05-22 14:29:57
-->
<template>
  <section id="root"></section>
</template>
<script lang="ts">
import { getOACommonUserInfo } from '@/server/api';
import amisEnv from '@/utils/amisEnv';
import { getUrlParam } from '@/utils/fn';
import { amisRemoteEmbed } from '@king-fisher/crm-amis-next';
import { DEFAULT_SYSTEM_ID, AMIS_ENV } from '@/utils/request';

const LeaderFlagMap = {
  BOSS: '1',
  NORMAL: '0',
};
const JIGOUMAP = {
  fofRemainAmount: '0',
  fofSaleVisitCount: '1',
  fofRealAchievementAmount: '2',
  adAchievementAmount: '3',
};
const SaleTypeMap = {
  NET: '01',
  PHONE: '02',
  JIGOU: '03',
};
const orderByMap = {
  '03': JIGOUMAP,
};
const renderPageIdMap = {
  [SaleTypeMap.NET]: {
    [LeaderFlagMap.NORMAL]: 1165,
    [LeaderFlagMap.BOSS]: 1168,
  },
  [SaleTypeMap.PHONE]: {
    [LeaderFlagMap.NORMAL]: 1171,
    [LeaderFlagMap.BOSS]: 1174,
  },
  [SaleTypeMap.JIGOU]: {
    [LeaderFlagMap.NORMAL]: 1159,
    [LeaderFlagMap.BOSS]: 1162,
  },
};
export default {
  data() {
    return {
      amisScoped: null,
      userInfo: {},
      renderPageId: null,
      workNum: '',
    };
  },
  async created() {
    const salerHumanid = getUrlParam('salerHumanid') || '';
    const res = await getOACommonUserInfo(salerHumanid);
    const orderMapForShow = orderByMap[String(res.sale_type)];
    this.renderPageId =
      renderPageIdMap[String(res.sale_type)][String(res.leader_flag)];
    this.userInfo = res;

    amisRemoteEmbed(
      '#root',
      {
        id: this.renderPageId,
        systemId: DEFAULT_SYSTEM_ID,
        env: AMIS_ENV,
      },
      {
        // 开启本地缓存
        useLocalCache: true,
      },
      {
        data: {
          userInfo: [this.userInfo],
          workNum: salerHumanid,
          orderByMap: orderMapForShow,
        },
      },
      amisEnv()
    ).then(scoped => {
      this.amisScoped = scoped;
    });
  },
  mounted() {},
  unmounted() {
    this.amisScoped.unmount();
  },
};
</script>
