<template>
  <div class="m-distribute-rule">
    <h2 style="font-size: 17px; font-weight: 600; margin-bottom: 17px">
      自定义分配规则：
    </h2>
    <div class="m-flex mb-20">
      <span>分配资料至：</span>
      <a-tree-select
        v-model:value="cluesOrigin"
        show-search
        style="width: 340px"
        allow-clear
        tree-default-expand-all
        :tree-data="cluesOriginOptions"
        tree-node-filter-prop="label"
      />
    </div>
    <div class="m-flex mb-20">
      <span>分配对象：</span>
      <div>
        <sale-tree
          v-model:selected-data="belongSale"
          v-model:tree-data="materialTree"
          style="width: 17.7vw"
          :max-tag-count="4"
        />
      </div>
    </div>
    <div class="m-flex mb-20">
      <span>是否自动分配：</span>
      <a-select v-model:value="do_allocate" style="width: 170px" allow-clear>
        <a-select-option
          v-for="option in stateOptions"
          :key="option.value"
          :value="option.value"
        >
          {{ option.label }}
        </a-select-option>
      </a-select>
    </div>
    <a-button type="primary" @click="handleSave">保存配置并生效</a-button>
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs } from 'vue';
import ajax from '@/server/http';
import saleTree from '@/components/basic/sale-tree.vue';
import { message } from 'ant-design-vue';
type iState = {
  cluesOrigin: string;
  do_allocate: string;
  materialTree: any[];
  belongSale: number[];
  stateOptions: { value: string; label: string }[];
};
export default defineComponent({
  name: 'DistributeRule',
  components: {
    saleTree,
  },
  props: {
    cluesOriginOptions: {
      type: Array,
      default: () => [],
    },
  },
  setup() {
    const state = reactive<iState>({
      cluesOrigin: '',
      do_allocate: '',
      materialTree: [],
      belongSale: [],
      stateOptions: [
        {
          value: '1',
          label: '自动分配',
        },
        {
          value: '0',
          label: '不分配',
        },
      ],
    });
    const handleSave = () => {
      ajax('clues_distri_rule', {
        activity_id: state.cluesOrigin,
        si_id_list: state.belongSale,
        do_allocate: state.do_allocate,
      })
        .then((res: any) => {
          message.success('保存配置成功');
        })
        .catch((err: unknown) => {
          console.log('保存配置错误：', err);
        });
    };
    return {
      ...toRefs(state),
      handleSave,
    };
  },
});
</script>
<style lang="less" scoped>
.m-distribute-rule {
  padding-left: 20px;
}
.m-flex {
  display: flex;
  & > span {
    margin-right: 10px;
    display: inline-block;
    text-align: right;
    width: 96px;
  }
}
.mb-20 {
  margin-bottom: 20px;
}
</style>
