<template>
  <div class="clues-list">
    <div class="clues-list-search filter-container">
      <div>
        <span>日期：</span>
        <a-range-picker
          v-model:value="date"
          format="YYYY-MM-DD HH:mm:ss"
          show-time
          style="width: 360px"
        />
      </div>
      <div>
        <span>线索来源：</span>
        <a-tree-select
          v-model:value="cluesOrigin"
          show-search
          style="width: 340px"
          allow-clear
          tree-default-expand-all
          :tree-data="cluesOriginOptions"
          tree-node-filter-prop="label"
        />
      </div>
      <div>
        <span>资料状态：</span>
        <a-select
          v-model:value="allocate_flag"
          style="width: 100px"
          allow-clear
          :options="STATE_OPTIONS"
        ></a-select>
      </div>
      <div>
        <a-select
          v-model:value="selectType"
          allow-clear
          placeholder="查询类型"
          style="width: 120px"
        >
          <a-select-option value="cust_name">姓名</a-select-option>
          <a-select-option value="cust_id">基金客户号</a-select-option>
        </a-select>
        <a-input
          v-model:value="xm"
          allow-clear
          placeholder="请输入姓名/伪码/基金客户号"
          style="width: 230px"
        />
      </div>
      <div class="m-filter-btns">
        <a-button type="primary" @click="handleSearch">查询</a-button>
        <a-button @click="exportTable">导出</a-button>
      </div>
    </div>
    <div class="clues-list-table">
      <custom-table
        v-model:page-num="pageNum"
        v-model:page-size="pageSize"
        :table-total="total"
        :table-data="tableData"
        :columns="COLUMNS"
        :default-page-size="PAGE_SIZE"
        :page-size-list="PAGE_SIZE_LIST"
        :row-class-name="rowClassName()"
        :on-submit="onTableSubmit"
      >
        <!-- 是否联系 -->
        <template #contact_flag="{ text }">
          <div>
            {{ text === '0' ? '未联系' : text === '1' ? '已联系' : '--' }}
          </div>
        </template>
        <!-- 详情 -->
        <template #detail="{ record }">
          <a-button type="link" @click="handleToDetail(record)">
            查看详情
          </a-button>
        </template>
      </custom-table>
    </div>
    <DistributeRule :clues-origin-options="cluesOriginOptions" />
  </div>
</template>

<script lang="ts">
import { defineComponent, onMounted, reactive, toRefs } from 'vue';
import ajax from '@/server/http';
import CustomTable, {
  useCustomTable,
} from '@/components/common/CustomTable.vue';
import DistributeRule from './distributeRule/index.vue';
import PARAMS from './const';
import { CluesListTableData } from './type';
import dayjs, { Dayjs } from 'dayjs';
import { message } from 'ant-design-vue';
import { fetchActivityTree } from '@/server/api';
import type { TreeSelectProps } from 'ant-design-vue';
dayjs.locale('zh-CN');

type iState = {
  total: number; // 表格记录总数
  tableData: CluesListTableData[]; // 表格数据
  date: [Dayjs, Dayjs]; // 搜索日期字段
  cluesOrigin: string; // 搜索线索来源字段
  allocate_flag: string | null; // 搜索资料状态字段
  selectType: string | null;
  xm: string; // 搜索姓名/伪码/基金客户号字段
  cluesOriginOptions: TreeSelectProps['treeData'];
};

export default defineComponent({
  name: 'ReconciliateReport',
  components: {
    CustomTable,
    DistributeRule,
  },
  setup() {
    const { table, onTableSubmit, onTableChange, rowClassName } =
      useCustomTable(PARAMS.PAGE_SIZE);
    const state = reactive<iState>({
      total: 0,
      tableData: [],
      date: [
        dayjs(new Date(new Date().getTime() - 24 * 3600 * 1000)),
        dayjs(new Date()),
      ],
      cluesOrigin: '',
      allocate_flag: null,
      selectType: null,
      xm: '',
      cluesOriginOptions: [],
    });
    /** methods */
    // 初始化
    const init = () => {
      fetchActivityList();
    };
    // 查询活动下拉
    const fetchActivityList = async () => {
      state.cluesOriginOptions = await fetchActivityTree({
        groupDisabled: true,
      });
    };
    // 判断所传搜索数据
    const searchInput = (searchVal: string, selectType: string) => {
      let body: { cust_id: string; cust_name: string } = {
        cust_id: '',
        cust_name: '',
      };
      if (searchVal) {
        switch (selectType) {
          case 'cust_id':
            body.cust_id = searchVal;
            break;
          case 'cust_name':
            body.cust_name = searchVal;
            break;
        }
      }
      return body;
    };
    // 查询表格
    const fetchTable = () => {
      ajax(
        'clues_table_list',
        {
          ...searchInput(state.xm, state.selectType),
          start_date: state.date[0].format('YYYY-MM-DD HH:mm:ss'),
          end_date: state.date[1].format('YYYY-MM-DD HH:mm:ss'),
          activity_id: state.cluesOrigin,
          allocate_flag: state.allocate_flag,
        },
        {
          pageNum: table.pageNum,
          pageSize: table.pageSize,
        }
      )
        .then((res: any) => {
          if (res) {
            state.total = res.total;
            state.tableData = res.record;
            state.tableData.map((item, i: number) => {
              item.index = i + 1 + (table.pageNum - 1) * table.pageSize;
            });
          } else {
            state.total = 0;
            state.tableData = [];
          }
        })
        .catch(() => {
          state.total = 0;
          state.tableData = [];
        });
    };
    // 搜索产品
    const handleSearch = () => {
      if (!state.cluesOrigin) {
        message.error('请选择线索来源');
        return;
      }
      onTableChange(fetchTable);
    };
    // 导出表格
    const exportTable = () => {
      const data = {
        ...searchInput(state.xm, state.selectType),
        start_date: state.date[0].format('YYYY-MM-DD HH:mm:ss'),
        end_date: state.date[1].format('YYYY-MM-DD HH:mm:ss'),
        activity_id: state.cluesOrigin || '',
        allocate_flag: state.allocate_flag || '',
      };
      ajax('clues_export', data);
    };

    onMounted(init);
    return {
      ...toRefs(table),
      ...toRefs(state),
      ...PARAMS,
      onTableSubmit: onTableSubmit(fetchTable),
      rowClassName,
      handleSearch,
      exportTable,
      fetchActivityList,
    };
  },
});
</script>

<style lang="less" scoped>
@import './index.less';
</style>
