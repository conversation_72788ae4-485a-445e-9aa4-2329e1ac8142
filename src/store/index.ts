import { createStore } from 'vuex';

const modules = {
  state() {
    return {
      loading: false, // 全局loading动作
      userInfo: {},
    };
  },
  mutations: {
    changeLoadingStatus(state, value) {
      state.loading = value;
    },
    changeUserInfo(state, value) {
      state.userInfo = value;
    },
  },
  actions: {
    showLoading(context) {
      context.commit('changeLoadingStatus', true);
    },
    hideLoading(context) {
      context.commit('changeLoadingStatus', false);
    },
    updateUserInfo(context, value) {
      context.commit('changeUserInfo', value);
    },
  },
};

const store = createStore(modules);

export default store;
