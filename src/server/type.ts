import type { RoleInfo } from '@/views/types';
export interface SaleInfo extends RoleInfo {
  role: number;
}

/** @description 接口地址: /crm/fund/resource/user/list */
export interface Resource {
  /** 资源ID */
  resourceId: number;
  /** 资源父级ID */
  parentId: number;
  /** URL */
  uri: string;
  /** 是否进行权限控制, 1: 不控制，2: 控制 */
  isCheck: number;
  resourceClass: number;
}
export interface ResourceList {
  /** 资源json数组 */
  resp_list: Resource[];
}

/** @description 接口地址: /crm/fund/common/sale_tree/v1/list_auth */
export interface SaleTreeData {
  /** 组id */
  group_id: number;
  /** 组名 */
  group_name: string;
  /** 子组 */
  sub_group_list: SaleTreeData[];
  sale: Array<{
    /** 销售id */
    id: number;
    /** 销售名称 */
    name: string;
  }>;
}

export interface ActivityList {
  /** 活动id */
  activity_id: string;
  /** 活动名称 */
  activity_name: string;
}

/** @description 接口地址: /crm/fund/tracking/data/v1/get_activity_tree */
export interface ActivityTreeData {
  /** 分组代码 */
  code: string;
  /** 分组名称 */
  name: string;
  activity_list: ActivityList[];
}
