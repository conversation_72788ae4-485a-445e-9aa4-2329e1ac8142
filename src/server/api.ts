import ajax from './http';
import { env } from '@/utils/request';
import type { TreeSelectProps } from 'ant-design-vue';
import type { RoleInfo } from '@/views/types';
import type {
  ActivityTreeData,
  ResourceList,
  Resource,
  SaleInfo,
  SaleTreeData,
} from './type';
import { RoleAuth } from '@/utils/const';

export const localDev = env === 'dev';
export const localTest = env === 'dev' || env === 'test';

/**
 * 获取用户信息
 * @returns Promise<any>
 */
export const fetchUserInfo: () => Promise<any> = () => {
  return ajax('user_info');
};

/**
 *
 * @param resourceId 资源id
 * @param resourceList 资源列表
 * @returns 是否有权限，即查询的资源id是否在资源列表中
 */
const checkPermission = (resourceId: number, resourceList: Resource[]) => {
  let hasPermission = false;
  if (Array.isArray(resourceList) && resourceList.length > 0) {
    hasPermission = resourceList.some(item => item.resourceId === resourceId);
  }
  return hasPermission;
};
/**
 * 获取用户页面对应权限
 * @param resourceId 页面id
 * @returns Promise<boolean>
 */
export function fetchUserAdmin(resourceId: number[]): Promise<boolean[]>;
export function fetchUserAdmin(resourceId: number): Promise<boolean>;
export function fetchUserAdmin(
  resourceId: number | number[]
): Promise<boolean | boolean[]> {
  return ajax('user_admin', {
    param: location.hash.slice(1).replace(/\?.*$/, ''),
  })
    .then((res: ResourceList) => {
      if (typeof resourceId === 'number') {
        return checkPermission(resourceId, res?.resp_list);
      } else if (Array.isArray(resourceId)) {
        return resourceId.map(id => {
          return checkPermission(id, res?.resp_list);
        });
      }
    })
    .catch(e => {
      console.log('获取用户页面对应权限失败：', e);
      return typeof resourceId === 'number'
        ? false
        : resourceId.map(() => false);
    });
}
/**
 * 获取销售信息
 * @param siId 销售id（为null时查看证书id）
 * @param roles 需要获取角色列表
 * @returns Promise<any>
 */
export const fetchSaleInfo: (
  siId: number | null,
  roles?: number[]
) => Promise<SaleInfo> = (
  siId: number | null,
  roles: number[] = [RoleAuth.chief, RoleAuth.groupLeader, RoleAuth.groupMember]
) =>
  ajax('sale_info', { siId })
    .then((res: RoleInfo) => {
      // 53 总监；56 组长；57 组员
      const _roles = [...roles];
      const _roleList = (res && res.roleIdSet) || [];
      let role = 0;
      for (const _role of _roles) {
        if (~_roleList.indexOf(_role)) {
          role = _role;
          break;
        }
      }
      return {
        ...res,
        role,
      };
    })
    .catch(e => {
      console.log('获取销售身份信息失败：', e);
      throw e;
    });
/**
 * 拨打电话
 * @param pseudocode 伪码
 * @param aiId 用户aiId
 * @param phoneNumber 电话号码
 */
export const callTelPhone = (
  pseudocode: string,
  aiId?: string,
  phoneNumber?: string
): void => {
  ajax('getPhoneBase64', {
    pseudocode,
  })
    .then((data: string) => {
      // 更新用户最近一次被拨打的手机号
      if (aiId && phoneNumber) {
        ajax('updatePhoneNumber', null, {
          aiId: aiId,
          phone: phoneNumber,
        }).catch((err: unknown) => {
          console.log('更新用户最近一次被拨打的手机号接口错误：', err);
        });
      }
      const a = document.createElement('a');
      a.setAttribute('href', `crm://m=1&p=${data}`);
      a.setAttribute('id', 'js_a');
      //防止反复添加
      if (document.getElementById('js_a')) {
        document.body.removeChild(document.getElementById('js_a'));
      }
      document.body.appendChild(a);
      a.click();
    })
    .catch((err: unknown) => {
      console.log('拨打电话模块接口错误：', err);
      throw err;
    });
};

/**
 * 判断用户是否是某些角色之一
 * @param roles 角色列表
 * @returns 是否在角色列表中
 */
export const isInRoles = (roles: Array<number>): Promise<boolean> => {
  return ajax('sale_info')
    .then(res => {
      return res.roleIdSet.some((item, index) => {
        return roles.includes(item);
      });
    })
    .catch(e => {
      console.log('获取销售身份信息失败：', e);
      return false;
    });
};

export const getOACommonUserInfo = gh => {
  return ajax('getOACommonUserInfo', { sale_gh: gh })
    .then(res => {
      return res;
    })
    .catch(e => {
      console.log('获取销售身份信息失败：', e);
      return false;
    });
};
/**
 * 获取销售树，并处理成组件可直接使用的格式
 * @param isSupervise 是否为监管版本的销售树
 * @returns 符合a-tree-select组件使用的treeData
 */
export const fetchSaleTree = (
  isSupervise = false
): Promise<TreeSelectProps['treeData']> => {
  // 递归函数对所有组对象进行格式转换
  const formatData = (rawData: SaleTreeData[]) => {
    return rawData?.map(item => {
      // 1. 对item本身做格式转换
      const treeFormattedItem = {
        value: Number(item.group_id),
        label: item.group_name,
        selectable: false,
      };
      // 2. 对item子节点数据（sub_group_list + sale）做格式转换，使用processedChildren收集处理结果
      const processedChildren = [];
      // 2.1 对于sub_group_list，按照组合的方式进行递归处理
      if (
        Array.isArray(item['sub_group_list']) &&
        item['sub_group_list'].length > 0
      ) {
        processedChildren.push(...formatData(item['sub_group_list']));
      }
      // 2.2 对于sale，只需要进行格式转换，不需要使用formatData函数处理
      if (Array.isArray(item['sale']) && item['sale'].length > 0) {
        processedChildren.push(
          ...item['sale'].map(sale => {
            return {
              value: Number(sale.id),
              label: sale.name,
              selectable: true,
            };
          })
        );
      }
      // 2.3 将处理好的数据赋给结果的children字段
      if (processedChildren.length > 0) {
        treeFormattedItem['children'] = processedChildren;
      }
      return treeFormattedItem;
    });
  };
  return ajax(`d_pool_sale_tree${isSupervise ? '_hg' : ''}`)
    .then(res => {
      const treeData = [];
      if (res) {
        treeData.push(...formatData([res]));
      }
      return treeData;
    })
    .catch(e => {
      console.log('获取销售树失败', e);
      return [];
    });
};

/**
 *
 * @param option 获取活动树参数
 * @param option.groupDisabled 是否禁用分组
 * @param option.condition 0或空：全部；1-网销；
 * @param option.audit 1: 审核 0：未审核
 * @returns 符合a-tree-select组件使用的treeData
 */
export const fetchActivityTree = (option?: {
  condition?: number;
  audit?: number;
  groupDisabled?: boolean;
}): Promise<TreeSelectProps['treeData']> => {
  const { groupDisabled = false, condition, audit } = option || {};
  return ajax('clues_activity_tree', { condition, audit })
    .then((rawData: ActivityTreeData[]) => {
      let treeData: TreeSelectProps['treeData'] = [];
      if (rawData && rawData.length > 0) {
        treeData = rawData.map(item => ({
          value: item.code,
          label: item.name,
          disabled: groupDisabled,
          children: item.activity_list.map(activity => ({
            value: activity.activity_id,
            label: activity.activity_name,
          })),
        }));
      }
      return treeData;
    })
    .catch(e => {
      console.log('获取活动树失败', e);
      return [];
    });
};
