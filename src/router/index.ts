import { createRouter, createWebHashHistory, RouteRecordRaw } from 'vue-router';
import { createRouterGuards } from './router-guards';
import CustomLayout from '@/layout/customLayout.vue';

import 'nprogress/css/nprogress.css'; // 进度条样式

const routes = [
  // 红包后台活动列表
  {
    path: '/packet',
    name: '红包后台',
    component: CustomLayout,
    children: [
      // 活动列表页
      {
        path: 'activities',
        name: 'packetActivities',
        meta: {
          title: '活动项目列表',
        },
        component: () => import('@/views/packet/activities/index.vue'),
      },
      // 活动列表审核页
      {
        path: 'verify',
        name: 'packetVerify',
        meta: {
          title: '活动项目审核',
        },
        component: () => import('@/views/packet/activities/index.vue'),
      },
      // 红包使用查询
      {
        path: 'red-query',
        name: 'redQuery',
        meta: {
          title: '红包使用查询',
        },
        component: () => import('@/views/packet/red-query/index.vue'),
      },
      // 券模板
      /** 券模板列表 */
      {
        path: 'coupon/template/list',
        name: 'couponTemplateList',
        meta: {
          title: '券模板列表',
        },
        component: () => import('@/views/packet/coupon/CouponTemplateList.vue'),
      },
      /** 券模板审核 */
      {
        path: 'coupon/template/verify',
        name: 'couponTemplateVerify',
        meta: {
          title: '券模板列表',
        },
        component: () => import('@/views/packet/coupon/CouponTemplateList.vue'),
      },
      /** 券模板详情 */
      {
        path: 'coupon/template/detail',
        name: 'couponTemplateDetail',
        meta: {
          title: '券模板详情',
        },
        component: () =>
          import('@/views/packet/coupon/CouponTemplateDetail.vue'),
      },
      /** 券活动列表 */
      {
        path: 'coupon/list',
        name: 'couponList',
        meta: {
          title: '券活动列表',
        },
        component: () => import('@/views/packet/coupon/CouponList.vue'),
      },
      /** 券活动详情 */
      {
        path: 'coupon/detail',
        name: 'couponDetail',
        meta: {
          title: '券活动详情',
        },
        component: () => import('@/views/packet/coupon/CouponDetail.vue'),
      },
      // 明细页
      // 领取明细
      {
        path: 'detail/receive',
        name: 'receiveDetail',
        meta: {
          title: '领取明细',
        },
        component: () => import('@/views/packet/details/ReceiveDetail.vue'),
      },
      // 使用明细
      {
        path: 'detail/use',
        name: 'useDetail',
        meta: {
          title: '使用明细',
        },
        component: () => import('@/views/packet/details/UseDetail.vue'),
      },
      // 红包明细
      {
        path: 'detail/red',
        name: 'redDetail',
        meta: {
          title: '红包明细',
        },
        component: () => import('@/views/packet/details/RedDetail.vue'),
      },
      // 券黑名单相关
      {
        path: 'coupon/blacklist/list',
        name: 'blacklist',
        meta: {
          title: '券黑名单列表',
        },
        component: () => import('@/views/packet/coupon/Blacklist.vue'),
      },
      {
        path: 'coupon/blacklist/verify',
        name: 'blacklistVerify',
        meta: {
          title: '券黑名单审核',
        },
        component: () => import('@/views/packet/coupon/Blacklist.vue'),
      },
      {
        path: 'coupon/blacklist/detail',
        name: 'blacklistDetail',
        meta: {
          title: '券黑名单详情',
        },
        component: () => import('@/views/packet/coupon/BlacklistDetail.vue'),
      },
    ],
  },
  {
    path: '',
    hidden: true,
    redirect: '/index',
  },
  {
    path: '/strategy-detail',
    name: '策略详情',
    component: () => import('@/views/strategy-detail/index.vue'),
    meta: {
      title: '策略详情',
    },
  },
  {
    path: '/login',
    name: 'login',
    hidden: true,
    component: () => import('@/views/login.vue'),
    meta: {
      title: '登陆',
    },
  },
  {
    path: '/system',
    name: 'system',
    meta: {
      title: '系统信息',
    },
    component: CustomLayout,
    children: [
      {
        path: 'black-add',
        name: 'black-add',
        meta: {
          title: '导入黑名单',
        },
        component: () =>
          import('@/views/systemInfo/blackMenu/BlackMenuAdd.vue'),
      },
    ],
  },
  {
    path: '/customer',
    name: '客户相关',
    meta: {
      title: '客户相关',
    },
    component: CustomLayout,
    children: [
      {
        path: 'info',
        name: 'customerInfo',
        meta: {
          title: '客户信息',
        },
        component: () => import('@/views/customer/newDetail/index.vue'),
      }, // 客户详情页
    ],
  },
  {
    path: '/market',
    name: '销售相关',
    meta: {
      title: '销售相关',
    },
    component: CustomLayout,
    children: [
      {
        path: 'clues-push',
        name: 'cluesPush',
        meta: {
          title: '电销线索推送',
        },
        component: () => import('@/views/market/cluesPush/index.vue'),
      },
    ],
  },
  {
    path: '/person',
    name: '人事相关',
    meta: {
      title: '人事相关',
    },
    component: CustomLayout,
    children: [
      {
        path: 'salary',
        name: 'salary',
        meta: {
          title: '工资核算',
        },
        component: () => import('@/views/person/salary/index.vue'),
      },
    ],
  },
  {
    path: '/operate',
    name: '运营相关',
    meta: {
      title: '运营相关',
    },
    component: CustomLayout,
    children: [
      {
        path: 'production',
        name: 'production',
        meta: {
          title: '产品列表',
        },
        component: () => import('@/views/operate/production.vue'),
      },
    ],
  },
  // TODO 2022.3.8 意见反馈
  {
    path: '/customer-service',
    name: '意见反馈中心',
    component: CustomLayout,
    children: [
      {
        path: 'feedback',
        name: 'feedback',
        meta: {
          title: '意见发聩',
        },
        component: () => import('@/views/customer-service/feedback/index.vue'),
      },
      {
        path: 'simu-feedback',
        name: 'simu-feedback',
        meta: {
          title: '私募回访',
        },
        component: () => import('@/views/customer-service/simu/index.vue'),
      },
    ],
  },
  // 红包后台活动列表
  {
    path: '/packet',
    name: '红包后台',
    component: CustomLayout,
    children: [
      // 活动列表页
      {
        path: 'activities',
        name: 'packetActivities',
        meta: {
          title: '活动项目列表',
        },
        component: () => import('@/views/packet/activities/index.vue'),
      },
      // 活动列表审核页
      {
        path: 'verify',
        name: 'packetVerify',
        meta: {
          title: '活动项目审核',
        },
        component: () => import('@/views/packet/activities/index.vue'),
      },
      // 红包使用查询
      {
        path: 'red-query',
        name: 'redQuery',
        meta: {
          title: '红包使用查询',
        },
        component: () => import('@/views/packet/red-query/index.vue'),
      },
      // 券模板
      /** 券模板列表 */
      {
        path: 'coupon/template/list',
        name: 'couponTemplateList',
        meta: {
          title: '券模板列表',
        },
        component: () => import('@/views/packet/coupon/CouponTemplateList.vue'),
      },
      /** 券模板审核 */
      {
        path: 'coupon/template/verify',
        name: 'couponTemplateVerify',
        meta: {
          title: '券模板列表',
        },
        component: () => import('@/views/packet/coupon/CouponTemplateList.vue'),
      },
      /** 券模板详情 */
      {
        path: 'coupon/template/detail',
        name: 'couponTemplateDetail',
        meta: {
          title: '券模板详情',
        },
        component: () =>
          import('@/views/packet/coupon/CouponTemplateDetail.vue'),
      },
      /** 券活动列表 */
      {
        path: 'coupon/list',
        name: 'couponList',
        meta: {
          title: '券活动列表',
        },
        component: () => import('@/views/packet/coupon/CouponList.vue'),
      },
      /** 券活动详情 */
      {
        path: 'coupon/detail',
        name: 'couponDetail',
        meta: {
          title: '券活动详情',
        },
        component: () => import('@/views/packet/coupon/CouponDetail.vue'),
      },
      // 明细页
      // 领取明细
      {
        path: 'detail/receive',
        name: 'receiveDetail',
        meta: {
          title: '领取明细',
        },
        component: () => import('@/views/packet/details/ReceiveDetail.vue'),
      },
      // 使用明细
      {
        path: 'detail/use',
        name: 'useDetail',
        meta: {
          title: '使用明细',
        },
        component: () => import('@/views/packet/details/UseDetail.vue'),
      },
      // 红包明细
      {
        path: 'detail/red',
        name: 'redDetail',
        meta: {
          title: '红包明细',
        },
        component: () => import('@/views/packet/details/RedDetail.vue'),
      },
      // 券黑名单相关
      {
        path: 'coupon/blacklist/list',
        name: 'blacklist',
        meta: {
          title: '券黑名单列表',
        },
        component: () => import('@/views/packet/coupon/Blacklist.vue'),
      },
      {
        path: 'coupon/blacklist/verify',
        name: 'blacklistVerify',
        meta: {
          title: '券黑名单审核',
        },
        component: () => import('@/views/packet/coupon/Blacklist.vue'),
      },
      {
        path: 'coupon/blacklist/detail',
        name: 'blacklistDetail',
        meta: {
          title: '券黑名单详情',
        },
        component: () => import('@/views/packet/coupon/BlacklistDetail.vue'),
      },
    ],
  },
  // 财务相关
  {
    path: '/financial',
    name: '财务相关',
    component: CustomLayout,
    children: [
      {
        path: 'capitalFlowReport',
        name: 'capitalFlowReport',
        meta: {
          title: '资金流水报表',
        },
        component: () =>
          import('@/views/financial/capitalFlowReport/index.vue'),
      },
      {
        path: 'capitalFlowReport/detail',
        name: 'capitalFlowReport-detail',
        meta: {
          title: '资金流水明细',
        },
        component: () =>
          import('@/views/financial/capitalFlowReport/detail/index.vue'),
      },
    ],
  },
  // 网销相关
  {
    path: '/netSale',
    name: '网销相关',
    component: CustomLayout,
    children: [
      {
        path: 'netSaleDatabase',
        name: 'netSaleDatabase',
        meta: {
          title: '网销资料库',
        },
        component: () => import('@/views/netSale/netSaleDatabase/index.vue'),
      },
    ],
  },
  // 电销相关
  {
    path: '/eSale',
    name: '电销相关',
    component: CustomLayout,
    children: [
      {
        path: 'eSaleDatabase',
        name: 'eSaleDatabase',
        meta: {
          title: '电销资料库',
        },
        component: () => import('@/views/eSale/eSaleDatabase/index.vue'),
      },
    ],
  },
  // 小喇叭
  {
    path: '/smallTrumpet',
    name: '小喇叭',
    component: CustomLayout,
    children: [
      {
        path: 'index',
        name: 'config',
        meta: {
          title: '基金小喇叭',
        },
        component: () => import('@/views/smallTrumpet/config/index.vue'),
      },
    ],
  },
  // 高端理财相关
  {
    path: '/highFinancial',
    name: '高端理财相关',
    component: CustomLayout,
    children: [
      {
        path: 'investorCertificate',
        name: 'investorCertificate',
        meta: {
          title: '网销业绩统计',
        },
        component: () =>
          import('@/views/highFinancial/investorCertificate/index.vue'),
      },
    ],
  },
  // 网银相关
  {
    path: '/bankTransfer',
    name: '网银转账',
    component: CustomLayout,
    children: [
      {
        path: 'index',
        name: 'index',
        meta: {
          title: '网银转账对账表',
        },
        component: () => import('@/views/bankTransfer/index.vue'),
      },
    ],
  },
  {
    path: '/clueStrategy',
    name: 'clueStrategy',
    meta: {
      title: '线索策略',
    },
    component: CustomLayout,
    children: [
      {
        path: 'list',
        name: 'list',
        meta: {
          title: '策略列表',
        },
        component: () => import('@/views/clueStrategy/list/index.vue'),
      },
    ],
  },
  // 机构相关
  {
    path: '/organization',
    name: '机构相关',
    component: CustomLayout,
    children: [
      {
        path: 'customerInfo',
        name: 'customerInfoOrg',
        meta: {
          title: '客户信息',
        },
        component: () => import('@/views/organization/customerInfo/index.vue'),
      },
      {
        path: 'costRegister',
        name: 'costRegister',
        meta: {
          title: '成本项登记',
        },
        component: () => import('@/views/organization/costRegister/index.vue'),
      },
      {
        path: 'costRegisterNew',
        name: 'costRegisterNew',
        meta: {
          title: '扣减管理',
        },
        component: () =>
          import('@/views/organization/costRegisterNew/index.vue'),
      },
      {
        path: 'investAccount',
        name: 'investAccount',
        meta: {
          title: '投资账户管理',
        },
        component: () => import('@/views/organization/investAccount/index.vue'),
      },
      {
        path: 'costInvestAudit',
        name: 'costInvestAudit',
        meta: {
          title: '审核列表',
        },
        component: () =>
          import('@/views/organization/costInvestAudit/index.vue'),
      },
    ],
  },
  // 智能外呼线索池
  {
    path: '/intelCallCluePool',
    name: 'intelCallCluePool',
    meta: {
      title: '智能外呼线索池',
    },
    component: () => import('@/views/intelCallCluePool/index.vue'),
  },
  // OA排行
  {
    path: '/OARank',
    name: 'OARank',
    component: () => import('@/views/OARank/index.vue'),
  },
  // 合规页面
  {
    path: '/tools',
    name: 'tools',
    children: [
      /** 电销资料库合规版 */
      {
        path: 'custDatabase',
        name: 'supervise-cust-database',
        meta: {
          title: '客户资料库',
        },
        component: () => import('@/views/supervise/custDatabase/index.vue'),
      },
      /** 新建优惠券（合规） */
      {
        path: 'coupon/template/new/list',
        name: 'supervise-coupon-template-list',
        meta: {
          title: '新建优惠券',
        },
        component: () =>
          import('@/views/supervise/coupon/CouponTemplateList.vue'),
      },
      /** 审核优惠券（合规） */
      {
        path: 'coupon/template/new/verify',
        name: 'supervise-coupon-template-verify',
        meta: {
          title: '审核优惠券',
        },
        component: () =>
          import('@/views/supervise/coupon/CouponTemplateList.vue'),
      },
      /** 券模板详情（合规） */
      {
        path: 'coupon/template/new/detail',
        name: 'supervise-coupon-template-detail',
        meta: {
          title: '券模板详情',
        },
        component: () =>
          import('@/views/supervise/coupon/CouponTemplateDetail.vue'),
      },
    ],
  },
  // amis通用页面
  {
    path: '/basic',
    name: 'basic',
    component: () => import('@/views/basic/index.vue'),
  },
];

const router = createRouter({
  // process.env.BASE_URL
  history: createWebHashHistory(''),
  routes: routes as Array<RouteRecordRaw>,
});

createRouterGuards(router);

export default router;
