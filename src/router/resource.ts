import { localTest } from '@/server/api';

const resource: {
  [key: string]: { test: Record<string, number>; prod: Record<string, number> };
} = {
  // 首页数据看板
  '/fund/home': {
    test: {
      group_all_detail: 3071, // 查看所有小组权限
      group_slef_detail: 3072, // 查看所属小组权限
    },
    prod: {
      group_all_detail: 3071,
      group_slef_detail: 3072,
    },
  },
  // 产品列表
  '/operate/production': {
    test: {
      production_edit: 3089, // 新增/编辑产品权限
      production_del: 3090, // 删除产品权限
    },
    prod: {
      production_edit: 3089,
      production_del: 3090,
    },
  },
  // 红包后台
  '/packet/activities': {
    test: {
      project_add: 3098,
      project_verify: 3099,
    },
    prod: {
      project_add: 3098,
      project_verify: 3099,
    },
  },
  // 红包 - 券活动列表
  '/packet/coupon/list': {
    test: {
      coupon_list_verify: 3120,
    },
    prod: {
      coupon_list_verify: 3123,
    },
  },
  // 客户详情页
  '/customer/info': {
    test: {
      net_record_verify: 3182, // 查看更多净增量数据权限
    },
    prod: {
      net_record_verify: 3182,
    },
  },
  // 新版个人销售业绩统计页
  '/market/new-kpi/new-personal': {
    test: {
      personal_verify: 3186, // 查看更多增量详情
    },
    prod: {
      personal_verify: 3188,
    },
  },
  // 旧版个人销售业绩统计页
  '/market/new-kpi/personal': {
    test: {
      personal_verify: 3114, // 查看更多增量详情
    },
    prod: {
      personal_verify: 3114,
    },
  },
  // 新版财务对账单二级页面
  '/financial/newReconciliateReport/detail': {
    test: {
      column_verify: 3188, // 查看更多列权限
    },
    prod: {
      column_verify: 3188,
    },
  },
  // 个人销售业绩统计页24合规版
  '/tools/newKpi/newPersonal': {
    test: {
      personal_verify: 3186, // 查看更多增量详情
    },
    prod: {
      personal_verify: 3197,
    },
  },
  // 个人销售业绩统计页23合规版
  '/tools/newKpi/kpiTable': {
    test: {
      personal_verify: 3114, // 查看更多增量详情
    },
    prod: {
      personal_verify: 3196,
    },
  },
  // 新版财务对账单二级页面合规版
  '/tools/newReconciliateReport/detail': {
    test: {
      column_verify: 3188, // 查看更多列权限
    },
    prod: {
      column_verify: 3198,
    },
  },
};
// 取哈希路由，并去掉可能的参数，添加兜底逻辑
export default resource[location.hash.slice(1).replace(/\/?\?.*$/, '')]?.[
  localTest ? 'test' : 'prod'
] || {};
