import { Router } from 'vue-router';
import NProgress from 'nprogress'; // 顶部进度条

NProgress.configure({ showSpinner: false }); // 进度条配置

// eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types
export function createRouterGuards(router: Router) {
  router.beforeEach((to, from, next) => {
    NProgress.start();
    const token = '';
    if (token) {
      if (to.path === '/login') {
        next();
        NProgress.done();
      } else {
        const hasRoute = router.hasRoute(to.name || '');
        if (hasRoute) {
          next();
          NProgress.done();
        }
      }
    } else {
      next();
      NProgress.done();
    }
  });

  router.afterEach((to, from, failure) => {
    NProgress.done();
  });

  router.onError((error) => {
    console.log(error, '路由错误');
  });
}
