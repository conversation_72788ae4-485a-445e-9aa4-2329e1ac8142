export enum Gateway {
  CRM_FUND = '/crm/fund',
  IMS_CRM = '/ims/crm',
}

// 网关切换器
export class GatewaySwitcher {

  private baseHost: string;
  private gateway: Gateway;
  
  /**
   * @param baseHost 请求域名(+端口)
   * @param gateway 网关
   */
  constructor(baseHost: string, gateway: Gateway = Gateway.CRM_FUND) {
    this.baseHost = baseHost;
    this.gateway = gateway;
  }

  // 根据传入的网关动态构建请求前缀
  getUrlPrefix(gateway: Gateway = this.gateway): string {
    return `${this.baseHost}${gateway}`
  }
}