type Header = {
  key: string;
  title: string;
};

export default function (
  exportData: any[],
  header: Header[],
  fileName: string
) {
  // 导出文件名称
  const _fileName = `${fileName}.csv`;
  let fileContent = ''; // 文件内容
  const _header = header.map((item) => item.title).join(','); // 用逗号拼接的字符串
  const keys = header.map((item) => item.key); // 数组
  fileContent = fileContent + _header + '\n';
  exportData.forEach((item, index) => {
    let dataString = '';
    for (let i = 0; i < keys.length; i++) {
      dataString += item[keys[i]] + ',';
    }
    fileContent +=
      index < exportData.length
        ? dataString.replace(/,$/, '\n')
        : dataString.replace(/,$/, '');
  });
  console.log(fileContent);
  const a = document.createElement('a');
  a.href = encodeURI(`data:text/plain;charset=utf-8,\ufeff${fileContent}`);
  a.download = _fileName;
  a.click();
}
