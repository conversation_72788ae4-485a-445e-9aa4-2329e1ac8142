/*
 * @Author: like4
 * @Date: 2023-04-26 19:37:59
 * @LastEditTime: 2023-05-04 13:23:35
 */
import { amisHttpRequest, env } from '@/utils/request';

/**
 * amis env 配置文档请见
 * https://aisuda.bce.baidu.com/amis/zh-CN/docs/start/getting-started#env
 */

/**
 * @description: amis中的网络请求处理
 * @param {*} url 接口地址
 * @param {*} method 请求方法 get、post、put、delete
 * @param {*} data 请求数据
 * @param {*} config 其他配置
 * @return {*} Promise<fetcherResult>
 */
const fetcher = ({ url, method, data, config }) => {
  return amisHttpRequest(method, url, data, config);
};

// web页各环境地址
const HOST = {
  dev: 'http://localhost:8080/', //开发环境本地地址
  test: 'https://testcrm.5ifund.com:8443/',
  pre: 'https://crm.5ifund.com:5000/pre/',
  prod: 'https://crm.5ifund.com:5000/',
};

/**
 * @description: amis中的跳转处理
 * @param {*} to 跳转地址
 * @param {*} action 行为对象
 * @param {*} ctx 上下文对象
 * @return {*}
 */
const jumpTo = (to, action, ctx) => {
  const url = HOST[env] + to;
  window.open(url);
};

///去掉配置中请求的域名
const replaceURLDoamin = {
  replaceText: {
    'https://trade.5ifund.com:8443/crm/fund': '',
    'https://testcrm.5ifund.com:8443/crm/fund': '',
    'https://crm.5ifund.com:5000/pre/crm/fund': '',
  },
  replaceTextKeys: ['api', 'source', 'initApi'],
};

export default {
  fetcher,
  jumpTo,
  ...replaceURLDoamin,
};
