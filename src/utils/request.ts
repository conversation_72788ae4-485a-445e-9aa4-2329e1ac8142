import Axios, { AxiosPromise } from 'axios';
import { message } from 'ant-design-vue';
import NProgress from 'nprogress';
import { useRouter } from 'vue-router';
import { localData, systemMonitor } from './common';
import store from '../store/index';

const locationHref = window.location.href;
let env: 'dev' | 'test' | 'pre' | 'prod' = 'dev';
switch (true) {
  case locationHref.includes('testcrm'):
  case locationHref.includes('172.19.80'): {
    env = 'test'; //测试环境
    break;
  }
  case locationHref.includes('crm.5ifund.com:5000'): {
    if (locationHref.includes('/pre/')) {
      env = 'pre'; //预发布环境
    } else {
      env = 'prod'; //正式环境
    }
    break;
  }
}

const AMIS_ENV_MAP: Record<'dev' | 'test' | 'pre' | 'prod', string> = {
  dev: 'test',
  test: 'test',
  pre: 'test',
  prod: 'prod',
};

const AMIS_ENV = AMIS_ENV_MAP[env];
const DEFAULT_SYSTEM_ID = 15;

const allUrls = {
  dev: '/crm/fund', //开发环境工程代理地址
  test: 'https://testcrm.5ifund.com:8443/crm/fund',
  pre: 'https://crm.5ifund.com:5000/pre/crm/fund',
  prod: 'https://crm.5ifund.com:5000/prod/crm/fund',
};

// web页各环境地址
const allWebUrls = {
  dev: 'http://localhost:8080/', //开发环境本地地址
  test: 'https://testcrm.5ifund.com:8443/crmweb/fund/index.html',
  pre: 'https://crm.5ifund.com:5000/pre/crmweb/fund/index.html',
  prod: 'https://crm.5ifund.com:5000/crmweb/fund/index.html',
};

const baseURL = allUrls[env];
const baseWebURL = allWebUrls[env];

const axios = Axios.create({
  baseURL,
  withCredentials: true,
  timeout: 10 * 1000,
});

const GLOBAL_STATUS = {
  '500': '操作失败',
  '-1': '服务器内部异常',
  '101': '请求参数校验失败',
  '102': '数据转换失败',
  '103': '请求方式错误',
  '104': '缺少请求参数',
  '105': '权限用户不存在，请重新登陆',
  '106': '请求路径不存在',
};

const LOGIN_STATUS = {
  '500': '操作失败',
  '-1': '服务器内部异常',
  '10401': '请先登陆账号',
  '10403': '未开通IP白名单',
  '10404': '资源路径不存在',
  '10405': '没有资源访问权限',
  '10418': '未获取到证书',
  '10419': '用户证书不存在',
  '10420': '密码错误',
};

message.config({
  top: '10px',
  duration: 1.5,
  maxCount: 2,
});

axios.interceptors.request.use(
  (config) => {
    NProgress.start();
    store.dispatch('showLoading');
    message.destroy();
    return config;
  },
  (error) => {
    console.error(error);
    return Promise.reject(error);
  }
);

axios.interceptors.response.use(
  (response) => {
    NProgress.done();
    store.dispatch('hideLoading');
    let msg = '';
    if (response.status === 200) {
      if (response.data.status_code === 0 || response.data.code === 0) {
        return Promise.resolve(response.data.data);
      } else {
        if (response.config.url.indexOf('login') > -1) {
          msg = LOGIN_STATUS[response.data.code] || response.data.msg;
        } else {
          msg =
            response.data.status_msg ||
            response.data.msg ||
            GLOBAL_STATUS[response.data.status_code];
        }
        message.error('请求失败：' + msg);
        systemMonitor.error(response.config.url + msg); // 监控
        return Promise.reject(msg);
      }
    } else {
      message.error(
        '请求失败：' + response.data.status_msg || response.data.msg
      );
      systemMonitor.error(
        response.config.url + response.data.status_msg || response.data.msg
      ); // 监控
      return Promise.reject(response.data.status_msg);
    }
  },
  (error) => {
    NProgress.done();
    store.dispatch('hideLoading');
    console.log(error); // 调试拦截失败
    message.error('请求失败');
    return Promise.reject(error);
  }
);

const defaultHeaders: any = {};
if (env === 'dev') {
  defaultHeaders.nginx_cert_thumbprint = '9FBB80C10D924DA6D76C8438AA42BDDB';
}

const get = (url: string, params: any = {}, headers?: any): AxiosPromise => {
  return axios({
    method: 'GET',
    url,
    params,
    headers: {
      ...defaultHeaders,
      ...headers,
    },
  });
};
const post = (
  url: string,
  data: any = {},
  params: any = {},
  config?: any
): AxiosPromise => {
  let headers: any;
  if (config) headers = config.headers || {};
  return axios({
    method: 'POST',
    url,
    params,
    data,
    headers: {
      ...defaultHeaders,
      'Content-Type': 'application/json; charset=UTF-8',
      ...headers,
    },
  });
};

const uploadFile = function (
  url: string,
  data: {
    [x: string]: any;
  },
  params: any = {},
  config: any = {
    headers: {
      'Content-Type':
        'multipart/form-data; boundary=----WebKitFormBoundaryp4MQF6NJlEOVNlZa',
    },
  },
  success?: (arg0: any) => void,
  onError?: (arg0: any) => void
): Promise<any> {
  const formData = new FormData();
  if (typeof data === 'object') {
    for (const key in data) {
      formData.append(key, data[key]);
    }
  }
  message.loading('文件上传中');
  return post(url, formData, params, config)
    .then((res) => {
      success && success(res);
      message.destroy();
      return res;
    })
    .catch((err) => {
      message.destroy();
      onError && onError(err);
      return err;
    });
};

const getUserAuthList = function (
  //根据当前路由获取权限
  onSuccess: () => void,
  onError: (Arg0: any) => void
): void {
  const router = useRouter();
  const currentPath = router.currentRoute.value.path;
  get('resource/user/list', {
    param: currentPath,
  })
    .then((res) => {
      if (Array.isArray(res['resp_list'])) {
        const result = {};
        res['resp_list'].forEach((_el) => {
          result[_el['resourceId']] = 'granted';
        });
        if (Object.keys(result).length > 0) {
          localData.set(currentPath + '_' + 'auth', result);
        }
        onSuccess();
      }
    })
    .catch((err) => {
      message.error('获取权限失败，请联系管理员');
      onError(err);
    });
};

const httpAxios = Axios.create({
  withCredentials: true,
});

const httpRequest = function (
  //处理特殊的不走配置域名的外部url
  method: 'post' | 'get',
  url: string,
  data: Record<string, unknown> | unknown
): Promise<unknown> {
  return new Promise((resolve, reject) => {
    if (method === 'get') {
      data = {
        params: data,
      };
    }
    httpAxios[method](url, data)
      .then((res) => {
        const response = res.data;
        NProgress.done();
        if (response['code'] || response['msg']) {
          response['status_code'] = response['code'];
          response['status_msg'] = response['msgs'];
        }
        if (
          response['status_code'] === 0 &&
          (response.data || response.status_msg)
        ) {
          resolve(response.data || response.status_msg);
        } else {
          const msg = response.status_msg || response.msg;
          reject(msg ? msg : '');
        }
      })
      .catch((err) => {
        reject(err);
      });
  });
};

const amisAxios = Axios.create({
  baseURL,
  withCredentials: true,
  timeout: 60 * 1000,
});
amisAxios.interceptors.response.use(
  (response) => {
    if (response.status === 200) {
      const { data } = response;
      if (response.data.status_code !== 0 && response.data.code !== 0) {
        const msg =
          response.data.status_msg ||
          response.data.msg ||
          GLOBAL_STATUS[response.data.status_code];
        systemMonitor.error(response.config.url + msg); // 监控
      }
      return Promise.resolve({
        ...response,
        data: {
          ...data,
          status: data.status_code,
          msg: data.status_msg,
        },
      });
    } else {
      systemMonitor.error(
        response.config.url + response.data.status_msg || response.data.msg
      ); // 监控
      return Promise.reject(response.data.status_msg);
    }
  },
  (error) => {
    systemMonitor.error(error); // 监控
    return Promise.reject(error);
  }
);
const amisHttpRequest = function (
  //处理特殊的不走配置域名的外部url
  method: 'post' | 'get',
  url: string,
  data: Record<string, unknown> | unknown,
  config?: any
): Promise<unknown> {
  let headers: any;
  if (config) headers = config.headers || {};
  if (method === 'get') {
    data = {
      params: data,
    };
  } else {
    headers['Content-Type'] = 'application/json; charset=UTF-8';
  }
  return amisAxios[method](url, data, {
    headers: {
      ...defaultHeaders,
      ...headers,
    },
  });
};

const stockListLike = async function (
  keyWord: string | number,
  onSuccess: (arg0: any) => void,
  onError: (arg0: any) => void
): Promise<any> {
  const res = await get('/recommendCase/searchStock', {
    pattern: keyWord,
  });
  if (Array.isArray(res['resp_list']) && res['resp_list'].length > 0) {
    onSuccess(res['resp_list']);
  } else {
    onError('请求失败');
  }
};

const downloadFile: (
  url: string,
  data: any,
  params: any,
  options?: {
    getByElement?: boolean;
    fileName?: string;
  }
) => void = function (
  url: string,
  data: any,
  params: any,
  options: {
    getByElement?: boolean;
    fileName?: string;
  } = {}
) {
  const link = `${baseURL}${url}`;
  const { getByElement, fileName } = options;
  if (getByElement) {
    const a = document.createElement('a');
    a.setAttribute('href', link);
    a.setAttribute('download', fileName);
    a.style.display = 'none';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
  }
  window.open(link);
};

export {
  get,
  post,
  uploadFile,
  getUserAuthList,
  stockListLike,
  httpRequest,
  downloadFile,
  amisHttpRequest,
  baseURL,
  baseWebURL,
  env,
  AMIS_ENV,
  DEFAULT_SYSTEM_ID,
};
