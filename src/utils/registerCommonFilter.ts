import {
  agGridRenderNumber,
  agGridRenderRate,
  agGridRenderPercent,
  getRatio,
} from '@/utils/fn';

export default function (amisLib) {
  //自定义filter
  amisLib.registerFilter('formatMoney', input => {
    return agGridRenderNumber({ value: input });
  });
  amisLib.registerFilter('formatRate', input => {
    return agGridRenderRate({ value: input });
  });
  amisLib.registerFilter('formatPercent', input => {
    return agGridRenderPercent({ value: input });
  });
  amisLib.registerFilter('formatRatio', (num1, num2) => {
    return getRatio(num1, num2);
  });
}
