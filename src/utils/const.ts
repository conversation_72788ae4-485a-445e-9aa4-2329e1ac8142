//角色权限
export enum RoleAuth {
  chief = 53, // 总监
  groupLeader = 56, // 销售_主管
  groupMember = 57, // 普通销售
  operation = 106, //运营
  custService = 109, // 售前客服
  custServiceGroup = 110, // 售前客服主管
  finance = 103, // 财务
  developer = 101, // 开发者
  organization_group = 114, // 机构_总监
  organization_operation = 113, // 机构_运营
}

// 页面枚举
export enum PageRouter {
  OLD_RECONCILIATE_REPORT_DETAIL = '/financial/reconciliateReport/detail', // 旧版财务对账单详情页
  NEW_RECONCILIATE_REPORT_DETAIL = '/financial/newReconciliateReport/detail', // 新版财务对账单详情页
  SUPERVISE_NEW_RECONCILIATE_REPORT_DETAIL = '/tools/newReconciliateReport/detail', // 新版财务对账单详情页（合规）
  CUSTOMER_INFO = '/customer/info', // 客户详情页
}

// 兜底文案
export const EMPTY_TEXT = '--';

// SaleTree组件自动选择的模式
export enum AutoSelectedMode {
  /** 无 */
  None = 0,
  /** 个人 */
  Personal = 1,
  /** 部门 */
  Group = 2,
}
