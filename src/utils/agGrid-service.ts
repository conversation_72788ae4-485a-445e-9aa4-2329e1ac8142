import { DefineComponent, defineComponent, VNode } from 'vue';

const columnDefGenerator = function (arr: unknown[], fields: any[]): unknown[] {
  const result = [];
  arr.forEach((el: any, index) => {
    let find = null;
    if (typeof el === 'string') {
      find = fields.find((_it) => _it.title === el);
    } else {
      find = fields.find((_it) => _it.title === el['headerName']);
    }
    if (find) {
      const param = {
        field: find.field,
        headerName: find.title,
        cellRenderer: el.cellRenderer,
        cellRendererParams: el.cellRendererParams,
        width: 100,
        sortable: true,
        filter: true,
        resizable: true,
        tooltipField: typeof el === 'string' ? find.field : '',
        cellStyle: { 'border-right': '1px solid #e6e6e6' },
      };
      if (typeof el === 'object') {
        param['renderer'] = el['renderer'];
        param['renderParams'] = el['renderParams'];
        if (param['field'] === 'operate') {
          param['pinned'] = 'right';
          param['width'] = 150;
        }
      }
      if (index === 0) {
        param['checkboxSelection'] = true;
        param['headerCheckboxSelection'] = true;
      }
      result.push(param);
    }
  });
  return result;
};

/**
 * agGrid函数式组件装饰器
 * @param fns 函数式组件对象
 * @returns defineComponents
 */
const createRenderers = function (fns: {
  [x: string]: (...params: any) => VNode
}): {
  [x: string]: DefineComponent<any>
} {
  const renders: any = {};
  for (const name in fns) {
    renders[name] = defineComponent({
      name,
      render: fns[name],
    })
  }
  return renders;
}


export { columnDefGenerator, createRenderers };
