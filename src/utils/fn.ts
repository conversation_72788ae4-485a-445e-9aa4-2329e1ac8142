/** 通用业务方法 */
import dayjs, { Dayjs } from 'dayjs';
import currency from 'currency.js';
import { EMPTY_TEXT } from './const';
dayjs.locale('zh-cn');

/**
 * 获取url中查询参数/路由参数
 * @param param 参数key
 * @param type search/hash 默认search
 * @param 用于查询的字符串 默认为location.href
 * @returns 参数值
 */
export function getUrlParam(param: string, type?: 'search' | 'hash'): string {
  if (!param) return '';
  type = type ? type : 'search';
  let str = location[type] || '';
  if (type === 'hash') {
    str = str.slice(str.indexOf('?'));
  }
  return getStrParam(str, param);
}

/**
 * 获取字符串中参数
 * @param str 输入字符串
 * @param param 要查询的参数
 * @returns 参数值
 */
export function getStrParam(str: string, param: string): string {
  if (!str) return '';
  const arr = str.slice(1).split('&');
  const map: {
    [key: string]: string;
  } = {};
  arr.forEach((item) => {
    const $param = item.split('=') || [];
    const key = $param[0];
    map[key] = $param[1] || '';
  });
  return map[param] || '';
}

/**
 * 获取本月时间范围
 * @param format 字符串形式格式化模板
 * @returns 起止时间
 */
export function getMonthRange(format?: string): string[] | Dayjs[] {
  const start = dayjs(dayjs(new Date()).format('YYYY-MM'));
  const end = dayjs(new Date());
  if (format) return [start.format(format), end.format(format)];
  else return [start, end];
}
/**
 * 获取近一个月
 * @returns 近一个月开始/结束日期
 */
export function getLastMonth(): [Dayjs, Dayjs] {
  const now = dayjs(new Date()).format('YYYY-MM-DD');
  return [dayjs(now).subtract(1, 'month'), dayjs(now)];
}
/**
 * 最大值
 * @param 比较值列表
 * @returns 最大值
 */
export function getMax(...data: number[]): number {
  if (data.length == 0) return 0;
  let max = 0;
  for (let i = 0; i < data.length; i++) {
    max = max > data[i] ? max : data[i];
  }
  return max;
}
/**
 * 最小值
 * @param 比较值列表
 * @returns 最小值
 */
export function getMin(...data: number[]): number {
  if (data.length == 0) return 0;
  let min = 0;
  for (let i = 0; i < data.length; i++) {
    min = min < data[i] ? min : data[i];
  }
  return min;
}
/**
 * 将yyyyMMdd处理为yyyy-MM-dd
 * @param date yyyyMMdd格式日期
 * @returns YYYY-MM-DD
 */
export function dealYYYYMMDD(date: string): string {
  if (!date) return EMPTY_TEXT;
  const dateArr = date.split('');
  dateArr.splice(6, 0, '-');
  dateArr.splice(4, 0, '-');
  return dateArr.join('');
}
/**
 * 安全显示
 * @param 检查是否需要安全显示的值
 * @returns 安全值
 */
export function safeValueShow(value: string | number): string | number {
  return !value && !equal(value as string, 0) ? EMPTY_TEXT : value;
}
/**
 * 判断数字是否相等
 * @param value 比较值 number | string
 * @param compare 基准
 * @returns true/false
 */
export function equal(value: string, compare = 0): boolean {
  return parseInt(value, 10) === compare;
}
/**
 * 检查数字格式
 * @param value 数字
 * @returns 是/否
 */
export function checkStringNum(value: string | number): boolean {
  if (typeof value === 'number') return true;
  const reg = /^(-|\+)?(0|[1-9]\d*)(\.\d+)?$/;
  return reg.test(value);
}
/**
 * 返回格式化金额
 * @param {number | string} value 数字
 * @param {object} options 配置项{ 前缀，后缀 }
 * @returns {string} money 金额
 */
export function formatMoney(
  value: number | string,
  options: {
    symbol?: string;
    pattern?: string;
    negativePattern?: string;
    separator?: string;
    precision?: number;
    [key: string]: any;
  } = {}
): string {
  // 检查数字类型
  if (typeof value !== 'number' && typeof value !== 'string') {
    return EMPTY_TEXT;
  }
  // 检查数字格式
  if (!checkStringNum(value)) return EMPTY_TEXT;
  // 格式化
  const defaultOptions = {
    symbol: '',
    pattern: '#!',
    negativePattern: '-#!',
    separator: '',
    precision: 2,
  };
  options = {
    ...defaultOptions,
    ...options,
  };
  return currency(value, options).format();
}
/**
 * 将数字转换为千位制
 * @param value
 * @returns str
 */
export function numToThousand(value: string | number): string {
  const type = typeof value;
  if (type !== 'string' && type !== 'number')
    console.error('请输入类型为string或number的值.');
  const total = value.toString().split('');
  const parts = [];
  while (total.length > 3) {
    parts.push(total.splice(-3, 3).join(''));
  }
  if (total.length > 0) parts.push(total.join(''));
  return parts.reverse().join(',');
}
/**
 * 分割文字
 * @param str 分割对象
 * @param char 分割符
 * @returns 返回分割结果
 */
export const sliceString = (
  str: string,
  char: string | RegExp,
  position: 'front' | 'behind' = 'front'
): string[] => {
  if (!str || typeof str !== 'string') return [str, ''];
  if (typeof char === 'string') {
    try {
      new RegExp(char);
    } catch {
      return [str, ''];
    }
  }
  if (str.search(char) === -1) return [str, ''];
  if (position == 'front')
    return [str.slice(0, str.search(char)), str.slice(str.search(char))];
  else
    return [
      str.slice(0, str.search(char) + 1),
      str.slice(str.search(char) + 1),
    ];
};
/**
 * 复制文字到剪贴板
 * @param content 文字内容
 * @param fn 成功后回调
 */
export function copyToClip(content: string, fn: () => void): void {
  const aux = document.createElement('input');
  aux.setAttribute('value', content);
  document.body.appendChild(aux);
  aux.select();
  document.execCommand('copy');
  document.body.removeChild(aux);
  fn && fn();
}
/**
 * 文本文件读取
 * @param file 文件
 * @param fn 执行回调
 */
export function readTxtFile(
  file: File,
  fn?: (str: string | ArrayBuffer) => void,
  options?: { [x: string]: any }
): void {
  options = {
    encoding: 'utf-8',
    ...options,
  };
  const reader = new FileReader();
  reader.onload = function (e) {
    const str = e.target.result;
    fn && fn(str);
  };
  reader.readAsText(file, options.encoding);
}

/**
 * 图片文件转base64
 * @param img 图片
 * @param fn 执行回调
 */
export function imgToBase64(
  img: File,
  fn: (base64: string | ArrayBuffer) => void
): void {
  const reader = new FileReader();
  reader.onload = function (e) {
    const base64 = e.target.result;
    fn(base64);
  };
  reader.readAsDataURL(img);
}

/**
 * 保留两位小数
 * @param value
 * @param count
 * @returns
 */
export function toFixed(value: number | string, count = 2) {
  const dealValue = Number(value);
  if (!isNaN(dealValue)) {
    return dealValue.toFixed(count);
  } else {
    return value;
  }
}

/**
 * 计算两个数相除后的比率，返回百分数
 * @param dividend 被除数
 * @param divisor 除数
 */
export function getRatio(
  dividend: number | string,
  divisor: number | string
): string {
  const _dividend = Number(dividend);
  const _divisor = Number(divisor);
  if (isNaN(_dividend) || isNaN(_divisor) || _divisor === 0) {
    return '';
  } else {
    return `${toFixed((_dividend / _divisor) * 100)}%`;
  }
}

/**
 * 判断是否为有效值，不为null，undefined，空字符串
 */
export function isValidContent(value: any) {
  return value !== '' && value !== null && value !== undefined;
}

/**
 * 判断是否为有效数字
 */
export function isValidNumber(value: number | string) {
  return isValidContent(value) && !isNaN(Number(value));
}

/**
 * ag-grid表格 渲染数字
 * @param 单元格数据
 * @returns 固定保留两位小数，兜底"--"，负数添加负号
 */
export function agGridRenderNumber({ value }) {
  return formatMoney(value, { symbol: '' });
}

/**
 * ag-grid表格 渲染百分比
 * @param 单元格数据
 * @returns 固定保留两位小数，兜底"--"，添加百分号
 */
export function agGridRenderRate({ value }) {
  return isValidNumber(value) ? `${formatMoney(value, { symbol: '' })}%` : EMPTY_TEXT;
}

/**
 * ag-grid表格 渲染需要"--"兜底的文字
 * @param 单元格数据
 * @returns 兜底"--"
 */
export function agGridRenderNull({ value }) {
  return isValidContent(value) ? value : EMPTY_TEXT;
}

/**
 * ag-grid表格 渲染需要乘100的百分数
 * @param 单元格数据
 * @returns 兜底"--"，乘100，保留至多两位小数（1.00 → 1，1.10 → 1.1），添加百分号
 */
export function agGridRenderPercent({ value }) {
  return isValidNumber(value)
    ? `${parseFloat((value * 100).toFixed(2))}%`
    : EMPTY_TEXT;
}
