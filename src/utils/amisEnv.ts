import amisEnvInit from '@king-fisher/amis-adapter';
import { GatewaySwitcher } from './gateWaySwitcher';
import { env } from '@/utils/request';

const baseHost = {
  dev: '', //开发环境工程代理地址
  test: 'https://testcrm.5ifund.com:8443',
  pre: 'https://crm.5ifund.com:5000/pre',
  prod: 'https://crm.5ifund.com:5000/prod',
};

// 网关动态切换
export const gatewaySwitcher = new GatewaySwitcher(baseHost[env]);

// 环境变量配置
export const amisEnv = function () {
  // 初始化默认请求头
  const initHeaders = () => {
    const defaultRequestHeaders: any = {};
    if (env === 'dev') {
      defaultRequestHeaders.nginx_cert_thumbprint =
        '9FBB80C10D924DA6D76C8438AA42BDDB';
    }
    return defaultRequestHeaders;
  };
  // 自定义env配置
  const customEnvConfig = {
    statusName: 'status_code',
    statusSuccessCode: 0,
    msgName: 'status_msg',
    apiPrefix: gatewaySwitcher.getUrlPrefix(),
    defaultRequestHeaders: initHeaders(),
  };
  return amisEnvInit(customEnvConfig);
};

export default amisEnv;
