/* eslint-disable @typescript-eslint/explicit-module-boundary-types */
import dayjs from 'dayjs';
import _ from 'lodash';
import { useRouter } from 'vue-router';
// TS
import { TreeSelectProps, message } from 'ant-design-vue';
import ajax from '@/server/http';
import { dealYYYYMMDD } from './fn';
import { PageRouter } from './const';

export const changeStr = (str: string) => {
  return str.charAt(0).toUpperCase() + str.slice(1);
};

export const getAsyncComp = async (cateName, compName = 'index.vue') => {
  const result = await import(
    /* webpackChunkName: "[request]" */ `@/components/${cateName}/${compName}`
  );
  return result.default;
};

export const randomColor = (type: 'rgb' | 'hex' | 'hsl'): string => {
  switch (type) {
    case 'rgb':
      return window.crypto.getRandomValues(new Uint8Array(3)).toString();
    case 'hex':
      return (
        '#' +
        Math.floor(Math.random() * 0xffffff)
          .toString(16)
          .padStart(6, `${Math.random() * 10}`)
      );
    case 'hsl':
      // 在25-95%范围内具有饱和度，在85-95%范围内具有亮度
      return [
        360 * Math.random(),
        100 * Math.random() + '%',
        100 * Math.random() + '%',
      ].toString();
  }
};

export const copyText = (text: string) => {
  return new Promise((resolve) => {
    const copyInput = document.createElement('input'); //创建一个input框获取需要复制的文本内容
    copyInput.value = text;
    document.body.appendChild(copyInput);
    copyInput.select();
    document.execCommand('copy');
    copyInput.remove();
    resolve(true);
  });
};

export const isBase64 = (str: string): boolean => {
  if (str === '' || str.trim() === '') {
    return false;
  }
  try {
    return btoa(atob(str)) == str;
  } catch (err) {
    return false;
  }
};

// 对象转JSON
export const toJSON = (obj) => {
  return JSON.stringify(obj, (key, value) => {
    switch (true) {
      case typeof value === 'undefined':
        return 'undefined';
      case typeof value === 'symbol':
        return value.toString();
      case typeof value === 'function':
        return value.toString();
      default:
        break;
    }
    return value;
  });
};

export const formatDate = (time) => dayjs(time).format('YYYY-MM-DD');
export const formatTime = (time) => dayjs(time).format('HH:mm:ss');
export const formatDateTime = (time) =>
  dayjs(time).format('YYYY-MM-DD HH:mm:ss');
export const dateToMoment = (str) => dayjs(str, 'YYYY-MM-DD');
export const timeToMoment = (str) => dayjs(str, 'HH:mm:ss');

export const formatNumber = function (value: string) {
  value += '';
  const list = value.split('.');
  const prefix = list[0].charAt(0) === '-' ? '-' : '';
  let num = prefix ? list[0].slice(1) : list[0];
  let result = '';

  while (num.length > 3) {
    result = `,${num.slice(-3)}${result}`;
    num = num.slice(0, num.length - 3);
  }

  if (num) {
    result = num + result;
  }

  return `${prefix}${result}${list[1] ? `.${list[1]}` : ''}`;
};

export const localData = {
  get: function (name) {
    if (localStorage.getItem(name + '_obj')) {
      return JSON.parse(localStorage.getItem(name + '_obj'));
    } else if (localStorage.getItem(name + '_str')) {
      return localStorage.getItem(name + '_str');
    } else {
      return null;
    }
  },
  set: function (name, obj) {
    if (typeof obj == 'object') {
      localStorage.setItem(name + '_obj', JSON.stringify(obj));
    } else {
      localStorage.setItem(name + '_str', obj);
    }
    return true;
  },
  clean: function (name) {
    localStorage.removeItem(name + '_obj');
    localStorage.removeItem(name + '_str');
    return true;
  },
};
export const sessionData = {
  get: function (name) {
    if (sessionStorage.getItem(name + '_obj')) {
      return JSON.parse(sessionStorage.getItem(name + '_obj'));
    } else if (sessionStorage.getItem(name + '_str')) {
      return sessionStorage.getItem(name + '_str');
    } else {
      return null;
    }
  },
  set: function (name, obj) {
    if (typeof obj == 'object') {
      sessionStorage.setItem(name + '_obj', JSON.stringify(obj));
    } else {
      sessionStorage.setItem(name + '_str', obj);
    }
    return true;
  },
  clean: function (name) {
    sessionStorage.removeItem(name + '_obj');
    sessionStorage.removeItem(name + '_str');
    return true;
  },
};

export const deepCopy = function (obj) {
  return _.cloneDeep(obj);
};

export const checkAuth = function () {
  const router = useRouter();
  const currentPath = router.currentRoute.value.path;
  return function (code) {
    if (!code) {
      return true;
    } else {
      const auth = localData.get(currentPath + '_' + 'auth');
      return auth ? auth[code] : false;
    }
  };
};

// 返回带颜色的文字
export const colorStr = function (
  text: string,
  color?: string
): {
  text: string;
  color: string;
} {
  color = color || '#000';
  return { text, color };
};

// 通用业务api
/*
  创建列配置
  有问题咨询:<EMAIL>
  params:
    columnTitles -- 表格列名称
    columnKeys -- 表格列对应的key
  return:
    columns -- 表格列配置
*/
export const createColumns = (columnTitles: string[], columnKeys: string[]) => {
  const columns = [];
  columnTitles.forEach((title: string, index: number) => {
    if (columnKeys[index] === 'number') {
      columns.push({
        title,
        dataIndex: columnKeys[index],
        align: 'center',
        customRender: ({ Index }) => Index,
      });
      return;
    }
    columns.push({
      title,
      dataIndex: columnKeys[index],
      align: 'center',
      customRender: ({ text }) => {
        if (text === 0 || text) {
          return text;
        } else {
          return '——';
        }
      },
    });
  });
  return columns;
};
/*
  表格数据
  有问题咨询:<EMAIL>
  params:
    dataTemplate -- 数据模板
  return:
    dataSource -- 模拟数据
*/
export const createArtificialData = (dataTemplate: unknown) => {
  const dataSource = [];
  for (let i = 1; i <= 200; i++) {
    dataSource.push(dataTemplate);
  }
  return dataSource;
};
// 2022.5.6 跳转客户详情页
export const jumpToCustomDetail = (id?: string | number) => {
  if (id || id === 0) {
    const url = location.href.replace(
      location.hash,
      `#/customer/info?id=${id}`
    );
    window.open(url);
  } else {
    message.error('id错误');
  }
};
// 2022.5.6 跳转销售详情页
export const jumpToSaleDetail = (id?: string | number, startTime?: string, endTime?: string) => {
  if (id || id === 0) {
    const url = location.href.replace(
      location.hash,
      `#/market/detail?id=${id}${startTime ? `&startTime=${startTime}` : ''}${endTime ? `&endTime=${endTime}` : ''}`
    );
    window.open(url);
  } else {
    message.error('id错误');
  }
};

// 通用跳转函数
export const jumpToPage = (router: PageRouter, query: string) => {
  if (query) {
    const url = location.href.replace(location.hash, `#${router}${query}`);
    window.open(url);
  } else {
    message.error('id错误');
  }
}

// 2022.5.6 万元户相关
// 请求当前活动列表
export const fetchSaleActivityList = async () => {
  // 暂定单页多条请求
  return ajax(
    'fetchSaleActivity',
    {},
    {
      pageNum: 1,
      pageSize: 500,
    }
  )
    .then((res: any) => {
      if (res && res.crmRecord) {
        return res.crmRecord.map((activity) => {
          return {
            taa_id: activity.taa_id,
            activity_name: activity.activity_name,
          };
        });
      } else {
        return [];
      }
    })
    .catch((e) => {
      console.error(e);
      return [];
    });
};
// 请求用户-活动关联信息
export const fetchUserActivity = async (aiIdList: number[]) => {
  return ajax('fetchUserActivity', {
    crm_id_list: aiIdList,
  });
};
export const standardJoinText = (record: any) => {
  const { joinTime, activityName } = transStandardData(record);
  return [
    colorStr('参与'),
    colorStr(activityName, 'rgb(251, 73, 73)'),
    colorStr(' 于 '),
    colorStr(joinTime, 'rgb(78, 184, 255)'),
  ];
};
export const standardAchieveText = (record: any) => {
  const { activityAmount, achieve, achieveTime, isvalid, amount } =
    transStandardData(record);
  if (!isvalid) return [colorStr('已失效')];
  if (achieve)
    return [colorStr('已达成于'), colorStr(achieveTime, 'rgb(251, 73, 73)')];
  else
    return [
      colorStr('进行中，'),
      colorStr(amount, 'rgb(251, 73, 73)'),
      colorStr(' / '),
      colorStr(activityAmount.toString(), 'rgb(78, 184, 255)'),
    ];
};
export const transStandardData = (
  record: any
): {
  activityName?: string; // 活动名称
  activityAmount?: number | string; // 活动门槛
  joinTime?: string; // 参加时间
  achieve?: boolean; // 是否达成万元户
  achieveTime?: string; // 达成时间
  amount?: string; // 达成进度
  isvalid?: boolean; // 参与是否有效
} => {
  return {
    activityName: record.activity_name,
    activityAmount: record.amount_target,
    joinTime: record.c_time,
    achieve: !!record.saa_state,
    achieveTime: dealYYYYMMDD(record.reach_day),
    amount: record.amount_progress,
    isvalid: !!record.is_valid,
  };
};

const skyWalking = function (
  category: 'js' | 'ajax' | 'resource' | 'vue' | 'promise' | 'unknown',
  grade: 'Error' | 'Warning' | 'Info',
  info: any
) {
  const _global: any = window;
  _global.ClientMonitor.reportFrameErrors(
    {
      category: category, // 类型
      grade: grade, // Error Warning Info 级别
    },
    info
  );
};

export const systemMonitor = {
  // skywalking 监控
  error: function (info: any, category?: any) {
    skyWalking(category || 'js', 'Error', info);
  },
  warning: function (info: any, category?: any) {
    skyWalking(category || 'js', 'Warning', info);
  },
  info: function (info: any, category?: any) {
    skyWalking(category || 'js', 'Info', info);
  },
};

/** 寻找指定id的分组 */
export const getGroupInTreeData = (
  allData: TreeSelectProps['treeData'],
  groupId: number
) => {
  const saleList: number[] = [];
  const findGroup = (treeData: TreeSelectProps['treeData']) => {
    treeData.forEach((item) => {
      if (item.value === groupId) {
        collectSaleIds(item.children, saleList);
      } else if (item.children) {
        findGroup(item.children);
      }
    });
  };
  findGroup(allData);
  return saleList;
};
/** 收集分组下的所有saleId */
export const collectSaleIds = (
  treeData: TreeSelectProps['treeData'],
  saleList: number[]
) => {
  treeData.forEach((item) => {
    if (item.children) {
      collectSaleIds(item.children, saleList);
    } else {
      saleList.push(item.value);
    }
  });
};
